# Snyk (https://snyk.io) policy file, patches or ignores known vulnerabilities.
version: v1.25.0
# ignores vulnerabilities until expiry date; change duration by modifying expiry date
ignore:
  SNYK-JS-IP-6240864:
    - '*': 
        reason: >-
          Ignore for now and fix when angular migrate
        expires: 2024-12-31T00:00:00.000Z
        created: 2024-03-14T13:00:00.000Z
  SNYK-JS-SEMVER-3247795:
    - '* > semver@7.3.5': 
        reason: >-
          Ignore for now and fix when angular migrate
        expires: 2024-12-31T00:00:00.000Z
        created: 2024-03-14T13:00:00.000Z
  SNYK-JS-ANSIREGEX-1583908:
    - '* > ansi-regex@2.1.1': 
        reason: >-
          Ignore for now and fix when angular migrate
        expires: 2024-12-31T00:00:00.000Z
        created: 2024-03-14T13:00:00.000Z
  SNYK-JS-FOLLOWREDIRECTS-6141137:
    - '* > follow-redirects@1.15.3': 
        reason: >-
          Ignore for now and fix when angular migrate
        expires: 2024-12-31T00:00:00.000Z
        created: 2024-03-14T13:00:00.000Z
  SNYK-JS-WEBPACKDEVMIDDLEWARE-6476555:
    - '* > webpack-dev-middleware@5.3.0': 
        reason: >-
          Ignore for now and fix when angular migrate
        expires: 2024-12-31T00:00:00.000Z
        created: 2024-03-26T13:00:00.000Z
patch: {}