{"name": "@iris/elements-accounts-builder-component-v01-pkg", "version": "2.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:single-spa": "ng s --project elements-accounts-builder-component-v01-pkg --configuration production --port 4300", "build": "ng build elements-accounts-builder-component-v01-pkg", "build:ci": "ng build elements-accounts-builder-component-v01-pkg --configuration production", "test": "ng test --code-coverage", "test:coverage": "ng test --code-coverage --watch=false", "test:docker": "ng test --code-coverage --watch=false --karma-config=karma.conf-docker.js", "test:ci": "npm run build:ci", "lint": "ng lint", "build:single-spa:elements-accounts-builder-component-v01-pkg": "ng build elements-accounts-builder-component-v01-pkg --configuration production --deploy-url http://localhost:4200/", "serve:single-spa:elements-accounts-builder-component-v01-pkg": "ng s --project elements-accounts-builder-component-v01-pkg --disable-host-check --port 4200 --deploy-url http://localhost:4200/ --live-reload false", "e2e": "cypress open", "e2e:ci": "cypress run", "config-local": "ts-node --project ./tsconfig.scripts.json ./src/set-env.ts", "config": "ts-node --project ./tsconfig.scripts.json ./src/set-env.ts --environment=prod", "lint-fix": "ng lint --fix", "pre-commit": "lint-staged"}, "publishConfig": {"registry": "https://buildsystem-production-main-************.d.codeartifact.eu-west-2.amazonaws.com/npm/npm-group/"}, "overrides": {"lmdb": false}, "main": "dist/main.js", "dependencies": {"@angular/animations": "^18.2.7", "@angular/cdk": "^18.2.7", "@angular/common": "^18.2.7", "@angular/compiler": "^18.2.7", "@angular/core": "^18.2.7", "@angular/forms": "^18.2.7", "@angular/material": "^18.2.6", "@angular/material-moment-adapter": "^18.2.6", "@angular/platform-browser": "^18.2.7", "@angular/platform-browser-dynamic": "^18.2.7", "@angular/router": "^18.2.7", "@iris/accountsproduction-builder-report-viewer": "^0.0.486", "@iris/platform-ui-kit": "^2.0.1611", "axios": "^1.8.3", "dotenv": "^8.2.0", "eev": "^0.1.5", "form-data": "^4.0.4", "jquery-ui": "^1.14.0", "rxjs": "~6.5.4", "single-spa": "^6.0.3", "single-spa-angular": "^9.2.0", "style-loader": "^3.3.1", "tslib": "^2.0.0", "uuid": "^8.3.2", "yargs": "^15.4.1", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-builders/custom-webpack": "^18.0.0", "@angular-devkit/build-angular": "^18.2.7", "@angular/cli": "^18.2.7", "@angular/compiler-cli": "^18.2.7", "@angular/language-service": "^18.2.7", "@iris/platform-ui-core-utility-pkg": "^1.0.53", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^16.18.111", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "husky": "^8.0.0", "jasmine-core": "^4.2.0", "jasmine-spec-reporter": "~7.0.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "^5.0.1", "karma-jasmine-html-reporter": "^2.1.0", "karma-mocha-reporter": "^2.2.5", "karma-spec-reporter": "^0.0.32", "lint-staged": "^12.3.8", "prettier": "^2.8.1", "protractor": "~7.0.0", "ts-node": "^10.9.1", "typescript": "~5.4.0"}, "lint-staged": {"*.{ts,js,scss,css,md}": "prettier --write"}}