{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"elements-accounts-builder-component-v01-pkg": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "elements-accounts-builder-component-v01-pkg", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "extra-webpack.config.js"}, "optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": false}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}, {"replace": "src/main.ts", "with": "src/main.single-spa.ts"}], "optimization": true, "outputHashing": "none", "sourceMap": false, "namedChunks": false, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": true}}}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "options": {"browserTarget": "elements-accounts-builder-component-v01-pkg:build"}, "configurations": {"production": {"browserTarget": "elements-accounts-builder-component-v01-pkg:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "elements-accounts-builder-component-v01-pkg:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}