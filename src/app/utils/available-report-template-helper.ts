import { BusinessSubTypes, BusinessTypes } from '../models/client.model';
import { ReportingStandard, ReportingStandardVersion, ReportingStandards } from '../models/report.model';

export const availableReportTemplates: ReportingStandard[] = [
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTD105FUL001',
    display: true,
    name: 'FRS105 - Full',
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LTD105SFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTD105SFC001',
    display: true,
    name: 'FRS105 - Filleted',
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'LTD105FUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTDS1AFUL001',
    display: true,
    name: 'FRS102 1A - Full',
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LTDS1ASFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTDS1ASFC001',
    display: true,
    name: 'FRS102 1A - Filleted',
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'LTDS1AFUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.SoleTrader, BusinessTypes.Partnership],
    compatibleCompanySubTypes: null,
    correlationId: 'STPUNIALL001',
    display: true,
    name: 'Unincorporated',
    reportingStandard: ReportingStandards.Unincorporated,
    reportType: null,
    equivalentCorrelationId: null
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.SoleTrader, BusinessTypes.Partnership],
    compatibleCompanySubTypes: null,
    correlationId: 'STPUNIINE001',
    display: true,
    name: 'Unincorporated - I&E',
    reportingStandard: ReportingStandards.Unincorporated,
    reportType: null,
    equivalentCorrelationId: null
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.LLP],
    compatibleCompanySubTypes: null,
    correlationId: 'LLP105FUL001',
    display: true,
    name: 'FRS105 - Full LLP',
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LLP105SFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.LLP],
    compatibleCompanySubTypes: null,
    correlationId: 'LLP105SFC001',
    display: true,
    name: 'FRS105 - Filleted LLP',
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'LLP105FUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.LLP],
    compatibleCompanySubTypes: null,
    correlationId: 'LLPS1AFUL001',
    display: true,
    name: 'FRS102 1A - Full LLP',
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LLPS1ASFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.LLP],
    compatibleCompanySubTypes: null,
    correlationId: 'LLPS1ASFC001',
    display: true,
    name: 'FRS102 1A - Filleted LLP',
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'LLPS1AFUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany, BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CIC105FUL001',
    display: true,
    name: 'FRS105 - Full CIC',
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'CIC105SFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany, BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CIC105SFC001',
    display: true,
    name: 'FRS105 - Filleted CIC',
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'CIC105FUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany, BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CICS1AFUL001',
    display: true,
    name: 'FRS102 1A - Full CIC',
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'CICS1ASFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany, BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CICS1ASFC001',
    display: true,
    name: 'FRS102 1A - Filleted CIC',
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'CICS1AFUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTD102FUL001',
    display: true,
    name: 'FRS102 - Full HMRC',
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LTD102SFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTD102SFC001',
    display: true,
    name: 'FRS102 - Full CH',
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LTD102FUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.LLP],
    compatibleCompanySubTypes: null,
    correlationId: 'LLP102FUL001',
    display: true,
    name: 'FRS102 - Full LLP',
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LLP102SFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.LLP],
    compatibleCompanySubTypes: null,
    correlationId: 'LLP102SFC001',
    display: true,
    name: 'FRS102 - Full LLP CH copy',
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LLP102FUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany, BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CIC102FUL001',
    display: true,
    name: 'FRS102 - Full CIC',
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'CIC102SFC001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany, BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CIC102SFC001',
    display: true,
    name: 'FRS102 - Full CIC CH copy',
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'CIC102FUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.IncorporatedCharity, BusinessTypes.UnincorporatedCharity],
    compatibleCompanySubTypes: null,
    correlationId: 'CHACHAFUL001',
    display: true,
    name: 'Charity - Full',
    reportingStandard: ReportingStandards.Charity,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'CHACHAFUL001'
  },
  {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.IncorporatedCharity],
    compatibleCompanySubTypes: null,
    correlationId: 'CHACHASFC001',
    display: true,
    name: 'Charity - Co Hse copy',
    reportingStandard: ReportingStandards.Charity,
    reportType: ReportingStandardVersion.Filleted,
    equivalentCorrelationId: 'CHACHASFC001'
  },
   {
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTDIASFUL001',
    display: true,
    name: 'IFRS - Full HMRC',
    reportingStandard: ReportingStandards.IFRS,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LTDIASSFC001'
  },
{
    id: null,
    description: null,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTDIASSFC001',
    display: true,
    name: 'IFRS - Full Co Hse',
    reportingStandard: ReportingStandards.IFRS,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: 'LTDIASFUL001'
  }
];
