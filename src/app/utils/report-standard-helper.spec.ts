import { TestBed } from '@angular/core/testing';
import { ReportingStandards } from '../models/report.model';
import { reportNoteSections, reportStandardHelper } from './report-standard-helper';

describe('reportStandardHelper', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should map report note section properly for frs105', () => {
    expect(reportStandardHelper.getReportNoteSection(ReportingStandards.FRS105)).toEqual(
      reportNoteSections.find(c => c.reportingStandard === ReportingStandards.FRS105)
    );
  });

  it('should map report note section properly for frs102_1a', () => {
    expect(reportStandardHelper.getReportNoteSection(ReportingStandards.FRS102_1A)).toEqual(
      reportNoteSections.find(c => c.reportingStandard === ReportingStandards.FRS102_1A)
    );
  });

  it('should map report note section properly for unincorporated', () => {
    expect(reportStandardHelper.getReportNoteSection(ReportingStandards.Unincorporated)).toEqual(
      reportNoteSections.find(c => c.reportingStandard === ReportingStandards.Unincorporated)
    );
  });
});
