import { TestBed } from '@angular/core/testing';
import { availableReportTemplates } from './available-report-template-helper';
import { BusinessSubTypes } from '../models/client.model';

describe('availableReportTemplates', () => {
    beforeEach(() => {
        TestBed.configureTestingModule({});
    });

    it('should return business sub type property letting cic for cic reports', () => {
        const correlationIds = ["CIC105FUL001", "CIC105SFC001", "CICS1AFUL001", "CICS1ASFC001", "CIC102FUL001", "CIC102SFC001"];
        correlationIds.forEach(correlationId => {
            const result = availableReportTemplates.find(template => template.correlationId === correlationId);
            expect(result.compatibleCompanySubTypes.some(c => c == BusinessSubTypes.PropertyLettingCIC)).toBeTruthy();
        });
    });

    it('should not return business sub type property letting cic for non cic reports', () => {
        const correlationIds = ["LTD102SFC001", "LTDS1ASFC001"];
        correlationIds.forEach(correlationId => {
            const result = availableReportTemplates.find(template => template.correlationId === correlationId);
            expect(result.compatibleCompanySubTypes.some(c => c == BusinessSubTypes.PropertyLettingCIC)).toBeFalsy();
        });
    });
});