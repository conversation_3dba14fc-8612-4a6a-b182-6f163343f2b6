export class AppSettings {
  public static readonly FeatureFlag = {
    Reporting: {
      FormulaSandbox: 'Reporting.DPL.Formula.Sandbox',
      RawData: 'Reporting.DPL.RawData.View'
    },
    AccountsProduction: {
      Report: {
        ReportTemplates: {
          ViewAll: 'AccountProduction.Report.ReportTemplates.ViewAll'
        },
        Xbrl: {
          XbrlModeEnabled: 'AccountProduction.Report.Xbrl.Enabled'
        }
      },
      Licensing: 'AccountProduction.Licensing.IsEnabled',
      DataScreenTool: 'AccountsProduction.DataScreenTool.Enabled',
      FRS102Enabled: 'AccountProduction.AccountPeriod.FRS102Enabled',
      IFRSEnabled: 'AccountProduction.AccountPeriod.IFRSEnabled',
      IncorporatedCharities: 'AccountProduction.AccountPeriod.IncorporatedCharity',
      UnincorporatedCharities: 'AccountProduction.AccountPeriod.UnincorporatedCharity',
      DynamicIssueLogEnabled: 'AccountProduction.DynamicIssueLog.Enabled',
    }
  };

  public static readonly License = {
    Code: 'accountsprod',
    Name: 'IRIS Elements Accounts Production',
    Level: {
      Basic: 'basic',
      Premium: 'premium',
      Enterprise: 'enterprise'
    },
    Status: {
      Active: 'ACTIVE',
      FreeTrial: 'FREE_TRIAL'
    }
  };

  public static readonly XbrlEditPermission = {
    name: 'accountsprod',
    resource: {
      acountsChart: {
        name: 'accountsbuilder',
        action: {
          amendxbrl: 'amendxbrl'
        }
      }
    }
  };
}
