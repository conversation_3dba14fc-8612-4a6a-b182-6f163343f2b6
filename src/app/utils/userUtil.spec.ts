import { userUtil } from './userUtil';

describe('user util', () => {
  let store = {};
  const mockLocalStorage = {
    getItem: (key: string): string => {
      return key in store ? store[key] : null;
    },
    setItem: (key: string, value: string) => {
      store[key] = `${value}`;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
  const mockUserData = {
    username: 'user',
  };

  beforeEach(() => {
    spyOn(localStorage, 'getItem').and.callFake(mockLocalStorage.getItem);
    spyOn(localStorage, 'setItem').and.callFake(mockLocalStorage.setItem);
    spyOn(localStorage, 'removeItem').and.callFake(mockLocalStorage.removeItem);
    spyOn(localStorage, 'clear').and.callFake(mockLocalStorage.clear);

    userUtil.set(mockUserData);
  });

  it('should correctly set user details', () => {
    expect(JSON.parse(store['ias_user'])).toEqual(mockUserData);
  });

  it('should correctly get the details', () => {
    expect(userUtil.get()).toEqual(mockUserData);
  });
});
