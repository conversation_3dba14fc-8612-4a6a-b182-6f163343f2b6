import { NotesFormTypeEnum } from '../models/report-sections/report-notes.model';
import { ReportingStandards, ReportNoteSection } from '../models/report.model';

export const reportNoteSections: ReportNoteSection[] = [
  {
    reportingStandard: ReportingStandards.FRS105,
    notesToFinancialStatements: [
      NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES,
      NotesFormTypeEnum.GUARANTEES,
      NotesFormTypeEnum.OFF_BALANCE_SHEETS,
      NotesFormTypeEnum.ADVANCES,
      NotesFormTypeEnum.CTRL_PARTY_NOTE,
      NotesFormTypeEnum.MEMBERS_LIABILITY_MODAL,
      NotesFormTypeEnum.MEMBERS_LIABILITIES,
      NotesFormTypeEnum.ADDITIONAL_NOTE_1,
      NotesFormTypeEnum.ADDITIONAL_NOTE_2,
      NotesFormTypeEnum.RELATED_PARTY_TRANSACTIONS
    ],
    accountingPolicies: [],
    directorsReports: []
  },
  {
    reportingStandard: ReportingStandards.FRS102_1A,
    notesToFinancialStatements: [
      NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES,
      NotesFormTypeEnum.OPERATING_PROFIT_LOSS,
      NotesFormTypeEnum.VALUATION_IN_CURRENT_REPORTING_PERIOD,
      NotesFormTypeEnum.HISTORICAL_COST_BREAKDOWN,
      NotesFormTypeEnum.ANALYSIS_OF_COST_VALUATION,
      NotesFormTypeEnum.GUARANTEES,
      NotesFormTypeEnum.OFF_BALANCE_SHEETS,
      NotesFormTypeEnum.ADVANCES,
      NotesFormTypeEnum.ADVANCES_CREDITS,
      NotesFormTypeEnum.CTRL_PARTY_NOTE,
      NotesFormTypeEnum.MEMBERS_LIABILITY_MODAL,
      NotesFormTypeEnum.MEMBERS_LIABILITIES,
      NotesFormTypeEnum.ADDITIONAL_NOTE_1,
      NotesFormTypeEnum.ADDITIONAL_NOTE_2,
      NotesFormTypeEnum.RELATED_PARTY_TRANSACTIONS,
      NotesFormTypeEnum.INTANGIBLE_ASSETS_REVALUATION,
      NotesFormTypeEnum.LOANS_DEBTS
    ],
    accountingPolicies: [
      NotesFormTypeEnum.EXEMPTION_FINANCIAL,
      NotesFormTypeEnum.PRESENTATION_CURRENCY,
      NotesFormTypeEnum.RESEARCH_AND_DEVELOPMENT,
      NotesFormTypeEnum.FOREIGN_CURRENCIES,
      NotesFormTypeEnum.CHANGES_IN_ACC_POLICIES,
      NotesFormTypeEnum.FINANCIAL_INSTRUMENTS,
      NotesFormTypeEnum.GOVERNMENT_GRANTS,
      NotesFormTypeEnum.MEMBERS_TRANSACTIONS,
      NotesFormTypeEnum.GOODWILL_MATERIAL,
      NotesFormTypeEnum.IMPROVEMENTS_PROPERTY,
      NotesFormTypeEnum.PLANT_AND_MACHINERY,
      NotesFormTypeEnum.FIXTURE_AND_FITTINGS,
      NotesFormTypeEnum.MOTOR_VEHICLES,
      NotesFormTypeEnum.COMPUTER_EQUIPMENT,
      NotesFormTypeEnum.FREEHOLD_PROPERTY,
      NotesFormTypeEnum.SHORT_LEASEHOLD_PROPERTY,
      NotesFormTypeEnum.LONG_LEASEHOLD_PROPERTY,
      NotesFormTypeEnum.GOODWILL,
      NotesFormTypeEnum.PATENTS_AND_LICENSES,
      NotesFormTypeEnum.DEVELOPMENT_COSTS,
      NotesFormTypeEnum.COMPUTER_SOFTWARE,
      NotesFormTypeEnum.PLANT_AND_MACHINERY_RENAME,
      NotesFormTypeEnum.LAND_AND_BUILDING_RENAME
    ],
    directorsReports: []
  },
  {
    reportingStandard: ReportingStandards.Unincorporated,
    notesToFinancialStatements: [
      NotesFormTypeEnum.MEMBERS_LIABILITY_MODAL,
      NotesFormTypeEnum.MEMBERS_LIABILITIES,
      NotesFormTypeEnum.ADDITIONAL_NOTE_1,
      NotesFormTypeEnum.ADDITIONAL_NOTE_2,
      NotesFormTypeEnum.VALUATION_IN_CURRENT_REPORTING_PERIOD,
      NotesFormTypeEnum.HISTORICAL_COST_BREAKDOWN,
      NotesFormTypeEnum.ANALYSIS_OF_COST_VALUATION
    ],
    accountingPolicies: [
      NotesFormTypeEnum.IMPROVEMENTS_PROPERTY,
      NotesFormTypeEnum.PLANT_AND_MACHINERY,
      NotesFormTypeEnum.FIXTURE_AND_FITTINGS,
      NotesFormTypeEnum.MOTOR_VEHICLES,
      NotesFormTypeEnum.COMPUTER_EQUIPMENT,
      NotesFormTypeEnum.FREEHOLD_PROPERTY,
      NotesFormTypeEnum.SHORT_LEASEHOLD_PROPERTY,
      NotesFormTypeEnum.LONG_LEASEHOLD_PROPERTY
    ],
    directorsReports: []
  }
];

export const reportStandardHelper = {
  getReportNoteSection(reportingStandard: string): ReportNoteSection {
    return reportNoteSections.find(c => c.reportingStandard === reportingStandard);
  }
};
