import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { environment } from 'src/environments/environment';
import { AccountsBuilderComponent } from './components/accounts-builder/accounts-builder.component';
import { EmptyRouteComponent } from './routes/empty-route/empty-route.component';

const routes: Routes = [
  {
    path: 'accounts-production/periods/:clientId/:periodId/accounts-builder',
    component: AccountsBuilderComponent
  },
  { path: '**', component: EmptyRouteComponent }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: environment.isLocalEnv ?? false })],
  exports: [RouterModule]
})
export class AppRoutingModule {}
