import { HttpClientModule } from '@angular/common/http';
import { TestBed, ComponentFixture, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { environment } from 'src/environments/environment';
import { AppComponent } from './app.component';
import { ReportViewer2Service } from '@iris/accountsproduction-builder-report-viewer';

const reportViewerServiceMock = jasmine.createSpyObj('ReportViewer2Service', ['setEnvironment']);
describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule, HttpClientModule],
      declarations: [AppComponent],
      providers: [
        { provide: 'env', useValue: environment },
        {
          provide: ReportViewer2Service,
          useValue: reportViewerServiceMock
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should create subscription', () => {
    component.ngOnInit();
    expect(component.subscription).not.toBeNull();
  });

  it('should unsubscribe from single spa on destroy', () => {
    spyOn(component.subscription, 'unsubscribe');
    component.ngOnDestroy();

    expect(component.subscription.unsubscribe).toHaveBeenCalled();
  });
});
