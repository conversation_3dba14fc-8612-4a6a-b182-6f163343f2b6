import { BrowserModule } from '@angular/platform-browser';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { APP_BASE_HREF, CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HealthCheckComponent } from './routes/health-check/health-check.component';
import { EmptyRouteComponent } from './routes/empty-route/empty-route.component';
import { AccountsBuilderComponent } from './components/accounts-builder/accounts-builder.component';
import { ReportSidebarComponent } from './components/accounts-builder/report-sidebar/report-sidebar.component';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { DisplayReportComponent } from './components/accounts-builder/display-report/display-report.component';
import { ReportViewerModule } from '@iris/accountsproduction-builder-report-viewer';
import { environment } from 'src/environments/environment';
import { ReportSectionsComponent } from './components/accounts-builder/report-sidebar/report-sections/report-sections.component';
import { ReportSectionsJsonformsComponent } from './components/accounts-builder/report-sidebar/report-sections-jsonforms/report-sections-jsonforms.component';
import { XbrlSectionHtmlReportViewerComponent } from './components/accounts-builder/xbrl-section/xbrl-section-html-report-viewer/xbrl-section-html-report-viewer/xbrl-section-html-report-viewer.component';
import { XbrlSectionTagEditorComponent } from './components/accounts-builder/xbrl-section/xbrl-section-tag-editor/xbrl-section-tag-editor/xbrl-section-tag-editor.component';
import { SectionNotesTextareaComponent } from './components/accounts-builder/report-sidebar/report-sections/section-notes-textarea/section-notes-textarea.component';
import { AverageEmployeesFormComponent } from './components/accounts-builder/report-sidebar/report-sections/average-employees-form/average-employees-form.component';
import { ReportTypeComponent } from './components/accounts-builder/report-sidebar/report-type/report-type.component';
import { SidebarHeaderComponent } from './components/accounts-builder/report-sidebar/sidebar-header/sidebar-header.component';
import { IssueLogComponent } from './components/accounts-builder/display-report/issue-log/issue-log.component';
import { IssueDetailsComponent } from './components/accounts-builder/display-report/issue-log/issue-details/issue-details.component';
import { ReportItemComponent } from './components/accounts-builder/report-sidebar/report-sections/report-item/report-item.component';
import { IssueLogHeaderDetailsComponent } from './components/accounts-builder/display-report/issue-log/issue-log-header-details/issue-log-header-details.component';
import { DynamicIssueLogComponent } from './components/accounts-builder/display-report/dynamic-issuelog/dynamic-issuelog.component';
import { DynamicIssueDetailsComponent } from './components/accounts-builder/display-report/dynamic-issuelog/dynamic-issue-details/dynamic-issue-details.component';
import { DynamicIssueLogHeaderDetailsComponent } from './components/accounts-builder/display-report/dynamic-issuelog/dynamic-issuelog-header-details/dynamic-issuelog-header-details.component';
import { TitledParagraphNoteComponent } from './components/accounts-builder/report-sidebar/report-sections/titled-paragraph-note/titled-paragraph-note.component';
import { ExemptionFormComponent } from './components/accounts-builder/report-sidebar/report-sections/exemption-form/exemption-form.component';
import { CheckboxFormComponent } from './components/accounts-builder/report-sidebar/report-sections/checkbox-form/checkbox-form.component';
import { BalanceSheetFormComponent } from './components/accounts-builder/report-sidebar/report-sections/balance-sheet-form/balance-sheet-form.component';
import { OperatingProfitLossNoteComponent } from './components/accounts-builder/report-sidebar/report-sections/operating-profit-loss-note/operating-profit-loss-note.component';
import { OperatingProfitLossNoteItemComponent } from './components/accounts-builder/report-sidebar/report-sections/operating-profit-loss-note/operating-profit-loss-note-item/operating-profit-loss-note-item.component';
import { AssetsBasisFormComponent } from './components/accounts-builder/report-sidebar/report-sections/assets-basis-form/assets-basis-form.component';
import { ValuationCurrentReportingPeriod } from './components/accounts-builder/report-sidebar/report-sections/valuation-current-reporting-period-form/valuation-current-reporting-period.component';
import { HistoricCostBreakdown } from './components/accounts-builder/report-sidebar/report-sections/historical-cost-breakdown/historical-cost-breakdown.component';
import { DirectorEditComponent } from './components/accounts-builder/report-sidebar/report-sections/report-item/director-edit/director-edit.component';
import { CommaDelimiterPipe } from './pipes/comma-delimiter.pipe';
import { SectionRenameFormComponent } from './components/accounts-builder/report-sidebar/report-sections/section-rename-form/section-rename-form.component';
import { AnalysisOfCostValuationComponent } from './components/accounts-builder/report-sidebar/report-sections/analysis-of-cost-valuation/analysis-of-cost-valuation.component';
import { AnalysisOfCostValuationItemComponent } from './components/accounts-builder/report-sidebar/report-sections/analysis-of-cost-valuation/analysis-of-cost-valuation-item/analysis-of-cost-valuation-item.component';
import { MembersLiabilityModal } from './components/accounts-builder/report-sidebar/report-sections/members-liability-modal/members-liability-modal.component';
import { ParcelModule } from 'single-spa-angular/parcel';

@NgModule({
  declarations: [
    AppComponent,
    HealthCheckComponent,
    EmptyRouteComponent,
    AccountsBuilderComponent,
    ReportSidebarComponent,
    DisplayReportComponent,
    ReportSectionsComponent,
    ReportSectionsJsonformsComponent,
    XbrlSectionTagEditorComponent,
    XbrlSectionHtmlReportViewerComponent,
    ReportItemComponent,
    SectionNotesTextareaComponent,
    AverageEmployeesFormComponent,
    ReportTypeComponent,
    SidebarHeaderComponent,
    IssueLogComponent,
    IssueDetailsComponent,
    IssueLogHeaderDetailsComponent,
    DynamicIssueLogComponent,
    DynamicIssueDetailsComponent,
    DynamicIssueLogHeaderDetailsComponent,
    TitledParagraphNoteComponent,
    ExemptionFormComponent,
    CheckboxFormComponent,
    BalanceSheetFormComponent,
    OperatingProfitLossNoteComponent,
    OperatingProfitLossNoteItemComponent,
    AssetsBasisFormComponent,
    ValuationCurrentReportingPeriod,
    HistoricCostBreakdown,
    AnalysisOfCostValuationComponent,
    DirectorEditComponent,
    CommaDelimiterPipe,
    SectionRenameFormComponent,
    AnalysisOfCostValuationItemComponent,
    MembersLiabilityModal
  ],
  imports: [
    BrowserModule,
    CommonModule,
    BrowserAnimationsModule,
    HttpClientModule,
    AppRoutingModule,
    MatExpansionModule,
    ReportViewerModule.forRoot(environment),
    ParcelModule
  ],
  providers: [
    { provide: APP_BASE_HREF, useValue: '/' },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule {}
