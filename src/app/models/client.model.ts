export interface ClientDetails {
  id: string;
  accountsChartId?: number;
  accountsChartIdentifier?: string;
  groupStructureId?: number;
  groupStructureCode?: number;
  clientId?: string;
  name?: string;
  tradingName?: string;
  businessType?: BusinessTypes;
  businessSubType?: BusinessSubTypes;
  businessTypeDisplayName?: string;
  limitedCompanyType?: string;
  incorporated?: string;
  tradingCommenced?: string;
  tradingCeased?: string;
  registeredNo?: string;
  countryRegIn?: string;
  companyAuthenticationCode?: string;
}

export interface ClientDetailsReport {
  businessType: BusinessTypes;
  businessTypeName: string;
  businessSubType: BusinessSubTypes;
  countryRegIn: string;
  clientUUID: string;
  limitedCompanyType: string;
}

export enum BusinessTypes {
  NotApplicable = 'NotApplicable',
  Limited = 'Limited',
  Partnership = 'Partnership',
  SoleTrader = 'SoleTrader',
  Unknown = 'Unknown',
  LLP = 'LLP',
  Group = 'Group',
  Trust = 'Trust',
  IncorporatedCharity = 'IncorporatedCharity',
  UnincorporatedCharity = 'UnincorporatedCharity',
  ConsolidatedIncorporatedCharity = 'ConsolidatedIncorporatedCharity',
  ConsolidatedUnincorporatedCharity = 'ConsolidatedUnincorporatedCharity'
}

export enum LimitedCompanyTypes {
  LimitedByGuarantee = 'LimitedByGuarantee',
  LimitedByGuaranteeS30Exempt = 'LimitedByGuaranteeS30Exempt'
}

export interface ProcessedClientInvolvement {
  directors: ClientInvolvement[];
  secretaries: ClientInvolvement[];
  partners: ClientInvolvement[];
  proprietors: ClientInvolvement[];
}

export interface ClientInvolvement {
  id: number;
  involvedClientGuid: string;
  clientName: string;
  involvementType: string;
  from: string;
  to: string;
}

export interface ClientInvolvementResponse {
  id: number;
  involvedClientGuid: string;
  involvedClientType: string;
  involvedClientName: string;
  involvementType: string;
  startDate: string;
  endDate: string;
}

export enum InvolvementTypeEnum {
  Director = 'director',
  Partner = 'proprietor/partner',
  Secretary = 'secretary'
}

export interface ProfitShare {
  id: number;
  accountPeriodId: number;
  totalProfitLossShare: number;
  clientId: string;
  tenantId: string;
  partnerPeriods: [
    {
      id: number;
      startDateUtc: string;
      endDateUtc: string;
      periodProfitLossShare: number;
      isPercentageSelected: boolean;
      shareAllocations: [
        {
          id: number;
          sharePercentage: number;
          shareAmount: number;
          involvementId: number;
        }
      ];
    }
  ];
}

export enum BusinessSubTypes {
  None = 'None',
  TradeProfessionVocation = 'TradeProfessionVocation',
  RentalIncome = 'RentalIncome',
  PropertyLettingCompany = 'PropertyLettingCompany',
  CommunityInterestCompany = 'CommunityInterestCompany',
  PropertyLettingCIC = 'PropertyLettingCIC',
  Academy = 'Academy',
  Undefined = ''
}
