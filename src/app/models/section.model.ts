export enum SubsectionEditType {
  PREVIEW = 'preview',
  NOTE = 'note'
}

export interface EditableSection {
  name: string;
  editType: SubsectionEditType;
}

export const editableSectionList: EditableSection[] = [
  {
    name: 'Average number of employees',
    editType: SubsectionEditType.PREVIEW
  },
  {
    name: 'Guarantees and other financial commitments',
    editType: SubsectionEditType.NOTE
  },
  {
    name: 'Off-balance sheet arrangements',
    editType: SubsectionEditType.NOTE
  },
  {
    name: 'Advances, credits and guarantees granted to Directors',
    editType: SubsectionEditType.NOTE
  }
];
