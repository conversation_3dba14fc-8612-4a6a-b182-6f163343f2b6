export interface IssueLogHeaderDetailsModel {
  type: string;
  iconColour: string;
  backgroundColor: string;
  message: string;
  styleClass: string;
}

export interface IssueLogTab {
  title: string;
  isDisabled: boolean;
  isHidden: boolean;
  issueType: string;
}

export const ISSUELOG_TABS: IssueLogTab[] = [
  {
    isDisabled: false,
    isHidden: false,
    title: 'Data screens',
    issueType : 'accounts builder'
  },
  {
    isDisabled: false,
    isHidden: false,
    title: 'Accounts production',
    issueType : 'accounts production'
  },
  {
    isDisabled: false,
    isHidden: false,
    title: 'Client management',
    issueType : 'client management'
  }
];

export const DYNAMIC_ISSUELOG_TABS: IssueLogTab[] = [
  {
    isDisabled: false,
    isHidden: false,
    title: 'Data screens',
    issueType : 'DataScreen'
  },
  {
    isDisabled: false,
    isHidden: false,
    title: 'Accounts production',
    issueType : 'Accounts'
  },
  {
    isDisabled: false,
    isHidden: false,
    title: 'Client management',
    issueType : 'Client'
  }
];
