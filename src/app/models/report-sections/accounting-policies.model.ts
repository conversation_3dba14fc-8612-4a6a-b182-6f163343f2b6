import { DataSourceServiceEnum, NotesFormTypeEnum, ValidationIssueEnum } from './report-notes.model';
import { ReportSection } from './report-sections.model';

export const balanceSheetSection: ReportSection = {
  label: 'Balance sheet',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  formConfigKey: NotesFormTypeEnum.GOODWILL_MATERIAL,
  dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
  validationIssueCode: ValidationIssueEnum.BALANCE_SHEET_WARN
};

export const tangibleAssetsSubsections: ReportSection[] = [
  {
    label: 'Plant and machinery etc.',
    parent: null,
    children: null,
    isMandatory: true,
    errorCount: 0,
    warningCount: 0,
    validationIssueCode: ValidationIssueEnum.PLANT_AND_MACHINERY_WARN,
    formConfigKey: NotesFormTypeEnum.PLANT_AND_MACHINERY_RENAME,
    skipCountUpdate: true
  },
  {
    label: 'Land and buildings',
    parent: null,
    children: null,
    isMandatory: true,
    errorCount: 0,
    warningCount: 0,
    validationIssueCode: ValidationIssueEnum.LAND_AND_BUILDING_WARN,
    formConfigKey: NotesFormTypeEnum.LAND_AND_BUILDING_RENAME,
    skipCountUpdate: true
  }
];

export const intangibleAssetsSubsections: ReportSection[] = [
  {
    label: 'Goodwill',
    parent: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.GOODWILL,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.GOODWILL_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    label: 'Patents & licences',
    parent: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.PATENTS_AND_LICENSES,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.PATENTS_AND_LICENSES_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    label: 'Development costs',
    parent: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.DEVELOPMENT_COSTS,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.DEVELOPMENT_COSTS_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    label: 'Computer software',
    parent: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.COMPUTER_SOFTWARE,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.COMPUTER_SOFTWARE_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  }
];

export const plantAndMachinerySubsections: ReportSection[] = [
  {
    parent: null,
    label: 'Improvements to property',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.IMPROVEMENTS_PROPERTY,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.IMPROVEMENTS_PROPERTY_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Plant and machinery',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.PLANT_AND_MACHINERY,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.PLANT_AND_MACHINERY_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Fixtures & fittings',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.FIXTURE_AND_FITTINGS,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.FIXTURES_AND_FITTINGS_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Motor vehicles',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.MOTOR_VEHICLES,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.MOTOR_VEHICLES_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Computer equipment',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.COMPUTER_EQUIPMENT,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.COMPUTER_EQUIPMENT_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  }
];

export const landAndBuildingsSubsections: ReportSection[] = [
  {
    parent: null,
    label: 'Freehold property',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.FREEHOLD_PROPERTY,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.FREEHOLD_PROPERTY_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Short leasehold property',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.SHORT_LEASEHOLD_PROPERTY,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.SHORT_LEASEHOLD_PROPERTY_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Long leasehold property',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.LONG_LEASEHOLD_PROPERTY,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    validationIssueCode: ValidationIssueEnum.LONG_LEASEHOLD_PROPERTY_ERROR,
    children: null,
    errorCount: 0,
    warningCount: 0
  }
];

export const accountingPoliciesSection: ReportSection = {
  label: 'Accounting policies',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0
};

export const accountingPoliciesSubsections: ReportSection[] = [
  {
    parent: null,
    label: 'Presentation currency',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.PRESENTATION_CURRENCY,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Exemption from preparation of consolidated financial statements',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.EXEMPTION_FINANCIAL,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Changes in accounting policies',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.CHANGES_IN_ACC_POLICIES,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Intangible assets',
    isMandatory: true,
    children: null,
    validationIssueCode: ValidationIssueEnum.INTANGIBLE_ASSETS_WARN,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Tangible fixed assets',
    isMandatory: true,
    children: null,
    validationIssueCode: ValidationIssueEnum.TANGIBLE_ASSETS_WARN,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Financial instruments',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.FINANCIAL_INSTRUMENTS,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Government grants',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.GOVERNMENT_GRANTS,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Research and development',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.RESEARCH_AND_DEVELOPMENT,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Foreign currencies',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.FOREIGN_CURRENCIES,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Members’ transactions with the LLP',
    isMandatory: true,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.MEMBERS_TRANSACTIONS,
    dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES,
    children: null,
    errorCount: 0,
    warningCount: 0
  }
];
