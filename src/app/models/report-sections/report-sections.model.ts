import { Bookmark } from '../bookmark.model';
import { DataSourceServiceEnum, NotesFormTypeEnum, ValidationIssueEnum } from './report-notes.model';

const avgNumberOfEmployees = {
  parent: null,
  label: 'Average number of employees',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const operatingProfit = {
  parent: null,
  label: 'Operating profit/(loss)',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.OPERATING_PROFIT_LOSS,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const intangibleAssetsRevaluation = {
  parent: null,
  label: 'Intangible assets - Revaluations',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.INTANGIBLE_ASSETS_REVALUATION,
  dataSourceService: DataSourceServiceEnum.NOTES,
  validationIssueCode: ValidationIssueEnum.INTANGIBLE_ASSETS_REVALUATION_WARN,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const tangibleFixedAssets = {
  parent: null,
  label: 'Tangible fixed assets',
  isMandatory: false,
  hasData: false,
  dataSourceService: DataSourceServiceEnum.NOTES,
  validationIssueCode: ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_NOTES_WARN,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const relatedPartyTransactions = {
  parent: null,
  label: 'Related party transactions',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.RELATED_PARTY_TRANSACTIONS,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const guaranteesAndOtherFinancialCommitments = {
  parent: null,
  label: 'Guarantees and other financial commitments',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.GUARANTEES,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const offBalanceSheetArrangements = {
  parent: null,
  label: 'Off-balance sheet arrangements',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.OFF_BALANCE_SHEETS,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const advancesCreditAndGuaranteesGrantedToDirectors = {
  parent: null,
  label: 'Advances, credits and guarantees granted to Directors',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.ADVANCES,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const membersLiability = {
  parent: null,
  label: 'Members’ liability',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.MEMBERS_LIABILITIES,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const additionalNote1 = {
  parent: null,
  label: 'Additional note 1',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.ADDITIONAL_NOTE_1,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const additionalNote2 = {
  parent: null,
  label: 'Additional note 2',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.ADDITIONAL_NOTE_2,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const controllingPartyNote = {
  parent: null,
  label: 'Controlling party',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.CTRL_PARTY_NOTE,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const loansAndOtherDebts = {
  parent: null,
  label: 'Loans and other debts due to members',
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.LOANS_DEBTS,
  dataSourceService: DataSourceServiceEnum.NOTES,
  children: null,
  errorCount: 0,
  warningCount: 0
};

export interface ReportSection {
  parent: ReportSection | null;
  label: string;
  navigationRef?: Bookmark;
  description?: string;
  isMandatory: boolean;
  hasData?: boolean;
  errorCount: number;
  validationMessage?: {
    mandatoryMessage?: string;
    optionalMessage?: string;
  };
  warningCount: number;
  skipCountUpdate?: boolean;
  formConfigKey?: string;
  director?: {
    index: number;
    involvementClientGuid: string;
    directorName: string;
  };
  dataSourceService?: number;
  validationIssueCode?: ValidationIssueEnum;
  children: ReportSection[] | null;
  alias?: string[];
  visibile?: boolean;
}

export const directorsReportSection: ReportSection = {
  label: 'Director’s report',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  alias: ['Directors’ report']
};

export const customizableReportSection: ReportSection = {
  label: 'Notes to the financial statements',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0
};

export const customizableReportSubsectionsFrs1021a: ReportSection[] = [
  avgNumberOfEmployees,
  operatingProfit,
  intangibleAssetsRevaluation,
  tangibleFixedAssets,
  loansAndOtherDebts,
  relatedPartyTransactions,
  guaranteesAndOtherFinancialCommitments,
  offBalanceSheetArrangements,
  advancesCreditAndGuaranteesGrantedToDirectors,
  membersLiability,
  additionalNote1,
  additionalNote2,
  controllingPartyNote
];

export const customizableReportSubsectionsFrs105: ReportSection[] = [
  offBalanceSheetArrangements,
  avgNumberOfEmployees,
  operatingProfit,
  intangibleAssetsRevaluation,
  tangibleFixedAssets,
  guaranteesAndOtherFinancialCommitments,
  relatedPartyTransactions,
  advancesCreditAndGuaranteesGrantedToDirectors,
  membersLiability,
  additionalNote1,
  additionalNote2,
  controllingPartyNote
];

export const tangibleFixedAssetsSection: ReportSection[] = [
  {
    parent: null,
    label: 'Analysis of cost or valuation',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.ANALYSIS_OF_COST_VALUATION,
    dataSourceService: DataSourceServiceEnum.NOTES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Valuation in current reporting period',
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.VALUATION_IN_CURRENT_REPORTING_PERIOD,
    dataSourceService: DataSourceServiceEnum.NOTES,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: null,
    label: 'Historical cost breakdown',
    isMandatory: false,
    hasData: false,
    validationMessage: {
      optionalMessage:
        'Complete the text and provide figures in respect of tangible fixed assets that have been revalued for inclusion in the notes to the financial statements.'
    },
    formConfigKey: NotesFormTypeEnum.HISTORICAL_COST_BREAKDOWN,
    dataSourceService: DataSourceServiceEnum.NOTES,
    children: null,
    errorCount: 0,
    warningCount: 0
  }
];
