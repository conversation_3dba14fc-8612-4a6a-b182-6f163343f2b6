export interface ReportNotesData {
  previousPeriodId: string;
  averageNumberOfEmployees: AvgNumberOfEmployeesNote;
  offBalanceSheetArrangements: string;
  advancesCreditAndGuaranteesGrantedToDirectorsExtended: AdvancesCreditAndGuaranteesGrantedToDirectorsExtended;
  advancesCreditAndGuaranteesGrantedToDirectors: string;
  guaranteesAndOtherFinancialCommitments: string;
  membersLiabilityModel?: MembersLiabilityModal;
  membersLiabilityText: string;
  additionalNote1: AdditionalNote;
  additionalNote2: AdditionalNote;
  controllingPartyNote: string;
  relatedPartyTransactions: string;
  operatingProfitLoss: OperatingProfitLossNote;
  intangibleAssetsRevaluation: string;
  tangibleFixedAssetsNotes: TangibleFixedAssetsNotes;
}

export interface TangibleFixedAssetsNotes {
  valuationInCurrentReportingPeriod?: ValuationInCurrentReportingPeriod;
  historicalCostBreakdown?: HistoricalCostBreakdown;
  analysisOfCostOrValuation?: AnalysisOfCostOrValuation;
}

export interface HistoricalCostBreakdown {
  revaluedAssetClass: string;
  revaluedClassPronoun: string;
  currentReportingPeriodCost: number;
  currentReportingPeriodAccumulatedDepreciation: number;
}

export interface AnalysisOfCostOrValuationItem {
  index: number;
  year: number;
  landAndBuildings: number;
  plantAndMachineryEtc: number;
}
export interface MembersLiabilityModal {
  noteTitle: string;
  noteText: string;
}

export interface AnalysisOfCostOrValuation {
  analysisOfCostOrValuationItems: AnalysisOfCostOrValuationItem[];
  costLandAndBuildings: number;
  costPlantAndMachineryEtc: number;
  totalLandAndBuildings: number;
  totalPlantAndMachineryEtc: number;
}

export interface ValuationInCurrentReportingPeriod {
  valuationDetails: string;
  independentValuerInvolved: boolean;
  revaluationBasis: string;
  dateOfRevaluation: string;
}

export interface OperatingProfitLossNote {
  isEnabled: boolean;
  items: OperatingProfitLossNoteItem[];
}
export interface OperatingProfitLossNoteItem {
  description: string;
  value: number;
  index: number;
}

export interface AdvancesCreditAndGuaranteesGrantedToDirectorsExtended {
  guarantees: string;
  items: AdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem[];
}

export interface AdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem {
  index: number;
  involvementClientGuid: string;
  directorName: string;
  balanceOutstandingAtStartOfYear: number;
  amountsAdvanced: number;
  amountsRepaid: number;
  amountsWrittenOff: number;
  amountsWaived: number;
  balanceOutstandingAtEndOfYear: number;
  advanceCreditConditions: string;
}

export interface AdditionalNote {
  noteTitle: string;
  noteText: string;
}

export interface AvgNumberOfEmployeesNote {
  currentPeriod?: number;
  previousPeriod?: number;
}

export interface AccountingPoliciesData {
  clientId: string;
  previousPeriodId: string;
  exemptionsFinancialStatements: ExemptionsFinancialStatements;
  researchAndDevelopment: boolean;
  foreignCurrencies: boolean;
  presentationCurrency: boolean;
  changesInAccountingPolicies: string;
  financialInstrumentsAccountingPolicy: string;
  governmentGrantsAccountingPolicy: string;
  goodwillMaterial: boolean;
  membersTransactionsWithTheLlpText: string;
  tangibleFixedAssets: {
    plantAndMachinery: {
      classNameCustomization: string;
      improvementsToProperty: AssetsBasis;
      plantAndMachinery: AssetsBasis;
      fixturesAndFittings: AssetsBasis;
      motorVehicles: AssetsBasis;
      computerEquipment: AssetsBasis;
    };
    landAndBuildings: {
      classNameCustomization: string;
      freeholdProperty: AssetsBasis;
      shortLeaseholdProperty: AssetsBasis;
      longLeaseholdProperty: AssetsBasis;
    };
  };
  intangibleAssets: {
    goodwill: AssetsBasis;
    patentsAndLicenses: AssetsBasis;
    developmentCosts: AssetsBasis;
    computerSoftware: AssetsBasis;
  };
}

export interface AssetsBasis {
  categoryDescription: string;
  reducingBalanceBasis: number;
  straightLineBasis: number;
  alternativeBasis: string;
}

export interface AccountingPoliciesResponse {
  body: AccountingPoliciesData;
  status: number;
}

export interface ExemptionsFinancialStatements {
  section: ExemptionTypeEnum;
  parentName: string;
  parentAddress: string;
}

export enum ExemptionTypeEnum {
  NO_EXEMPTION = 0,
  SECTION_399_2A,
  SECTION_400,
  SECTION_401
}
export enum NotesFormTypeEnum {
  AVG_NUMBER_OF_EMPLOYEES = 'averageNumberOfEmployees',
  INTANGIBLE_ASSETS_REVALUATION = 'intangibleAssetsRevaluation',
  VALUATION_IN_CURRENT_REPORTING_PERIOD = 'valuationInCurrentReportingPeriod',
  HISTORICAL_COST_BREAKDOWN = 'historicalCostBreakdown',
  ANALYSIS_OF_COST_VALUATION = 'analysisOfCostValuation',
  TANGIBLE_FIXED_ASSETS_NOTES = 'tangibleFixedAssetsNotes',
  OPERATING_PROFIT_LOSS = 'operatingProfitLoss',
  GUARANTEES = 'guaranteesAndOtherFinancialCommitments',
  OFF_BALANCE_SHEETS = 'offBalanceSheetArrangements',
  ADVANCES = 'advancesCreditAndGuaranteesGrantedToDirectors',
  ADVANCES_CREDITS = 'advancesCreditAndGuaranteesGrantedToDirectorsExtended',
  CTRL_PARTY_NOTE = 'controllingPartyNote',
  LOANS_DEBTS = 'loansAndOtherDebtsDueToMembers',
  MEMBERS_LIABILITY_MODAL = 'membersLiabilityModel',
  MEMBERS_LIABILITIES = 'membersLiabilityText',
  MEMBERS_TRANSACTIONS = 'membersTransactionsWithTheLlpText',
  ADDITIONAL_NOTE_1 = 'additionalNote1',
  ADDITIONAL_NOTE_2 = 'additionalNote2',
  RELATED_PARTY_TRANSACTIONS = 'relatedPartyTransactions',
  EXEMPTION_FINANCIAL = 'exemptionsFinancialStatements',
  PRESENTATION_CURRENCY = 'presentationCurrency',
  RESEARCH_AND_DEVELOPMENT = 'researchAndDevelopment',
  FOREIGN_CURRENCIES = 'foreignCurrencies',
  CHANGES_IN_ACC_POLICIES = 'changesInAccountingPolicies',
  FINANCIAL_INSTRUMENTS = 'financialInstrumentsAccountingPolicy',
  GOVERNMENT_GRANTS = 'governmentGrantsAccountingPolicy',
  GOODWILL_MATERIAL = 'goodwillMaterial',
  IMPROVEMENTS_PROPERTY = 'tangibleFixedAssets/plantAndMachinery/improvementsToProperty',
  PLANT_AND_MACHINERY = 'tangibleFixedAssets/plantAndMachinery/plantAndMachinery',
  FIXTURE_AND_FITTINGS = 'tangibleFixedAssets/plantAndMachinery/fixturesAndFittings',
  MOTOR_VEHICLES = 'tangibleFixedAssets/plantAndMachinery/motorVehicles',
  COMPUTER_EQUIPMENT = 'tangibleFixedAssets/plantAndMachinery/computerEquipment',
  FREEHOLD_PROPERTY = 'tangibleFixedAssets/landAndBuildings/freeholdProperty',
  SHORT_LEASEHOLD_PROPERTY = 'tangibleFixedAssets/landAndBuildings/shortLeaseholdProperty',
  LONG_LEASEHOLD_PROPERTY = 'tangibleFixedAssets/landAndBuildings/longLeaseholdProperty',
  GOODWILL = 'intangibleAssets/goodwill',
  PATENTS_AND_LICENSES = 'intangibleAssets/patentsAndLicenses',
  DEVELOPMENT_COSTS = 'intangibleAssets/developmentCosts',
  COMPUTER_SOFTWARE = 'intangibleAssets/computerSoftware',
  PLANT_AND_MACHINERY_RENAME = 'tangibleFixedAssets/plantAndMachinery/classNameCustomization',
  LAND_AND_BUILDING_RENAME = 'tangibleFixedAssets/landAndBuildings/classNameCustomization'
}

export enum DataSourceServiceEnum {
  NOTES = 1,
  ACCOUNTING_POLICIES,
  DIRECTORS_REPORT
}

export enum ValidationIssueEnum {
  INTANGIBLE_ASSETS_REVALUATION_WARN = 'WRN2050001',
  TANGIBLE_FIXED_ASSETS_NOTES_WARN = 'WRN2050002',
  BALANCE_SHEET_WARN = 'WRN2040001',
  INTANGIBLE_ASSETS_WARN = 'WRN2030101',
  TANGIBLE_ASSETS_WARN = 'WRN2030201',
  LAND_AND_BUILDING_WARN = 'WRN2030202',
  PLANT_AND_MACHINERY_WARN = 'WRN2030206',
  IMPROVEMENTS_PROPERTY_ERROR = 'ERR1030207',
  PLANT_AND_MACHINERY_ERROR = 'ERR1030208',
  FIXTURES_AND_FITTINGS_ERROR = 'ERR1030209',
  MOTOR_VEHICLES_ERROR = 'ERR1030210',
  COMPUTER_EQUIPMENT_ERROR = 'ERR1030211',
  GOODWILL_ERROR = 'ERR1030102',
  PATENTS_AND_LICENSES_ERROR = 'ERR1030103',
  DEVELOPMENT_COSTS_ERROR = 'ERR1030104',
  COMPUTER_SOFTWARE_ERROR = 'ERR1030105',
  TANGIBLE_FIXED_ASSETS_ANALYSIS_OF_COST_ERROR = 'ERR1030106',
  TANGIBLE_FIXED_ASSETS_HISTORICAL_COST_BREAKDOWN_ERROR = 'ERR1030107',
  FREEHOLD_PROPERTY_ERROR = 'ERR1030203',
  SHORT_LEASEHOLD_PROPERTY_ERROR = 'ERR1030204',
  LONG_LEASEHOLD_PROPERTY_ERROR = 'ERR1030205',
  ADVANCES_CREDIT_AND_GUARANTEES_TO_DIRECTORS_ERROR = 'ERR1020203'
}

export enum ValidationTypeEnum {
  ADVANCES = 'AdvancesCreditGuarantees'
}

export enum DisplayedFormEnum {
  AVERAGE_EMPLOYEES = 1,
  PARAGRAPH,
  TITLED_PARAGRAPH,
  EXEMPTION_FINANCIAL,
  CHECKBOX_FORM,
  GOODWILL_MATERIAL,
  OPERATING_PROFIT_LOSS,
  ASSETS_BASIS,
  VALUATION_CURRENT_PERIOD,
  HISTORICAL_BREAKDOWN,
  ANALYSIS_OF_COST_VALUATION,
  ADVANCES_CREDITS,
  SECTION_RENAME,
  MEMBERS_LIABILITY_MODAL
}

export interface ReportNotesResponse {
  body: ReportNotesBody;
  status: number;
}

export interface ReportNotesBody extends ReportNotesData {
  clientId?: string;
  createdTimeUtc?: string;
  entityModificationTime?: string;
  periodId?: string;
  tenantId?: string;
}

export enum StatusTypesEnum {
  CREATED = 204
}
