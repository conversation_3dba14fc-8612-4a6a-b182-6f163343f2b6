import {
  FailedReportErrorCodes,
  GenerateReportPayload,
  GetReportStatusResponse,
  ReportData,
  ReportStatus,
  ValidationData,
  ValidationIssueCategory,
  ValidationIssueType,
  ValidationIssueTarget,
  ReportingStandardVersion,
  ReportingStandards,
  ReportingStandard
} from './report.model';
import { availableReportTemplates } from '../utils/available-report-template-helper';
import { BusinessSubTypes, BusinessTypes } from './client.model';

export const MOCK_GET_REPORTING_STANDARDS_RESPONSE: ReportingStandard[] = [
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e963',
    name: 'FRS105 - Filleted',
    description: '(display) this description $$LTD105SFC001$$',
    display: true,
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Filleted,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCIC,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTD105SFC001',
    equivalentCorrelationId: null
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e962',
    name: 'FRS105 - Full',
    description: 'this description (Display) $$LTD105FUL001$$',
    display: true,
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Full,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCIC,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: 'LTD105FUL001',
    equivalentCorrelationId: null
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e964',
    name: 'FRS105 - Balances sheet',
    description: 'FRS105 - random Filleted report one that is not in available template configuration',
    display: false,
    reportingStandard: ReportingStandards.FRS105,
    reportType: ReportingStandardVersion.Filleted,
    compatibleCompanySubTypes: null,
    compatibleCompanyTypes: null,
    correlationId: null,
    equivalentCorrelationId: null
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e965',
    name: 'FRS102 1A - Full CIC',
    description: 'FRS102 1A - Full CIC $$CICS1AFUL001$$',
    display: true,
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Full,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany],
    correlationId: 'CICS1AFUL001',
    equivalentCorrelationId: 'CICS1ASFC001'
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706l965',
    name: 'FRS102 1A - Full CIC',
    description: 'FRS102 1A - Full CIC $$CICS1AFUL001$$',
    display: true,
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Full,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CICS1AFUL001',
    equivalentCorrelationId: 'CICS1ASFC001'
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e966',
    name: 'FRS102 1A - Filleted CIC',
    description: 'FRS102 1A - Filleted CIC $$CICS1ASFC001$$',
    display: true,
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Filleted,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.CommunityInterestCompany],
    correlationId: 'CICS1ASFC001',
    equivalentCorrelationId: 'CICS1AFUL001'
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3705e966',
    name: 'FRS102 1A - Filleted CIC',
    description: 'FRS102 1A - Filleted CIC $$CICS1ASFC001$$',
    display: true,
    reportingStandard: ReportingStandards.FRS102_1A,
    reportType: ReportingStandardVersion.Filleted,
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [BusinessSubTypes.PropertyLettingCIC],
    correlationId: 'CICS1ASFC001',
    equivalentCorrelationId: 'CICS1AFUL001'
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e967',
    description: 'FRS102 - Full HMRC v25.4.0.1 $$LTD102FUL001$$',
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCIC,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: "LTD102FUL001",
    display: true,
    name: "FRS102 - Full HMRC",
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: "LTD102CHC001"
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e968',
    description: 'FRS102 - Full CH v25.4.0.1 $$LTD102CHC001$$',
    compatibleCompanyTypes: [BusinessTypes.Limited],
    compatibleCompanySubTypes: [
      BusinessSubTypes.None,
      BusinessSubTypes.PropertyLettingCIC,
      BusinessSubTypes.PropertyLettingCompany,
      BusinessSubTypes.Undefined
    ],
    correlationId: "LTD102CHC001",
    display: true,
    name: "FRS102 - Full CH",
    reportingStandard: ReportingStandards.FRS102,
    reportType: ReportingStandardVersion.Full,
    equivalentCorrelationId: "LTD102FUL001"
  },
];

export const MOCK_REPORT_DATA: ReportData = {
  clientId: 'clientId',
  lastSuccessfullProcessId: 'processId',
  lastSuccessfullTimeUtc: '2021-09-01T00:00:00Z',
  periodId: 'periodId',
  previousPeriodId: 'previousPeriodId',
  validationData: {
    validationIssues: [],
    advisoryValidationIssuesLogCount: 0,
    mandatoryValidationIssuesLogCount: 0
  },
  reportingStandard: availableReportTemplates.find(o => o.correlationId === 'LTD105SFC001'),
  entitySetup: null,
  isDirty: false,
  errorCode: null
};

export const VALIDATION_DATA: ValidationData = {
  validationIssues: [
    {
      errorCode: 'ERR1202034',
      breadcrumb: 'MAIN > SECOND',
      description: 'ERR description',
      displayName: 'Error to display',
      errorCategory: ValidationIssueCategory.Mandatory,
      name: 'Error name',
      target: ValidationIssueTarget.IssueLog,
      type: ValidationIssueType.Invalid
    },
    {
      errorCode: 'WRN1202031',
      breadcrumb: 'MAIN > SECOND',
      description: 'Warning description',
      errorCategory: ValidationIssueCategory.Advisory,
      name: 'Warning name',
      target: ValidationIssueTarget.IssueLog,
      type: ValidationIssueType.Missing
    },
    {
      errorCode: 'WRN2202031',
      breadcrumb: 'MAIN > SECOND',
      description: 'Warning description',
      errorCategory: ValidationIssueCategory.Advisory,
      name: 'Warning name',
      target: ValidationIssueTarget.SectionValidation,
      type: ValidationIssueType.Missing
    }
  ]
};

export const MOCK_GENERATE_REPORT_PAYLOAD: GenerateReportPayload = {
  clientId: 'clientId',
  businessType: 'Limited',
  periodId: 'periodId',
  previousPeriodId: 'previousPeriodId',
  reportingStandard: availableReportTemplates.find(o => o.correlationId === 'LTD105SFC001')
};

export const MOCK_GET_REPORT_STATUS_RESPONSE: GetReportStatusResponse = {
  data: {
    status: ReportStatus.SUCCESS,
    errorCode: FailedReportErrorCodes.TECHNICAL
  }
};

export const MOCK_REPORT_STANDARDS: ReportingStandard[] = [
  availableReportTemplates.find(o => o.correlationId === 'LTD105FUL001'),
  availableReportTemplates.find(o => o.correlationId === 'LTD105SFC001'),
  availableReportTemplates.find(o => o.correlationId === 'LTDS1AFUL001'),
  availableReportTemplates.find(o => o.correlationId === 'LTDS1ASFC001')
];
