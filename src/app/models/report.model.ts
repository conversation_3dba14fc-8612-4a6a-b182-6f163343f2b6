import { BusinessSubTypes, BusinessTypes } from './client.model';
import { NotesFormTypeEnum } from './report-sections/report-notes.model';

export interface GenerateReportPayload {
  clientId: string;
  businessType: string;
  periodId: string;
  previousPeriodId?: string;
  reportingStandard: ReportingStandard;
}

export interface EntitySetup {
  reportingStandard: string;
  entitySize: string;
  independentReviewType: string;
  tradingStatus: string;
  dormantStatus: string;
  terminology: string;
  choiceOfStatement: string;
}

export enum EntitySize {
  MICRO = 'Micro',
  SMALL = 'Small',
  MEDIUM = 'Medium',
  LARGE = 'Large'
}

export interface ReportingStandard {
  id: string;
  compatibleCompanyTypes: BusinessTypes[];
  compatibleCompanySubTypes: BusinessSubTypes[] | null;
  correlationId: string;
  display: boolean;
  name: string;
  description: string;
  reportingStandard: ReportingStandards;
  reportType: ReportingStandardVersion;
  equivalentCorrelationId: string | null;
}

export interface ReportingTemplate {
  id: string;
  name: string;
  type: string;
  isPublic: boolean;
  description: string;
  version: ReportingStandardVersionName;
}

export enum Terminology {
  COMPANIES_ACT = 'Companies Act',
  INTERNATIONAL = 'International'
}

export interface TriggerGenerateReportResponse {
  data: ReportData;
}

export interface GetReportStatusResponse {
  data: ReportStatusResponse;
}
export interface ReportStatusResponse {
  status: ReportStatus;
  errorCode: FailedReportErrorCodes;
}

export enum FailedReportErrorCodes {
  TECHNICAL = 'TECHNICAL_ERROR',
  CALCULATION = 'CALCULATION_ERROR'
}

export enum FailedReportErrorMessages {
  TECHNICAL = 'Unable to generate the report due to a technical error.',
  CALCULATION = 'Unable to generate the report due to a calculation error.',
  BANNER_TECHNICAL = 'The report could not refresh due to a technical issue.',
  BANNER_CALCULATION = 'The report could not refresh due to a calculation error.'
}

export enum ReportStatus {
  NO_DATA = 'NoData',
  NOT_STARTED = 'NotStarted',
  IN_PROGRESS = 'InProgress',
  FAILED = 'Failed',
  SUCCESS = 'Successful'
}

export enum ReportState {
  GENERATED = 'Generated',
  NOT_GENERATED = 'Not Generated'
}

export interface ReportType {
  reportingStandard: ReportingStandard;
  entitySetup: EntitySetup | null;
}

export interface ReportDto {
  id: string;
  name: string;
  description: string;
  type: string;
  isPublic: boolean;
  version: string;
}

export interface ReportData {
  clientId: string;
  periodId: string;
  previousPeriodId: string;
  errorCode?: string;
  validationData: ValidationData;
  isDirty: boolean;
  lastSuccessfullTimeUtc: string;
  reportingStandard: ReportingStandard;
  lastSuccessfullProcessId: string;
  entitySetup: EntitySetup | null;
}
//TODO: EXTRACT VALIDATION MODELS IN ITS OWN MODEL FILE

export interface ValidationData {
  validationIssues: ValidationIssue[];
  advisoryValidationIssuesLogCount?: number;
  mandatoryValidationIssuesLogCount?: number;
}

export interface ValidationIssue {
  breadcrumb?: string;
  description?: string;
  displayName?: string;
  errorCategory: ValidationIssueCategory;
  name: string;
  type: ValidationIssueType;
  errorCode: string;
  target: ValidationIssueTarget;
}

export interface ReportingStandardDetail {
  reportingStandards: ReportingStandards;
  versions: ReportingStandardVersion[];
}

export enum ReportingStandardVersion {
  Filleted = 1,
  Full = 2
}

export enum ReportingStandardVersionName {
  Filleted = 'Filleted',
  Full = 'Full'
}

// Reporting standard name coming from entity setup
export enum ReportingStandards {
  FRS105 = 'FRS105',
  FRS102 = 'FRS102',
  FRS102_1A = 'FRS102 1A',
  Unincorporated = 'Unincorporated',
  Charity = 'Charities SORP - FRS102',
  IFRS = 'IFRS'
}

// Used in the report generation payload to accounts builder
export const ReportingStandardsABTypes = {
  FRS105: 'FRS105',
  FRS102: 'FRS102',
  'FRS102 1A': 'FRS102 1A',
  Unincorporated: 'Unincorporated',
  'Charities SORP - FRS102': 'Charities SORP - FRS102',
  IFRS: 'IFRS'
};

// Ids passed to jsonforms
export const ReportingStandardsJsonforms = {
  FRS105: 'FRS105',
  FRS102: 'FRS102',
  'FRS102 1A': 'FRS102 1A',
  Unincorporated: 'Unincorporated',
  'Charities SORP - FRS102': 'CharitiesSORP',
  IFRS: 'IFRS'
};

export const TreeviewIdMapping = {
  FRS105: 'FRS105',
  FRS102: 'FRS102',
  'FRS102 1A': 'FRS102-1A',
  Unincorporated: 'Unincorporated',
  'Charities SORP - FRS102': 'CharitiesSORP',
  IFRS: 'IFRS'
};

export const ChartIdMapping = {
  FRS105: 'FRS105',
  FRS102: 'FRS102',
  'FRS102 1A': 'FRS102 1A',
  Unincorporated: 'Unincorporated',
  'Charities SORP - FRS102': 'CharitiesSORP',
  IFRS: 'IFRS'
};

export enum ValidationIssueTarget {
  IssueLog = 'IssueLog',
  SectionValidation = 'SectionValidation'
}

export enum ValidationIssueType {
  Missing = 'Missing',
  Invalid = 'Invalid'
}

export enum ValidationIssueCategory {
  Mandatory = 'Mandatory',
  Advisory = 'Advisory'
}

export interface ReportNoteSection {
  reportingStandard: ReportingStandards;
  notesToFinancialStatements: NotesFormTypeEnum[];
  accountingPolicies: NotesFormTypeEnum[];
  directorsReports: NotesFormTypeEnum[];
}
