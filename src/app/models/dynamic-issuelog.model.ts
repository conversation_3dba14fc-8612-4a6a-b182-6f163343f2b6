export interface GetTreeviewRequest {
    treeviewId: string;
    clientUUID: string;
    periodUUID: string;
    previousPeriodUUID?: string,
    chartId: string;
}

export interface TreeViewInterface {
    treeviewId: string;
    chartId: string;
    issueLogs: IssueLog[];
}

export interface IssueLogData {
    validationIssues?: IssueLog[];
    advisoryValidationIssuesLogCount?: number;
    mandatoryValidationIssuesLogCount?: number;
}

export interface IssueLog {
    category?: string;
    group?: string;
    issueTitle?: string;
    issueDescription?: string;
    issueLongDescription?: string;
    issuePath?: string;
    screenId?: string;
}