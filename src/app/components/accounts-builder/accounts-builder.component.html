<div class="accounts-builder-container" *ngIf="dataLoaded; else loadingSpinner">
  <elements-accounts-builder-component-v01-pkg-report-sidebar
    [hasSourceData]="hasSourceData"
    [entitySetupData]="entitySetupData"
    [isLoading]="isLoading"
    [clientDetailsReport]="clientDetailsReport"
    [periodData]="periodData"
    [periodUUID]="periodUUID"
    [previousPeriodUUID]="previousPeriodUUID"
    [reportingStandard]="reportData?.reportingStandard"
    (isLoadingUpdate)="updateIsLoading($event)"
  ></elements-accounts-builder-component-v01-pkg-report-sidebar>
  <elements-accounts-builder-component-v01-pkg-display-report
    [hasSourceData]="hasSourceData"
    [isLoading]="isLoading"
    [clientUUID]="clientDetailsReport.clientUUID"
    [periodUUID]="periodUUID"
    [periodId]="periodData?.id"
    (isLoadingUpdate)="updateIsLoading($event)"
  ></elements-accounts-builder-component-v01-pkg-display-report>
</div>
<ng-template #loadingSpinner>
  <iris-loading-spinner size="80"></iris-loading-spinner>
</ng-template>
<iris-snackbar [type]="alert.type" *ngIf="alert" (close)="alert = null">
  {{ alert.message }}
</iris-snackbar>
