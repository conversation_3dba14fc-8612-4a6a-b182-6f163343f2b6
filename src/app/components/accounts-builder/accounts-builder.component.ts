import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EMPTY, forkJoin, of, Subject, Subscription } from 'rxjs';
import { catchError, concatMap, finalize } from 'rxjs/operators';
import { Alert } from 'src/app/models/alert.model';
import { Bookmark } from 'src/app/models/bookmark.model';
import { ClientDetailsReport, ClientInvolvement } from 'src/app/models/client.model';
import { ReportData, ReportState, ReportStatus } from 'src/app/models/report.model';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { AlertService } from 'src/app/services/alert.service';
import { ClientService } from 'src/app/services/client.service';
import { PeriodsService } from 'src/app/services/periods.service';
import { EntitySetupResponse } from '../../models/entity-setup-response';
import { ReportNotesService } from '../../services/report-notes.service';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';
import { AP_INTERNAL_AB_REPORT_STATE, internalEventBus } from 'src/app/event-bus';
import * as moment from 'moment';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-accounts-builder',
  templateUrl: './accounts-builder.component.html',
  styleUrls: ['./accounts-builder.component.scss']
})
export class AccountsBuilderComponent implements OnInit, OnDestroy {
  dataLoaded: boolean = false;
  hasSourceData: boolean = false;
  isReportGenerated: boolean = false;
  entitySetupData: EntitySetupResponse = null;
  isLoading: boolean = true;
  clientDetailsReport: ClientDetailsReport = {
    businessType: null,
    businessTypeName: null,
    businessSubType: null,
    countryRegIn: null,
    clientUUID: null,
    limitedCompanyType: null
  };
  periodUUID: string = null;
  previousPeriodUUID?: string = null;

  alert: Alert = null;
  alertSubscription: Subscription = null;
  reportStatusSubscription: Subscription = null;
  bookmarkList: Bookmark[] = null;
  navigateToBookmark: Subject<Bookmark> = new Subject<Bookmark>();
  reportData: ReportData;
  directors: ClientInvolvement[] = null;
  periodData: AccountProductionPeriod = null;

  private readonly unknownDetail: string = 'Unknown';
  private readonly businessTypesMapping = {
    Limited: 'Limited Company',
    IncorporatedCharity: 'Incorporated Charity',
    LLP: 'Limited Liability Partnership',
    UnincorporatedCharity: 'Unincorporated Charity',
    SoleTrader: 'Sole Trade',
    Partnership: 'Partnership',
    NotApplicable: 'Not Applicable',
    Other: 'Other'
  };

  constructor(
    private activatedRoute: ActivatedRoute,
    private periodsService: PeriodsService,
    private clientService: ClientService,
    private accountsBuilderService: AccountsBuilderService,
    private alertService: AlertService,
    private reportNotesService: ReportNotesService,
    private accountingPoliciesService: AccountingPoliciesService
  ) {}

  ngOnInit(): void {
    const periodId = +this.activatedRoute.snapshot.paramMap.get('periodId');
    const clientId = this.activatedRoute.snapshot.paramMap.get('clientId');
    this.accountsBuilderService.reportData.subscribe(updatedReportData => (this.reportData = updatedReportData));

    forkJoin([
      this.periodsService.getSourceData(periodId),
      this.periodsService.getPeriodData(clientId, periodId, true),
      this.clientService.getClientDetails(clientId),
      this.periodsService.getEntitySetup(clientId, periodId),
      this.clientService.getClientInvolvements(clientId),
      this.clientService.getProfitShare(clientId, periodId)
    ])
      .pipe(
        concatMap(
          ([numberOfSourceDataEntries, periodData, clientDetails, entitySetup, processInvolvement, profitShare]) => {
            return forkJoin([
              of(numberOfSourceDataEntries),
              of(periodData),
              of(clientDetails),
              this.accountsBuilderService.getReportData(clientId, periodData.periodId).pipe(
                catchError(() => {
                  return of(null);
                })
              ),
              of(entitySetup),
              of(processInvolvement),
              of(profitShare)
            ]);
          }
        ),
        finalize(() => {
          this.dataLoaded = true;
        })
      )
      .subscribe(
        ([
          numberOfSourceDataEntries,
          periodData,
          clientDetails,
          reportData,
          entitySetup,
          processInvolvement,
          profitShare
        ]) => {
          this.hasSourceData = numberOfSourceDataEntries > 0;
          this.periodData = periodData;
          this.periodUUID = periodData.periodId;
          this.previousPeriodUUID = periodData.previousPeriodId;
          this.clientDetailsReport = {
            businessType: clientDetails.businessType,
            businessTypeName: this.businessTypesMapping[clientDetails.businessType] ?? this.unknownDetail,
            businessSubType: clientDetails.businessSubType,
            countryRegIn: clientDetails.countryRegIn || this.unknownDetail,
            clientUUID: clientDetails.id,
            limitedCompanyType: clientDetails.limitedCompanyType
          };
          this.reportData = reportData;
          this.isReportGenerated = moment.utc(reportData?.lastSuccessfullTimeUtc).isAfter(moment.utc('0001-01-01'));
          this.entitySetupData = entitySetup;
          this.directors = processInvolvement.directors.filter(d => !d?.to);
          this.reportNotesService.limitedCompanyType = this.clientDetailsReport.limitedCompanyType;
          this.reportNotesService.businessType = this.clientDetailsReport.businessType;
          this.accountingPoliciesService.businessType = this.clientDetailsReport.businessType;
          this.accountingPoliciesService.reportingStandard = this.entitySetupData.reportingStandard;
          this.reportNotesService.businessType = this.clientDetailsReport.businessType;
          this.reportNotesService.reportingStandard = this.entitySetupData.reportingStandard;
          this.reportNotesService.profitShare = profitShare;
        }
      );

    this.alertSubscription = this.alertService.alertsSubject.subscribe(alert => (this.alert = alert));
    this.reportStatusSubscription = this.accountsBuilderService.reportStatusSubject.subscribe(status =>
      this.updateReportStatus(status)
    );
  }

  updateIsLoading(isLoading: boolean): void {
    this.isLoading = isLoading;
  }

  updateBookmarkSections(newBookmarkList: Bookmark[]): void {
    this.bookmarkList = newBookmarkList;
  }

  navigateToBookmarkUpdate(newBookmark: Bookmark): void {
    this.navigateToBookmark.next(newBookmark);
  }

  updateReportStatus(status: string): void {
    if (status === ReportStatus.SUCCESS && !this.isReportGenerated) {
      this.isReportGenerated = true;
      if (internalEventBus?.emit) {
        internalEventBus.emit(AP_INTERNAL_AB_REPORT_STATE, ReportState.GENERATED);
      }
    }
  }

  ngOnDestroy() {
    this.alertSubscription?.unsubscribe();
    this.reportStatusSubscription?.unsubscribe();
  }
}
