import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { features } from 'src/features';
import { mountRootParcel } from 'single-spa';
import { environment } from 'src/environments/environment';
import { report } from 'process';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-xbrl-section-tag-editor',
  templateUrl: './xbrl-section-tag-editor.component.html',
  styleUrl: './xbrl-section-tag-editor.component.scss'
})
export class XbrlSectionTagEditorComponent implements OnInit {
  @Input() clientUUID: string;
  @Input() periodUUID: string;
  @Input() previousPeriodUUID: string;
  @Input() reportId: string;
  @Input() taxonomyId: number;
  @Output() isApplicationLoaded: EventEmitter<boolean> = new EventEmitter<boolean>(false);
  mountRootParcel = mountRootParcel;
  parcelProps;
  xbrlTagEditorApp;
  windowAny: any = window;

  constructor() {
    console.log('XbrlSectionTagEditorComponent');
  }

  ngOnInit(): void {
    this.loadParcel();
  }
  loadParcel(): void {
    if (this.windowAny.System) {
      this.windowAny.System.import('@iris/elements2-accountsproduction-xbrl-tag-editor-v01-pkg').then(mod => {
        this.setParcelProps();
        this.xbrlTagEditorApp = mod.default;
      });
    }
    //this.isApplicationLoaded.emit(true);
  }

  setParcelProps(): void {
    this.parcelProps = {
      environment: environment,
      features: features,
      reportId: this.reportId,
      clientUUID: this.clientUUID,
      periodUUID: this.periodUUID,
      previousPeriodUUID: this.previousPeriodUUID,
      taxonomyId: 34,
      taxonomy: 'frs-102-2023-01-01-v1.0.1',
      code: 'FRS102'
    };
  }
}
