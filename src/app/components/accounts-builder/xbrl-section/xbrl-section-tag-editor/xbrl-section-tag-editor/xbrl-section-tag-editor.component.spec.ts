import { ComponentFixture, TestBed } from '@angular/core/testing';
import { XbrlSectionTagEditorComponent } from './xbrl-section-tag-editor.component';
import { EventEmitter } from '@angular/core';

describe('XbrlSectionTagEditorComponent', () => {
  let component: XbrlSectionTagEditorComponent;
  let fixture: ComponentFixture<XbrlSectionTagEditorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [XbrlSectionTagEditorComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(XbrlSectionTagEditorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default values for inputs', () => {
    expect(component.clientUUID).toBeUndefined();
    expect(component.periodUUID).toBeUndefined();
    expect(component.previousPeriodUUID).toBeUndefined();
    expect(component.reportId).toBeUndefined();
  });

  it('should emit isApplicationLoaded event', () => {
    spyOn(component.isApplicationLoaded, 'emit');
    component.isApplicationLoaded.emit(true);
    expect(component.isApplicationLoaded.emit).toHaveBeenCalledWith(true);
  });

  it('should call loadParcel on ngOnInit', () => {
    spyOn(component, 'loadParcel');
    component.ngOnInit();
    expect(component.loadParcel).toHaveBeenCalled();
  });

  it('should set parcelProps correctly', () => {
    component.clientUUID = 'test-client';
    component.periodUUID = 'test-period';
    component.previousPeriodUUID = 'test-previous-period';
    component.reportId = 'test-report';
    component.setParcelProps();
    expect(component.parcelProps).toEqual({
      environment: jasmine.any(Object),
      features: jasmine.any(Object),
      reportId: 'test-report',
      clientUUID: 'test-client',
      periodUUID: 'test-period',
      previousPeriodUUID: 'test-previous-period',
      taxonomyId: 34,
      taxonomy: 'frs-102-2023-01-01-v1.0.1',
      code: 'FRS102'
    });
  });

  it('should load parcel if System is available', async () => {
    component.windowAny.System = {
      import: jasmine.createSpy('import').and.returnValue(Promise.resolve({ default: 'mockParcelApp' }))
    };
    await component.loadParcel();
    expect(component.windowAny.System.import).toHaveBeenCalled();
    expect(component.xbrlTagEditorApp).toBe('mockParcelApp');
  });
});
