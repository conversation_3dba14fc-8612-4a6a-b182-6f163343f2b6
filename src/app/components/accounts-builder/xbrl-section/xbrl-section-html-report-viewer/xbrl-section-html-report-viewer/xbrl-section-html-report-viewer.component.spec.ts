import { ComponentFixture, TestBed } from '@angular/core/testing';
import { XbrlSectionHtmlReportViewerComponent } from './xbrl-section-html-report-viewer.component';
import { environment } from 'src/environments/environment';
import { features } from 'src/features';

describe('XbrlSectionHtmlReportViewerComponent', () => {
  let component: XbrlSectionHtmlReportViewerComponent;
  let fixture: ComponentFixture<XbrlSectionHtmlReportViewerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [XbrlSectionHtmlReportViewerComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(XbrlSectionHtmlReportViewerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call loadParcel on ngOnInit', () => {
    const loadParcelSpy = spyOn(component, 'loadParcel');
    component.ngOnInit();
    expect(loadParcelSpy).toHaveBeenCalled();
  });

  describe('setParcelProps', () => {
    it('should set parcelProps with the correct values', () => {
      component.clientUUID = 'client-uuid';
      component.periodUUID = 'period-uuid';
      component.previousPeriodUUID = 'previous-period-uuid';
      component.reportId = 'report-id';

      component.setParcelProps();

      expect(component.parcelProps).toEqual({
        environment: environment,
        features: features,
        reportId: 'report-id',
        clientUUID: 'client-uuid',
        periodUUID: 'period-uuid',
        previousPeriodUUID: 'previous-period-uuid',
        tenantId: undefined
      });
    });
  });
});
