import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { features } from 'src/features';
import { mountRootParcel } from 'single-spa';
import { environment } from 'src/environments/environment';
import { userUtil } from 'src/app/utils/userUtil';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-xbrl-section-html-report-viewer',
  templateUrl: './xbrl-section-html-report-viewer.component.html',
  styleUrl: './xbrl-section-html-report-viewer.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class XbrlSectionHtmlReportViewerComponent implements OnInit {
  @Input() clientUUID: string;
  @Input() periodUUID: string;
  @Input() previousPeriodUUID: string;
  @Input() reportId: string;
  @Output() isApplicationLoaded: EventEmitter<boolean> = new EventEmitter<boolean>(false);
  mountRootParcel = mountRootParcel;
  parcelProps;
  xbrlHtmlReportViewerApp = null;
  windowAny: any = window;

  constructor() {}

  ngOnInit(): void {
    this.loadParcel();
  }
  loadParcel(): void {
    if (this.windowAny.System) {
      this.windowAny.System.import(`@iris/elements2-accountsproduction-xbrl-html-report-viewer-v01-pkg`).then(mod => {
        this.setParcelProps();
        this.xbrlHtmlReportViewerApp = mod.default;
      });
    }
  }

  setParcelProps(): void {
    const user = userUtil.get();
    this.parcelProps = {
      environment: environment,
      features: features,
      reportId: this.reportId,
      clientUUID: this.clientUUID,
      periodUUID: this.periodUUID,
      previousPeriodUUID: this.previousPeriodUUID,
      tenantId: user?.userProfile?.platformTenantId
    };
  }
}
