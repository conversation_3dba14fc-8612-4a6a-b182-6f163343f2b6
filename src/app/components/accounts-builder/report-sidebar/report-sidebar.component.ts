import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Subscription } from 'rxjs';
import { Bookmark } from 'src/app/models/bookmark.model';
import { ClientDetailsReport } from 'src/app/models/client.model';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportingStandard } from 'src/app/models/report.model';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { AppSettings } from 'src/app/utils/appSettings';
import { features } from 'src/features';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-report-sidebar',
  templateUrl: './report-sidebar.component.html',
  styleUrls: ['./report-sidebar.component.scss']
})
export class ReportSidebarComponent {
  @Input() hasSourceData: boolean = false;
  @Input() entitySetupData: boolean = false;
  @Input() isLoading: boolean = true;
  @Input() reportingStandard: ReportingStandard = null;
  @Input() clientDetailsReport: ClientDetailsReport = {
    businessTypeName: null,
    businessType: null,
    businessSubType: null,
    countryRegIn: null,
    clientUUID: null,
    limitedCompanyType: null
  };
  @Input() periodData: AccountProductionPeriod = null;
  @Input() periodUUID: string = null;
  @Input() previousPeriodUUID?: string = null;
  @Output() isLoadingUpdate: EventEmitter<boolean> = new EventEmitter<boolean>();

  isGenerateReportButtonDisabled: boolean = true;
  successfulReportGenerated: boolean = false;
  reportingStandardValue: string = null;
  navigationState: ReportSection;
  selectedOption: Bookmark = null;
  hasBookmarks = false;
  isAccountsProductionDataScreenToolEnabled: boolean = false;
  isXbrlEditorModeOn: boolean = false;
  isXbrlButtonEnabled: boolean = true;

  $subscriptions: Subscription[] = [];
  constructor(private reportNavigationService: ReportNavigationService) {
    this.reportNavigationService.currentNavigationNode.subscribe(currentNavigationNode => {
      this.navigationState = currentNavigationNode;
    });
  }

  ngOnInit(): void {
    features.getState(AppSettings.FeatureFlag.AccountsProduction.DataScreenTool).subscribe(isToggledOn => {
      this.isAccountsProductionDataScreenToolEnabled = isToggledOn;
    });
  }

  removeSubscriptions(): void {
    this.$subscriptions.forEach(sub => {
      sub.unsubscribe();
    });
    this.$subscriptions = [];
  }

  onReportingStandardChange([value, isReportButtonClicked]: [string, boolean]): void {
    this.reportingStandardValue = value;
    this.isXbrlButtonEnabled = !isReportButtonClicked;
  }

  onApplicationLoaded(isJsonFormsLoaded: boolean): void {
    this.isXbrlButtonEnabled = isJsonFormsLoaded;
  }

  onXbrlEditorModeChanged([xblrMode, taxonomyId]: [boolean, number]): void {
    this.isXbrlEditorModeOn = xblrMode;
  }

  ngOnDestroy() {
    this.removeSubscriptions();
  }
}
