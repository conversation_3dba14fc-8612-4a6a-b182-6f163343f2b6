import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, convertToParamMap } from '@angular/router';
import { of, Subject, Subscription, throwError } from 'rxjs';
import { BusinessSubTypes, BusinessTypes } from 'src/app/models/client.model';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import { MOCK_REPORT_DATA, MOCK_GET_REPORTING_STANDARDS_RESPONSE } from 'src/app/models/report.mock';
import { EntitySize, ReportingStandard, ReportStatus, Terminology } from 'src/app/models/report.model';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { PeriodsService } from 'src/app/services/periods.service';
import { ReportBuilderService } from 'src/app/services/report-builder.service';
import { features } from 'src/features';

import { ReportTypeComponent } from './report-type.component';
import { EntitySetupResponse } from 'src/app/models/entity-setup-response';
import { AppSettings } from 'src/app/utils/appSettings';

describe('ReportTypeComponent', () => {
  let component: ReportTypeComponent;
  let fixture: ComponentFixture<ReportTypeComponent>;
  let getFeatureToggleStateSpy: jasmine.Spy;
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
    'triggerGenerateReport',
    'getReportStatus',
    'getPreviousReportData',
    'setGenerateReportPayload'
  ]);

  const reportBuilderServiceMock = jasmine.createSpyObj('BuilderService', ['getReportingStandards']);
  const periodsServiceMock = jasmine.createSpyObj('PeriodsService', ['getPeriodList']);

  const entitySizeObjectData = [
    { value: 'MICRO', name: 'Micro' },
    { value: 'SMALL', name: 'Small' },
    { value: 'MEDIUM', name: 'Medium' },
    { value: 'LARGE', name: 'Large' }
  ];

  const CLIENT_ID = '123';
  const PERIOD_UUID = '456';

  const PERIOD_DATA: AccountProductionPeriod[] = [
    {
      id: 1,
      clientUuid: CLIENT_ID,
      status: { id: 1, name: 'InProgress' },
      endDate: '2025-05-10T00:00:00Z',
      periodId: PERIOD_UUID
    },
    {
      id: 1,
      clientUuid: CLIENT_ID,
      status: { id: 1, name: 'InProgress' },
      endDate: '2024-05-10T00:00:00Z',
      periodId: '********-1111-1111-1111-********'
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [ReportTypeComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: convertToParamMap({ clientId: '123' })
            }
          }
        },
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        },
        {
          provide: PeriodsService,
          useValue: periodsServiceMock
        },
        {
          provide: ReportBuilderService,
          useValue: reportBuilderServiceMock
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    accountsBuilderServiceMock.triggerGenerateReport.and.returnValue(of({}));
    accountsBuilderServiceMock.getReportStatus.and.returnValue(of(ReportStatus.SUCCESS));
    accountsBuilderServiceMock.getPreviousReportData.and.returnValue(of(MOCK_REPORT_DATA));
    accountsBuilderServiceMock.triggerGenerateReportSubject = new Subject<any>();
    accountsBuilderServiceMock.reportProcessIdSubject = new Subject<string>();
    accountsBuilderServiceMock.reportStatusSubject = new Subject<string>();
    accountsBuilderServiceMock.reportData = new Subject<string>();

    reportBuilderServiceMock.getReportingStandards.and.returnValue(of(MOCK_GET_REPORTING_STANDARDS_RESPONSE));

    periodsServiceMock.getPeriodList.and.returnValue(of(PERIOD_DATA));

    fixture = TestBed.createComponent(ReportTypeComponent);
    component = fixture.componentInstance;

    component.periodUUID = PERIOD_UUID;
    component.clientDetailsReport = {
      businessType: BusinessTypes.Limited,
      businessTypeName: 'Limited',
      businessSubType: BusinessSubTypes.None,
      countryRegIn: null,
      clientUUID: CLIENT_ID,
      limitedCompanyType: null
    };

    component.entitySetupData = {
      entitySize: 'test',
      terminology: 'test',
      reportingStandard: 'test',
      independentReviewType: 'test',
      choiceOfStatement: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };

    fixture.detectChanges();

    getFeatureToggleStateSpy = spyOn(features, 'getState').and.callFake((toggleName: string) => {
      if (toggleName === AppSettings.FeatureFlag.AccountsProduction.Report.ReportTemplates.ViewAll) {
        return of(true);
      } else {
        return of(false);
      }
    });
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should navigate to admin tools page', () => {
    const windowOpenSpy = spyOn(window, 'open');
    component.reportData = MOCK_REPORT_DATA;
    component.viewResultsDisabled = false;
    component.isLoading = false;
    const expectedUrl = `/financial-performance/results?clientId=123&processId=${MOCK_REPORT_DATA.lastSuccessfullProcessId}&processType=${MOCK_REPORT_DATA.reportingStandard.reportingStandard}`;

    component.viewResults();

    expect(windowOpenSpy).toHaveBeenCalledWith(expectedUrl, '_blank');
  });

  it('should not navigate to admin tools page', () => {
    const windowOpenSpy = spyOn(window, 'open');
    component.reportData = MOCK_REPORT_DATA;
    component.isLoading = true;
    component.viewResultsDisabled = true;
    component.viewResults();

    expect(windowOpenSpy).not.toHaveBeenCalled();
  });

  it('should init default reporting standard', () => {
    const reportingStandard: ReportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[1];
    component.selectedReportingStandard = null;
    component.reportingStandard = reportingStandard;

    component.initData(MOCK_GET_REPORTING_STANDARDS_RESPONSE, reportingStandard.name);

    expect(component.reportingStandard.id).toEqual(reportingStandard.id);
  });

  it('should update reporting standard', () => {
    component.reportingStandard = null;

    component.selectedReportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[2];
    const expectedReportingStandard: ReportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
    component.updateReportingStandard({ detail: expectedReportingStandard.id });

    expect(component.reportingStandard.id).toEqual(expectedReportingStandard.id);
  });

  it('should assign all reporting standard when reporttemplates.viewall feature toggle is on', () => {
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeTrue();
    expect(component.reportingStandards.length == MOCK_GET_REPORTING_STANDARDS_RESPONSE.length).toBeTrue();
  });

  it('should not assign all reporting standard when reporttemplates.viewall feature toggle is off', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(component.reportingStandards.length <= MOCK_GET_REPORTING_STANDARDS_RESPONSE.length).toBeTrue();
  });

  it('should restrict reporting standard to FRS105 when reporttemplates.viewall feature toggle is off and entitysetup reportingstandard is FRS105', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });
    component.entitySetupData = {
      reportingStandard: 'FRS105',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };
    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(
      component.reportingStandards.length ===
        MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
          el => el.display && el.reportingStandard === component.entitySetupData.reportingStandard
        ).length
    ).toBeTrue();
  });

  it('should not return any reporting standard when reporttemplates.viewall feature toggle is off and FRS102Enabled is off and entitysetup reportingstandard is FRS102', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });

    component.entitySetupData = {
      reportingStandard: 'FRS102',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };
    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(component.reportingStandards.length).toEqual(0);
  });

  it('should not return any reporting standard when reporttemplates.viewall feature toggle is off and Inc/Uninc Charities toggle is off and entitysetup reportingstandard is Charity', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });
    component.entitySetupData = {
      reportingStandard: 'Charities SORP - FRS102',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };
    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(component.reportingStandards.length).toEqual(0);
  });

  it('should not return any reporting standard when reporttemplates.viewall feature toggle is off and IFRSEnabled is off and entitysetup reportingstandard is IFRS', () => {
  getFeatureToggleStateSpy.and.callFake(() => {
    return of(false);
  });

  component.entitySetupData = {
    reportingStandard: 'IFRS',
    entitySize: 'test',
    terminology: 'test',
    choiceOfStatement: 'test',
    independentReviewType: 'test',
    dormantStatus: 'test',
    tradingStatus: 'test',
    accountPeriodId: 12121
  };
  component.viewAllTemplates = false;
  component.ngOnInit();
  expect(component.viewAllTemplates).toBeFalse();
  expect(component.reportingStandards.length).toEqual(0);
});

  

  it('should restrict reporting standard to FRS102 when reporttemplates.viewall feature toggle is off and FRS102Enabled is on and entitysetup reportingstandard is FRS102', () => {
    getFeatureToggleStateSpy.and.callFake((toggleName: string) => {
      if (toggleName === AppSettings.FeatureFlag.AccountsProduction.FRS102Enabled) {
        return of(true);
      } else if (toggleName === AppSettings.FeatureFlag.AccountsProduction.Report.ReportTemplates.ViewAll) {
        return of(false);
      } else {
        return of(false);
      }
    });

    component.entitySetupData = {
      reportingStandard: 'FRS102',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };
    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(
      component.reportingStandards.length ===
        MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
          el => el.display && el.reportingStandard === component.entitySetupData.reportingStandard
        ).length
    ).toBeTrue();
  });

  it('should restrict reporting standard to IFRS when reporttemplates.viewall feature toggle is off and IFRSEnabled is on and entitysetup reportingstandard is IFRS', () => {
  getFeatureToggleStateSpy.and.callFake((toggleName: string) => {
    if (toggleName === AppSettings.FeatureFlag.AccountsProduction.IFRSEnabled) {
      return of(true);
    } else if (toggleName === AppSettings.FeatureFlag.AccountsProduction.Report.ReportTemplates.ViewAll) {
      return of(false);
    } else {
      return of(false);
    }
  });

  component.entitySetupData = {
    reportingStandard: 'IFRS',
    entitySize: 'test',
    terminology: 'test',
    choiceOfStatement: 'test',
    independentReviewType: 'test',
    dormantStatus: 'test',
    tradingStatus: 'test',
    accountPeriodId: 12121
  };
  component.viewAllTemplates = false;
  component.ngOnInit();
  expect(component.viewAllTemplates).toBeFalse();
  expect(
    component.reportingStandards.length ===
      MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
        el => el.display && el.reportingStandard === component.entitySetupData.reportingStandard
      ).length
  ).toBeTrue();
});
  

  it('should restrict reporting standard to Charity when reporttemplates.viewall feature toggle is off and Inc/Uninc Charities toggle is on and entitysetup reportingstandard is Charity', () => {
    getFeatureToggleStateSpy.and.callFake((toggleName: string) => {
      if (
        toggleName === AppSettings.FeatureFlag.AccountsProduction.IncorporatedCharities &&
        AppSettings.FeatureFlag.AccountsProduction.UnincorporatedCharities
      ) {
        return of(true);
      } else if (toggleName === AppSettings.FeatureFlag.AccountsProduction.Report.ReportTemplates.ViewAll) {
        return of(false);
      } else {
        return of(false);
      }
    });
    component.entitySetupData = {
      reportingStandard: 'Charities SORP - FRS102',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };
    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(
      component.reportingStandards.length ===
        MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
          el => el.display && el.reportingStandard === component.entitySetupData.reportingStandard
        ).length
    ).toBeTrue();
  });

  it('should restrict reporting standard to unincorporated when reporttemplates.viewall feature toggle is off and entitysetup reportingstandard is unincorporated', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });
    component.entitySetupData = {
      reportingStandard: 'Unincorporated',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };

    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(
      component.reportingStandards.length ===
        MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
          el => el.display && el.reportingStandard === component.entitySetupData.reportingStandard
        ).length
    ).toBeTrue();
  });

  it('should have empty reporting standard when reporttemplates.viewall feature toggle is off and entitysetup is not set', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });
    component.entitySetupData = null;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(component.reportingStandards.length === 0).toBeTrue();
  });

  it('should assign public reporting standard when feature toggle is off', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });
    component.ngOnInit();
    const publicMockTemplates = MOCK_GET_REPORTING_STANDARDS_RESPONSE?.filter(el =>
      el.description.toLowerCase().startsWith(component.publicTemplateTag.toLocaleLowerCase())
    );
    const publicComponentTemplates = component.reportingStandards?.filter(el =>
      el.description.toLowerCase().startsWith(component.publicTemplateTag.toLocaleLowerCase())
    );
    expect(publicComponentTemplates.length <= publicMockTemplates.length).toBeTrue();
  });

  it('should restrict reporting standard to FRS102 1A and CIC when reporttemplates.viewall feature toggle is off, entitysetup reportingstandard is FRS102 1A and businessSubType is CIC', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });

    component.entitySetupData = {
      reportingStandard: 'FRS102 1A',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };

    component.clientDetailsReport = {
      businessType: BusinessTypes.Limited,
      businessTypeName: 'Limited',
      businessSubType: BusinessSubTypes.CommunityInterestCompany,
      countryRegIn: null,
      clientUUID: CLIENT_ID,
      limitedCompanyType: null
    };

    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(
      component.reportingStandards.length ===
        MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
          el =>
            el.display &&
            el.reportingStandard === component.entitySetupData.reportingStandard &&
            el.compatibleCompanySubTypes.includes(component.clientDetailsReport.businessSubType)
        ).length
    ).toBeTrue();
  });

  it('should restrict reporting standard to FRS102 1A and property letting cic when reporttemplates.viewall feature toggle is off, entitysetup reportingstandard is FRS102 1A and businessSubType is property letting cic', () => {
    getFeatureToggleStateSpy.and.callFake(() => {
      return of(false);
    });

    component.entitySetupData = {
      reportingStandard: 'FRS102 1A',
      entitySize: 'test',
      terminology: 'test',
      choiceOfStatement: 'test',
      independentReviewType: 'test',
      dormantStatus: 'test',
      tradingStatus: 'test',
      accountPeriodId: 12121
    };

    component.clientDetailsReport = {
      businessType: BusinessTypes.Limited,
      businessTypeName: 'Limited',
      businessSubType: BusinessSubTypes.PropertyLettingCIC,
      countryRegIn: null,
      clientUUID: CLIENT_ID,
      limitedCompanyType: null
    };

    component.viewAllTemplates = false;
    component.ngOnInit();
    expect(component.viewAllTemplates).toBeFalse();
    expect(
      component.reportingStandards.length ===
        MOCK_GET_REPORTING_STANDARDS_RESPONSE.filter(
          el =>
            el.display &&
            el.reportingStandard === component.entitySetupData.reportingStandard &&
            el.compatibleCompanySubTypes.includes(component.clientDetailsReport.businessSubType)
        ).length
    ).toBeTrue();
  });

  describe('getPeriodDAta', () => {
    it('should set default report type if report type is not available', () => {
      const reportingStandard: ReportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[1];

      const reportType = {
        entitySize: EntitySize.LARGE,
        reportingStandard: reportingStandard,
        terminology: Terminology.COMPANIES_ACT,
        entitySetup: null
      };

      component.reportingStandard = reportingStandard;
      component.entitySetupData = {
        entitySize: EntitySize.LARGE,
        terminology: Terminology.COMPANIES_ACT
      } as EntitySetupResponse;

      component.ngOnInit();

      expect(component.reportingStandard).toEqual(reportType.reportingStandard);
    });
  });

  describe('companySubTypeFilter', () => {
    it('should return true if compatibleCompanySubTypes is null', () => {
      const reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[2];
      expect(component.companySubTypeFilter(reportingStandard)).toBeTrue();
    });
    it('should return false if businessSubType is null and compatibleCompanySubTypes does not include Undefined', () => {
      const reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[3];
      component.clientDetailsReport.businessSubType = null;
      expect(component.companySubTypeFilter(reportingStandard)).toBeFalse();
    });
    it('should return true if compatibleCompanySubTypes includes businessSubType', () => {
      const reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
      component.clientDetailsReport.businessSubType = BusinessSubTypes.None;
      expect(component.companySubTypeFilter(reportingStandard)).toBeTrue();
    });
    it('should return false if compatibleCompanySubTypes does not include businessSubType', () => {
      const reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
      component.clientDetailsReport.businessSubType = BusinessSubTypes.Academy;
      expect(component.companySubTypeFilter(reportingStandard)).toBeFalse();
    });
  });

  describe('unsubscribe', () => {
    beforeEach(() => {
      component.$subscriptions.push(new Subscription());
      component.$subscriptions.push(new Subscription());
    });

    it('should remove all subscriptions', () => {
      component.ngOnDestroy();
      expect(component.$subscriptions.length).toBe(0);
    });
  });

  describe('triggerGenerateReport', () => {
    let reportProcessIdSubjectSpy: any;
    beforeEach(() => {
      reportProcessIdSubjectSpy = spyOn(accountsBuilderServiceMock.reportProcessIdSubject, 'next');
      component.reportingStandards = MOCK_GET_REPORTING_STANDARDS_RESPONSE;
    });

    it('should call reportProcessIdSubject with null if triggerGenerateReport throws error', () => {
      const reportingStandard: ReportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
      component.reportingStandard = reportingStandard;

      const triggerGenerateReportSubjectSpy = spyOn(accountsBuilderServiceMock.triggerGenerateReportSubject, 'next');
      accountsBuilderServiceMock.triggerGenerateReport.and.returnValue(throwError(Error));
      component.triggerGenerateReport();
      expect(triggerGenerateReportSubjectSpy).toHaveBeenCalled();
    });

    it('trigger generate button should be disabled if no source data are found', () => {
      component.hasSourceData = false;
      component.reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
      component.entitySetupData = {
        entitySize: 'test',
        terminology: 'test',
        reportingStandard: 'test',
        independentReviewType: 'test',
        choiceOfStatement: 'test',
        dormantStatus: 'test',
        tradingStatus: 'test',
        accountPeriodId: 12121
      };
      component.ngOnInit();
      expect(component.isGenerateReportButtonDisabled).toBeTrue();
    });

    it('trigger generate button should be disabled if no entity setup data are found', () => {
      component.hasSourceData = true;
      component.entitySetupData = null;
      component.reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
      component.ngOnInit();
      expect(component.isGenerateReportButtonDisabled).toBeTrue();
    });

    it('trigger generate button should be disabled if xbrl toggle button is ON', () => {
      component.isXbrlEditorModeOn = true;
      component.ngOnInit();
      expect(component.isGenerateReportButtonDisabled).toBeTrue();
    });

    it('trigger generate button should be enabled if both source data and entity setup data are found', () => {
      component.hasSourceData = true;
      component.reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE[0];
      component.entitySetupData = {
        entitySize: 'test',
        terminology: 'test',
        reportingStandard: 'test',
        independentReviewType: 'test',
        choiceOfStatement: 'test',
        dormantStatus: 'test',
        tradingStatus: 'test',
        accountPeriodId: 12121
      };
      component.ngOnInit();
      expect(component.isGenerateReportButtonDisabled).toBeFalse();
    });
  });

  describe('next on subject', () => {
    beforeEach(() => {
      component.isGenerateReportButtonDisabled = true;
      component.hasSourceData = true;
    });

    it('should disable the trigger generate report button', () => {
      spyOn(component, 'triggerGenerateReport');
      accountsBuilderServiceMock.triggerGenerateReportSubject.next();

      expect(component.isGenerateReportButtonDisabled).toBe(true);
    });

    it("should keep the 'Generate report' button disabled if report status is IN_PROGRESS", () => {
      component.isGenerateReportButtonDisabled = false;
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.IN_PROGRESS);

      expect(component.isGenerateReportButtonDisabled).toBeTrue();
    });

    it("should disable the 'Generate report' button if report status is SUCCESS", () => {
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.SUCCESS);

      expect(component.isGenerateReportButtonDisabled).toBeFalse();
    });

    it("should disable the 'Generate report' button if report status is FAILED", () => {
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.FAILED);

      expect(component.isGenerateReportButtonDisabled).toBeFalse();
    });

    it("should disable the 'Generate report' button if report status is NOT_STARTED", () => {
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.NOT_STARTED);

      expect(component.isGenerateReportButtonDisabled).toBeFalse();
    });

    it("should disable the 'Generate report' button if report status is NO_DATA", () => {
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.NO_DATA);

      expect(component.isGenerateReportButtonDisabled).toBeFalse();
    });

    it("should enable the 'Generate report' button if report status is SUCCESS and there is no source data present", () => {
      component.hasSourceData = false;
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.SUCCESS);

      expect(component.isGenerateReportButtonDisabled).toBeTrue();
    });
  });

  describe('updateGenerateReportPayload', () => {
    it('should call setGenerateReportPayload with the current values', () => {
      const MOCK_GENERATE_REPORT_PAYLOAD = {
        clientId: 'clientUUID',
        businessType: 'Limited',
        periodId: 'periodUUID',
        previousPeriodId: 'previousPeriodUUID',
        reportingStandard: MOCK_GET_REPORTING_STANDARDS_RESPONSE.find(t => t.correlationId === 'LTD105SFC001')
      };
      component.entitySetupData = {
        entitySize: 'test',
        terminology: 'test',
        reportingStandard: 'FRS105',
        independentReviewType: 'test',
        choiceOfStatement: 'test',
        dormantStatus: 'test',
        tradingStatus: 'test',
        accountPeriodId: 12121
      };
      component.selectedReportingStandard = MOCK_GENERATE_REPORT_PAYLOAD.reportingStandard;
      component.clientDetailsReport = {
        businessType: BusinessTypes.Limited,
        businessTypeName: null,
        businessSubType: BusinessSubTypes.None,
        countryRegIn: null,
        clientUUID: MOCK_GENERATE_REPORT_PAYLOAD.clientId,
        limitedCompanyType: null
      };
      component.periodUUID = MOCK_GENERATE_REPORT_PAYLOAD.periodId;
      component.previousPeriodUUID = MOCK_GENERATE_REPORT_PAYLOAD.previousPeriodId;
      component.reportingStandard = MOCK_GENERATE_REPORT_PAYLOAD.reportingStandard;
      component.updateGenerateReportPayload();
      expect(accountsBuilderServiceMock.setGenerateReportPayload).toHaveBeenCalledWith(MOCK_GENERATE_REPORT_PAYLOAD);
    });
  });
});
