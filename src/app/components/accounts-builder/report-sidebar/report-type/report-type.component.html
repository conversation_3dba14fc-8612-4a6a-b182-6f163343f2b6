<div class="report-type"  *ngIf="dataLoaded; else loadingSpinner">
  <iris-select
    label="Select report"
    [objectData]="reportingStandardObjectData"
    (changed)="updateReportingStandard($event)"
    [value]="selectedReportingStandardName"
    required="true"
    width="s"
  ></iris-select>
  <div *ngIf="!entitySetupData?.reportingStandard" class="entity-setup-error">
    <div><iris-icon-only type="alert-circle" size="14" padding="0"></iris-icon-only> Complete entity setup to generate the relevant report</div>
  </div>
  <iris-button
    [disabled]="isLoading || isGenerateReportButtonDisabled || isXbrlEditorModeOn"
    (click)="triggerGenerateReport()"
  >
    Generate report
  </iris-button>
  <iris-button
    *ngIf="isViewResultsOn"
    class="view-results"
    (click)="viewResults()"
    [disabled]="isLoading || viewResultsDisabled"
    colour="tertiary"
  >
    View Results
  </iris-button>
</div>

<ng-template #loadingSpinner>
  <iris-loading-spinner size="80"></iris-loading-spinner>
</ng-template>
