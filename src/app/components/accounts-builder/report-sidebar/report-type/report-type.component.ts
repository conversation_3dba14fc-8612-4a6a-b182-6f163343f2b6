import { forkJoin, Observable, of, Subscription, throwError } from 'rxjs';
import { catchError, concatMap, finalize, map } from 'rxjs/operators';
import { Alert } from 'src/app/models/alert.model';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { BusinessSubTypes, BusinessTypes, ClientDetailsReport } from 'src/app/models/client.model';
import {
  GenerateReportPayload,
  ReportStatus,
  ReportData,
  ReportingStandard,
  ReportingStandards
} from 'src/app/models/report.model';
import { SelectOption } from 'src/app/models/select-option';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { AlertService } from 'src/app/services/alert.service';
import { PeriodsService } from 'src/app/services/periods.service';
import { ReportBuilderService } from 'src/app/services/report-builder.service';
import { features } from 'src/features';
import { AppSettings } from 'src/app/utils/appSettings';
import { EntitySetupResponse } from '../../../../models/entity-setup-response';

const DefaultErrorAlert: Alert = {
  type: 'error',
  message: 'Something went wrong. Please try again later.'
};

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-report-type',
  templateUrl: './report-type.component.html',
  styleUrls: ['./report-type.component.scss']
})
export class ReportTypeComponent implements OnInit, OnDestroy {
  @Input() hasSourceData: boolean = false;
  @Input() entitySetupData: EntitySetupResponse = null;
  @Input() clientDetailsReport: ClientDetailsReport = {
    businessTypeName: null,
    businessType: null,
    businessSubType: null,
    countryRegIn: null,
    clientUUID: null,
    limitedCompanyType: null
  };
  @Input() reportingStandard: ReportingStandard = null;
  @Input() periodUUID: string = null;
  @Input() previousPeriodUUID?: string = null;
  @Input() isLoading: boolean = true;
  @Input() isXbrlEditorModeOn: boolean = false;

  @Output() onReportingStandardChange: EventEmitter<[string, boolean]> = new EventEmitter<[string, boolean]>();

  selectedReportingStandard: ReportingStandard = null;
  dataLoaded: boolean = false;
  selectedReportingStandardName: string;
  reportingStandardObjectData: SelectOption[];
  reportingStandards: ReportingStandard[];
  isGenerateReportButtonDisabled: boolean = true;
  $subscriptions: Subscription[] = [];
  viewAllTemplates = false;
  publicTemplateTag: string = '(display)';
  viewResultsDisabled: boolean = false;
  isViewResultsOn: boolean;
  reportData: ReportData;
  isCIC: boolean = false;
  isGenerateButtonClicked: boolean = false;
  isFRS102Enabled: boolean = false;
  isIFRSEnabled: boolean = false;

  constructor(
    private readonly reportBuilderService: ReportBuilderService,
    private readonly accountsBuilderService: AccountsBuilderService,
    private readonly periodsService: PeriodsService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.isGenerateReportButtonDisabled = !this.hasSourceData || !this.entitySetupData;
    forkJoin([
      this.reportBuilderService.getReportingStandards(),
      features.getState(AppSettings.FeatureFlag.AccountsProduction.Report.ReportTemplates.ViewAll),
      features.getState(AppSettings.FeatureFlag.AccountsProduction.FRS102Enabled),
      features.getState(AppSettings.FeatureFlag.AccountsProduction.IFRSEnabled)
    ])
      .pipe(
        concatMap(([reportingStandards, viewAllTemplates, isFRS102ToggleOn, isIFRSToggleOn]) => {
          this.viewAllTemplates = viewAllTemplates;
          this.isCIC =
            this.clientDetailsReport.businessType === BusinessTypes.Limited &&
            (this.clientDetailsReport.businessSubType === BusinessSubTypes.CommunityInterestCompany ||
              this.clientDetailsReport.businessSubType === BusinessSubTypes.PropertyLettingCIC);
          this.isFRS102Enabled = isFRS102ToggleOn;
          this.isIFRSEnabled = isIFRSToggleOn;

          if (this.reportingStandard) return forkJoin([of(reportingStandards), of(null)]);

          return forkJoin([
            of(reportingStandards),
            this.getPreviousPeriod(this.clientDetailsReport.clientUUID, this.periodUUID)
          ]);
        }),
        concatMap(([reportingStandards, period]) => {
          if (!this.reportingStandard && period?.periodId) {
            return this.accountsBuilderService
              .getPreviousReportData(this.clientDetailsReport.clientUUID, period.periodId)
              .pipe(
                map((previousReportData: ReportData) => {
                  return { reportingStandards, previousReportData };
                }),
                catchError(err => {
                  if (err.status === 404) {
                    return of({ reportingStandards, previousReportData: null });
                  }
                  return throwError(err);
                })
              );
          }

          return of({ reportingStandards, previousReportData: null });
        }),
        catchError(err => {
          this.alertService.showAlert(DefaultErrorAlert);
          return throwError(err);
        }),
        finalize(() => {
          this.dataLoaded = true;
        })
      )
      .subscribe(data => {
        this.initData(data.reportingStandards, data.previousReportData?.reportingStandard?.name);
      });

    this.$subscriptions.push(
      this.accountsBuilderService.reportStatusSubject.subscribe(status => {
        this.viewResultsDisabled = status === ReportStatus.IN_PROGRESS || status === ReportStatus.FAILED;
        this.isGenerateReportButtonDisabled = status === ReportStatus.IN_PROGRESS || !this.hasSourceData;
      })
    );

    this.$subscriptions.push(
      this.accountsBuilderService.triggerGenerateReportSubject.subscribe(
        () => (this.isGenerateReportButtonDisabled = true)
      )
    );

    this.$subscriptions.push(
      this.accountsBuilderService.reportData.subscribe(reportData => {
        this.viewResultsDisabled = !reportData;
        this.reportData = reportData;
      })
    );

    this.$subscriptions.push(
      features.getState(AppSettings.FeatureFlag.Reporting.RawData).subscribe(toggle => (this.isViewResultsOn = toggle))
    );
  }

  private getPreviousPeriod(clientId: string, periodId: string): Observable<AccountProductionPeriod> {
    return this.periodsService.getPeriodList(clientId).pipe(
      map((periods: AccountProductionPeriod[]) => {
        const currentPeriodIndex = periods.findIndex(period => period.periodId === periodId);
        return periods[currentPeriodIndex + 1];
      })
    );
  }

  public initData(reportingStandards: ReportingStandard[], previousReportingStandardNameSelected: string) {
    this.reportingStandards = this.viewAllTemplates
      ? reportingStandards
      : this.getPublicReportingStandardsFilteredByEntitySetup(reportingStandards);

    this.reportingStandardObjectData = this.getReportingStandardData(this.reportingStandards);
    this.setSelectedReportingStandard(previousReportingStandardNameSelected);

    this.updateGenerateReportPayload();
    this.reportingStandardChange(this.selectedReportingStandardName);
  }

  private setSelectedReportingStandard(previousReportingStandardNameSelected: string): void {
    const defaultSelectedReportingStandard =
      this.getReportingStandardByName(this.reportingStandard?.name ?? previousReportingStandardNameSelected) ??
      this.reportingStandards[0];
    this.selectedReportingStandardName = defaultSelectedReportingStandard?.name;
    this.selectedReportingStandard = defaultSelectedReportingStandard;
    this.reportingStandard = defaultSelectedReportingStandard;
  }

  private getPublicReportingStandardsFilteredByEntitySetup(
    reportingStandards: ReportingStandard[]
  ): ReportingStandard[] {
    let reportingTemplateResults;
    reportingTemplateResults = this.entitySetupData?.reportingStandard
      ? reportingStandards.filter(
          reportingStandard =>
            reportingStandard.display &&
            reportingStandard.reportingStandard === this.entitySetupData.reportingStandard &&
            reportingStandard.compatibleCompanyTypes?.includes(this.clientDetailsReport.businessType) &&
            this.companySubTypeFilter(reportingStandard) &&
            (this.isCIC ||
              !reportingStandard.compatibleCompanySubTypes?.includes(BusinessSubTypes.CommunityInterestCompany) ||
              !reportingStandard.compatibleCompanySubTypes?.includes(BusinessSubTypes.PropertyLettingCIC)) &&
            (this.isFRS102Enabled || reportingStandard.reportingStandard !== ReportingStandards.FRS102)
        )
      : [];
    return reportingTemplateResults;
  }

  companySubTypeFilter(reportingStandard: ReportingStandard): boolean {
    if (reportingStandard.compatibleCompanySubTypes === null) {
      return true;
    } else if (this.clientDetailsReport.businessSubType === null) {
      return reportingStandard.compatibleCompanySubTypes?.includes(BusinessSubTypes.Undefined);
    }
    return reportingStandard.compatibleCompanySubTypes?.includes(this.clientDetailsReport.businessSubType);
  }

  private getReportingStandardData(reportingStandards: ReportingStandard[]): SelectOption[] {
    return reportingStandards.map(item => ({
      value: item.id,
      name: item.name
    }));
  }

  private getReportingStandardByName(reportName: string): ReportingStandard {
    return this.reportingStandards.find(c => c.name === reportName);
  }

  private getReportingStandardById(reportId: string): ReportingStandard {
    return this.reportingStandards.find(c => c.id === reportId);
  }

  updateReportingStandard({ detail }): void {
    this.selectedReportingStandard = this.getReportingStandardById(detail);
    this.reportingStandard = this.selectedReportingStandard;
  }

  private reportingStandardChange(value: string): void {
    this.onReportingStandardChange.emit([value, this.isGenerateButtonClicked]);
  }

  triggerGenerateReport(): void {
    this.isGenerateButtonClicked = true;
    this.reportingStandardChange(this.selectedReportingStandard?.name);
    this.updateGenerateReportPayload();
    this.accountsBuilderService.triggerGenerateReportSubject.next();
  }

  removeSubscriptions(): void {
    this.$subscriptions.forEach(sub => {
      sub.unsubscribe();
    });
    this.$subscriptions = [];
  }

  updateGenerateReportPayload(): void {
    const generateReportPayload: GenerateReportPayload = {
      clientId: this.clientDetailsReport.clientUUID,
      businessType: this.clientDetailsReport.businessType,
      periodId: this.periodUUID,
      previousPeriodId: this.previousPeriodUUID,
      reportingStandard: this.selectedReportingStandard
    };
    this.accountsBuilderService.setGenerateReportPayload(generateReportPayload);
  }

  viewResults() {
    if (this.isLoading || this.viewResultsDisabled) {
      return;
    }

    const url = `/financial-performance/results?clientId=${this.clientDetailsReport.clientUUID}&processId=${this.reportData?.lastSuccessfullProcessId}&processType=${this.reportData?.reportingStandard?.reportingStandard}`;
    window.open(url, '_blank');
  }

  ngOnDestroy() {
    this.removeSubscriptions();
  }
}
