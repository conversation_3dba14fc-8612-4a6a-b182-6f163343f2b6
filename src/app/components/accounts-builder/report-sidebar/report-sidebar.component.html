<elements-accounts-builder-component-v01-pkg-sidebar-header
[clientUUID]="clientDetailsReport.clientUUID"
[periodUUID]="periodUUID"
[periodData]="periodData"
[reportingStandard]="reportingStandardValue"
(xbrlEditorModeToggleSubject)="onXbrlEditorModeChanged($event)"
[isXbrlButtonEnabled]="isXbrlButtonEnabled"
></elements-accounts-builder-component-v01-pkg-sidebar-header>
<div class="sidebar-container"  [ngClass]="{ 'xbrl-container' : isXbrlEditorModeOn}">

  <elements-accounts-builder-component-v01-pkg-report-type
    *ngIf="!isXbrlEditorModeOn &&navigationState && navigationState.parent === null; else sectionsList"
    [hasSourceData]="hasSourceData"
    [entitySetupData]="entitySetupData"
    [clientDetailsReport]="clientDetailsReport"
    [periodUUID]="periodUUID"
    [previousPeriodUUID]="previousPeriodUUID"
    [isLoading]="isLoading"
    [reportingStandard]="reportingStandard"
    [isXbrlEditorModeOn]="isXbrlEditorModeOn"
    (onReportingStandardChange)="onReportingStandardChange($event)"
  ></elements-accounts-builder-component-v01-pkg-report-type>

  <ng-template #sectionsList>
    <elements-accounts-builder-component-v01-pkg-report-sections-jsonforms
    *ngIf="isAccountsProductionDataScreenToolEnabled; else currentSectionsList"
    [reportingStandard]="reportingStandard?.reportingStandard"
    [clientUUID]="clientDetailsReport.clientUUID"
    [periodUUID]="periodUUID"
    [previousPeriodUUID]="previousPeriodUUID"
    (isApplicationLoaded)="onApplicationLoaded($event)"
    ></elements-accounts-builder-component-v01-pkg-report-sections-jsonforms>
    <ng-template #currentSectionsList>
      <elements-accounts-builder-component-v01-pkg-report-sections [periodData]="periodData"></elements-accounts-builder-component-v01-pkg-report-sections>
    </ng-template>
  </ng-template>
</div>
