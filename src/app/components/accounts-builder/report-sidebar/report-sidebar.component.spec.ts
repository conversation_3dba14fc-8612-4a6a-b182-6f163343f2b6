import { HttpClientTestingModule } from '@angular/common/http/testing';
import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { BehaviorSubject, of, Subject, Subscription } from 'rxjs';
import { MOCK_REPORT_DATA } from 'src/app/models/report.mock';
import { ReportStatus } from 'src/app/models/report.model';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { ReportSidebarComponent } from './report-sidebar.component';
import { features } from 'src/features';

const MOCK_NAVIGATION_SECTION = {
  label: 'mock section',
  parent: null,
  children: null,
  isMandatory: true
};
describe('ReportTypeComponent', () => {
  let component: ReportSidebarComponent;
  let fixture: ComponentFixture<ReportSidebarComponent>;
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
    'triggerGenerateReport',
    'getReportStatus',
    'getReportData'
  ]);

  const reportNavigationServiceMock = jasmine.createSpyObj('ReportNavigationService', ['']);
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [ReportSidebarComponent],
      providers: [
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        },
        {
          provide: ReportNavigationService,
          useValue: reportNavigationServiceMock
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    reportNavigationServiceMock.currentNavigationNode = new BehaviorSubject(MOCK_NAVIGATION_SECTION);
    accountsBuilderServiceMock.triggerGenerateReport.and.returnValue(of(MOCK_REPORT_DATA.lastSuccessfullProcessId));
    accountsBuilderServiceMock.getReportStatus.and.returnValue(of(ReportStatus.SUCCESS));
    accountsBuilderServiceMock.getReportData.and.returnValue(of(MOCK_REPORT_DATA));
    accountsBuilderServiceMock.triggerGenerateReportSubject = new Subject<any>();
    accountsBuilderServiceMock.reportProcessIdSubject = new Subject<string>();
    accountsBuilderServiceMock.reportStatusSubject = new Subject<string>();

    fixture = TestBed.createComponent(ReportSidebarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set isAccountsProductionDataScreenToolEnabled to true', () => {
    spyOn(features, 'getState').and.returnValue(of(true));
    component.ngOnInit();
    expect(component.isAccountsProductionDataScreenToolEnabled).toBeTrue();
  });

  it('should set isAccountsProductionDataScreenToolEnabled to false', () => {
    spyOn(features, 'getState').and.returnValue(of(false));
    component.ngOnInit();
    expect(component.isAccountsProductionDataScreenToolEnabled).toBeFalse();
  });

  it('should select the perticular section for reporting standard.', () => {
    const reportingStandard = 'FRS101';
    const isReportButtonClicked = false;
    component.onReportingStandardChange([reportingStandard, isReportButtonClicked]);
    expect(component.reportingStandardValue).toEqual(reportingStandard);
  });

  it('should select the perticular section for is reporting button clicked.', () => {
    const reportingStandard = 'FRS101';
    const isXbrlButtonEnabled = true;
    const result = component.isXbrlButtonEnabled;
    component.onReportingStandardChange([reportingStandard, isXbrlButtonEnabled]);
    expect(result).toEqual(isXbrlButtonEnabled);
  });

  it('should set value for isXbrlButtonEnabled to true', () => {
    const isJsonFormsLoaded = true;
    const result = component.isXbrlButtonEnabled;
    component.onApplicationLoaded(isJsonFormsLoaded);
    expect(result).toEqual(isJsonFormsLoaded);
  });

  it('should set xbrlEditorMode to true', () => {
    component.onXbrlEditorModeChanged([true, 1]);
    expect(component.isXbrlEditorModeOn).toBeTrue();
  });

  it('should set xbrlEditorMode to false', () => {
    component.onXbrlEditorModeChanged([false, 1]);
    expect(component.isXbrlEditorModeOn).toBeFalse();
  });

  describe('unsubscribe', () => {
    beforeEach(() => {
      component.$subscriptions.push(new Subscription());
      component.$subscriptions.push(new Subscription());
    });
    it('should remove all subscriptions', () => {
      component.ngOnDestroy();
      expect(component.$subscriptions.length).toBe(0);
    });
  });
});
