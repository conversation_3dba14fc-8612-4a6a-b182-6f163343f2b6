import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BehaviorSubject, Subject, of, throwError } from 'rxjs';
import { Breadcrumb } from 'src/app/models/breadcrumb.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportCustomizationService } from 'src/app/services/report-customization.service';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';

import { SidebarHeaderComponent } from './sidebar-header.component';
import { features } from 'src/features';
import { AppSettings } from 'src/app/utils/appSettings';
import { XbrlMappingsService } from 'src/app/services/xbrl-mappings/xbrl-mappings-service';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReportData } from 'src/app/models/report.model';

const MOCK_SECTIONS_REFERENCE: ReportSection = {
  parent: null,
  label: 'Sections reference',
  isMandatory: false,
  formConfigKey: null,
  children: null,
  errorCount: 0,
  warningCount: 0
};
const MOCK_SECTIONS_BREADCRUMBS: Breadcrumb[] = [
  {
    name: 'Breadcrumbs 1',
    reference: {
      parent: null,
      label: 'Ref 1',
      isMandatory: false,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    }
  },
  {
    name: 'Breadcrumbs 2',
    reference: {
      parent: null,
      label: 'Ref 2',
      isMandatory: false,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    }
  },
  {
    name: 'Breadcrumbs 3',
    reference: {
      parent: null,
      label: 'Ref 3',
      isMandatory: false,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    }
  }
];

const MOCK_REPORT_DATA: any = {
  reportingStandard: {
    type: 'FRS102 1A'
  }
};

const reportNavigationServiceMock = jasmine.createSpyObj('ReportNavigationService', ['navigateToNode']);
const reportCustomizationServiceMock = jasmine.createSpyObj('ReportCustomizationService', ['initSectionsTree']);
const xbrlMappingServiceMock = jasmine.createSpyObj('XbrlMappingsService', ['getClientPeriodTaxonomyId']);
const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', null, {
  reportData: new BehaviorSubject<ReportData>(MOCK_REPORT_DATA)
});

describe('SidebarHeaderComponent', () => {
  reportNavigationServiceMock.breadcrumbsNavigationsLinks = new Subject();
  reportCustomizationServiceMock.reportSectionsTree = new Subject();
  reportCustomizationServiceMock.currentSectionNode = MOCK_SECTIONS_REFERENCE;

  let component: SidebarHeaderComponent;
  let fixture: ComponentFixture<SidebarHeaderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [SidebarHeaderComponent],
      providers: [
        {
          provide: ReportNavigationService,
          useValue: reportNavigationServiceMock
        },
        {
          provide: ReportCustomizationService,
          useValue: reportCustomizationServiceMock
        },
        {
          provide: XbrlMappingsService,
          useValue: xbrlMappingServiceMock
        },
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SidebarHeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('navigateToBreadcrumbLink', () => {
    it('should trigger a navigation to the referenced object', () => {
      MOCK_SECTIONS_BREADCRUMBS.forEach(breadcrumb => {
        component.navigateToBreadcrumbLink(breadcrumb);

        expect(reportNavigationServiceMock.navigateToNode).toHaveBeenCalledWith(breadcrumb.reference);
      });
    });
  });

  describe('navigateToPrevious', () => {
    it('should trigger a navigation to the previous breadcrumb reference', () => {
      component.breadcrumbsLinks = MOCK_SECTIONS_BREADCRUMBS;
      component.navigateToPrevious();

      expect(reportNavigationServiceMock.navigateToNode).toHaveBeenCalledWith(
        MOCK_SECTIONS_BREADCRUMBS[MOCK_SECTIONS_BREADCRUMBS.length - 2].reference
      );
    });
  });

  describe('navigateToSections', () => {
    it('should trigger a navigation to the previous breadcrumb reference', () => {
      component.navigateToSections();

      expect(reportNavigationServiceMock.navigateToNode).toHaveBeenCalledWith(MOCK_SECTIONS_REFERENCE);
    });
  });

  describe('ngOnInit', () => {
    it('should update isAccountsProductionLicenseEnabled and isAccountsProductionsXbrlEditorFeatureEnabled', () => {
      spyOn(features, 'getState').and.callFake(flag => {
        if (flag === AppSettings.FeatureFlag.AccountsProduction.Licensing) {
          return of(true);
        }
        if (flag === AppSettings.FeatureFlag.AccountsProduction.Report.Xbrl.XbrlModeEnabled) {
          return of(true);
        }
      });

      component.ngOnInit();

      expect(features.getState).toHaveBeenCalledWith(AppSettings.FeatureFlag.AccountsProduction.Licensing);
      expect(features.getState).toHaveBeenCalledWith(
        AppSettings.FeatureFlag.AccountsProduction.Report.Xbrl.XbrlModeEnabled
      );
      expect(component.isAccountsProductionLicenseEnabled).toBe(true);
      expect(component.isAccountsProductionsXbrlEditorFeatureEnabled).toBe(true);
    });

    it('should update reportAccountingStandard and call updateXbrlEditorSwitchAvailability', () => {
      xbrlMappingServiceMock.getClientPeriodTaxonomyId.and.returnValue(of(123));

      component.ngOnInit();

      component.xbrlEditorModeToggle$.subscribe(result => {
        expect(result).toEqual([true, 123]);
      });

      accountsBuilderServiceMock.reportData.next(MOCK_REPORT_DATA);
    });

    it('should handle error when fetching taxonomy id', () => {
      xbrlMappingServiceMock.getClientPeriodTaxonomyId.and.returnValue(throwError('error'));

      component.xbrlEditorMode = true;
      component.ngOnInit();

      component.xbrlEditorModeToggle$.subscribe(result => {
        expect(result).toEqual([false, null]);
      });

      accountsBuilderServiceMock.reportData.next(MOCK_REPORT_DATA);

      expect(component.xbrlEditorMode).toBe(false);
    });

    it('should not fetch taxonomy id if xbrlEditorMode is false', () => {
      const spy = spyOn(component.xbrlEditorModeToggle$, 'subscribe');

      component.xbrlEditorMode = false;
      component.ngOnInit();
      accountsBuilderServiceMock.reportData.next(MOCK_REPORT_DATA);

      component.ngOnInit();
      expect(spy).not.toHaveBeenCalled();
    });
  });

  describe('setXbrlButtonStatus', () => {
    it('should dispatch a custom event with the selectedXbrlButton value', () => {
      const selected = true;
      const dispatchEventSpy = spyOn(window, 'dispatchEvent');

      component.dispatchXbrlButtonEvent(selected);

      expect(dispatchEventSpy).toHaveBeenCalledWith(
        new CustomEvent('selectedXbrlButton', {
          detail: { selectedXbrlButton: selected }
        })
      );
    });
  });

  describe('onXbrlEditorModeChanged', () => {
    it('should set xbrlEditorMode to true and emit the new value when checked is true and taxonomy is present', () => {
      const event = { target: { checked: true } };
      const taxonomyId = 123;
      const convertedReportingStandard = 'FRS102';

      xbrlMappingServiceMock.getClientPeriodTaxonomyId.and.returnValue(new BehaviorSubject(taxonomyId));

      component.xbrlEditorModeToggle$.subscribe(result => {
        expect(result).toEqual([true, taxonomyId]);
      });

      component.onXbrlEditorModeChanged(event);

      expect(component.xbrlEditorMode).toBe(true);
      expect(component.accountStandardTaxonomyTuple).toEqual([convertedReportingStandard, taxonomyId]);
      expect(event.target.checked).toBe(true);
    });

    it('should set xbrlEditorMode to the value of event.target.checked and emit the new value when taxonomy is not null or for different accounting standard', () => {
      const event = { target: { checked: false } };
      const taxonomyId = 123;
      component.accountStandardTaxonomyTuple = ['FRS102', taxonomyId];

      component.xbrlEditorModeToggle$.subscribe(result => {
        expect(result).toEqual([false, taxonomyId]);
      });

      component.onXbrlEditorModeChanged(event);

      expect(component.xbrlEditorMode).toBe(false);
      expect(event.target.checked).toBe(false);
    });
  });
});
