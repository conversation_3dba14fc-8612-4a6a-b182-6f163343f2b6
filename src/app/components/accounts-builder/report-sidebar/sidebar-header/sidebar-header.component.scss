@import '../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

$total-tabs-width: 480px;

.report-title-container {
  display: flex;
  gap: $p-1;
  justify-content: space-between;
  align-items: flex-start;
  padding: $p-3 $p-4;
  max-width: $total-tabs-width;

  .report-title {
    h2,
    p {
      margin: 0;
    }

    h2 {
      font-size: 20px;
      font-weight: bold;
      line-height: 1.5;
    }

    p {
      font-weight: bold;
      font-size: 12px;
      color: $grey-light-6;
    }
  }
}

.sidebar-breadcrumbs {
  display: flex;
  gap: 12px;

  p {
    margin: 0;
  }

  .breadcrumbs-node {
    background: none;
    border: none;
    font-size: 14px;
  }

  .breadcrumbs-link {
    color: $iris-link-active;
    text-decoration: underline;
    cursor: pointer;
  }

  .disabled {
    color: black;
    pointer-events: none;
    text-decoration: none;
  }

  .breadcrumbs-label {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.sidebar-navigation-container {
  display: flex;
  padding: $p-2 $p-4;
  border-bottom: 2px solid $grey-light-1;
  max-width: $total-tabs-width;

  .xbrl-editor-toggle-container {
    position: relative;
    left: 10px;
    margin-left: auto;

    &-license-tooltip {
      display: none;
      position: absolute;
      bottom: 163%;
      z-index: 1;
    }
    &:hover {
      .xbrl-editor-toggle-container-license-tooltip {
        display: block;
      }
    }
  }
}
