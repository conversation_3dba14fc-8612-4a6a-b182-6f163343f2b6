import { Component, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { hasLicenseWithValidLevel, hasPermission } from '@iris/platform-ui-core-utility-pkg';
import { forkJoin, Subject, Subscription } from 'rxjs';
import { Breadcrumb } from 'src/app/models/breadcrumb.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { ReportCustomizationService } from 'src/app/services/report-customization.service';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { XbrlMappingsService } from 'src/app/services/xbrl-mappings/xbrl-mappings-service';
import { XbrlToggleService } from 'src/app/services/xbrl-toggle.service';
import { AppSettings } from 'src/app/utils/appSettings';
import { features } from 'src/features';
import { mountRootParcel } from 'single-spa';
import { AccountProductionPeriod } from '../../../../models/period.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-sidebar-header',
  templateUrl: './sidebar-header.component.html',
  styleUrls: ['./sidebar-header.component.scss']
})
export class SidebarHeaderComponent implements OnInit, OnDestroy {
  @Input() clientUUID: string = null;
  @Input() periodUUID: string = null;
  @Input() reportingStandard: string = null;
  @Input() isXbrlButtonEnabled: boolean = true;
  @Output() xbrlEditorModeToggleSubject = new Subject<[xbrlModeOn: boolean, taxonomyId?: number]>();
  @Input() periodData: AccountProductionPeriod = null;

  currentNavigationNode: ReportSection = null;
  sectionsNode: ReportSection = null;
  breadcrumbsLinks: Breadcrumb[] = [];
  mountRootParcel = mountRootParcel;
  reportAccountingStandard?: string = null;
  xbrlEditorModeToggle$ = this.xbrlEditorModeToggleSubject.asObservable();
  xbrlEditorMode: boolean = false;
  hasPremiumOrEnterpriseLicense: boolean;
  permissionCriteria: boolean;
  isAccountsProductionLicenseEnabled: boolean = false;
  isAccountsProductionsXbrlEditorFeatureEnabled: boolean = false;
  accountStandardTaxonomyTuple?: [string, number] = null;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly reportNavigationService: ReportNavigationService,
    private readonly reportCustomizationService: ReportCustomizationService,
    private readonly xbrlMappingService: XbrlMappingsService,
    private readonly accountsBuilderService: AccountsBuilderService,
    private readonly xbrlToggleService: XbrlToggleService
  ) {
    this.subscriptions.push(
      this.reportNavigationService.breadcrumbsNavigationsLinks.subscribe(newBreadcrumbsLinks => {
        this.breadcrumbsLinks = newBreadcrumbsLinks;
        this.currentNavigationNode = newBreadcrumbsLinks[newBreadcrumbsLinks.length - 1].reference;
      })
    );

    this.subscriptions.push(
      this.reportCustomizationService.reportSectionsTree.subscribe(treeUpdate => {
        this.sectionsNode = treeUpdate.children[0];
      })
    );
  }

  ngOnInit(): void {
    this.checkForRolesAndLicenses();

    this.subscriptions.push(
      forkJoin([
        features.getState(AppSettings.FeatureFlag.AccountsProduction.Licensing),
        features.getState(AppSettings.FeatureFlag.AccountsProduction.Report.Xbrl.XbrlModeEnabled)
      ]).subscribe(([isLicenseEnabled, isXbrlModeEnabled]) => {
        this.isAccountsProductionLicenseEnabled = isLicenseEnabled;
        this.isAccountsProductionsXbrlEditorFeatureEnabled = isXbrlModeEnabled;
      })
    );

    this.subscriptions.push(
      this.accountsBuilderService.reportData.subscribe(reportData => {
        const reportDataAny = reportData as any;
        this.reportAccountingStandard = reportDataAny.reportingStandard?.type;

        const convertedReportingStandard = this.convertReportingStandardToTaxonomyCode(this.reportAccountingStandard);

        if (
          this.xbrlEditorMode &&
          this.isTaxonomyNullOrForDifferentAccountingStandardPresent(convertedReportingStandard)
        ) {
          this.subscriptions.push(
            this.xbrlMappingService
              .getClientPeriodTaxonomyId(this.clientUUID, this.periodUUID, convertedReportingStandard)
              .subscribe({
                next: taxonomyId => {
                  this.accountStandardTaxonomyTuple = [convertedReportingStandard, taxonomyId];
                  this.xbrlToggleService.emitXbrlModeChanged(true, taxonomyId);
                },
                error: () => {
                  this.xbrlEditorMode = false;
                  this.xbrlToggleService.emitXbrlModeChanged();
                }
              })
          );
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  checkForRolesAndLicenses(): void {
    this.hasPremiumOrEnterpriseLicense = hasLicenseWithValidLevel(AppSettings.License.Code, [
      AppSettings.License.Level.Premium,
      AppSettings.License.Level.Enterprise
    ]);
    this.permissionCriteria = hasPermission(
      AppSettings.XbrlEditPermission.name,
      AppSettings.XbrlEditPermission.resource.acountsChart.name,
      AppSettings.XbrlEditPermission.resource.acountsChart.action.amendxbrl
    );
  }

  navigateToBreadcrumbLink(selectedBreadcrumb: Breadcrumb): void {
    this.reportNavigationService.navigateToNode(selectedBreadcrumb.reference);
  }

  navigateToPrevious(): void {
    this.reportNavigationService.navigateToNode(this.breadcrumbsLinks[this.breadcrumbsLinks.length - 2].reference);
  }

  navigateToSections(): void {
    this.reportNavigationService.navigateToNode(this.reportCustomizationService.currentSectionNode);
  }

  dispatchXbrlButtonEvent(selected: boolean): void {
    let customEvent = new CustomEvent('selectedXbrlButton', {
      detail: { selectedXbrlButton: selected }
    });
    window.dispatchEvent(customEvent);
  }

  onXbrlEditorModeChanged($event): void {
    const toggleXbrlEditorMode = (xbrlModeOn: boolean = false, taxonomyId?: number) => {
      $event.target.checked = xbrlModeOn;
      this.xbrlEditorMode = xbrlModeOn;
      this.emitXbrlModeChanged(xbrlModeOn, taxonomyId);
      this.xbrlToggleService.emitXbrlModeChanged(xbrlModeOn, taxonomyId);
    };

    const convertedReportingStandard = this.convertReportingStandardToTaxonomyCode(this.reportAccountingStandard);

    if (
      $event.target.checked === true &&
      this.isTaxonomyNullOrForDifferentAccountingStandardPresent(convertedReportingStandard)
    ) {
      this.subscriptions.push(
        this.xbrlMappingService
          .getClientPeriodTaxonomyId(this.clientUUID, this.periodUUID, convertedReportingStandard)
          .subscribe({
            next: taxonomyId => {
              this.accountStandardTaxonomyTuple = [convertedReportingStandard, taxonomyId];
              toggleXbrlEditorMode(true, taxonomyId);
            },
            error: () => {
              toggleXbrlEditorMode();
            }
          })
      );
    } else {
      toggleXbrlEditorMode(
        $event.target.checked,
        this.accountStandardTaxonomyTuple ? this.accountStandardTaxonomyTuple[1] : null
      );
    }
  }

  emitXbrlModeChanged(xbrlModeOn: boolean = false, taxonomyId: number = null): void {
    this.xbrlEditorModeToggleSubject.next([xbrlModeOn, taxonomyId]);
    this.dispatchXbrlButtonEvent(xbrlModeOn);
  }

  isTaxonomyNullOrForDifferentAccountingStandardPresent(reportingStandard: string): boolean {
    return this.accountStandardTaxonomyTuple === null || this.accountStandardTaxonomyTuple[0] !== reportingStandard;
  }

  convertReportingStandardToTaxonomyCode(reportingStandard: string): string {
    if (
      reportingStandard &&
      (reportingStandard.toLowerCase().includes('frs102') || reportingStandard.toLowerCase().includes('frs105'))
    ) {
      return 'FRS102';
    }
    if (reportingStandard && reportingStandard.toLowerCase().includes('ifrs')) {
      return 'IFRS';
    }
    return reportingStandard;
  }
}
