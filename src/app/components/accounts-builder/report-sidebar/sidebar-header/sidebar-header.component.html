<div class="sidebar-navigation-container">
    <nav class="sidebar-breadcrumbs">
        <ng-container *ngFor="let breadcrumb of breadcrumbsLinks; let i = index">
            <iris-icon-only *ngIf="i" type="chevron-right" size="16"></iris-icon-only>
            <span (click)="navigateToBreadcrumbLink(breadcrumb)" class="breadcrumbs-node breadcrumbs-label"
                [ngClass]="{'breadcrumbs-link': i !== breadcrumbsLinks?.length - 1}" [class.disabled]="xbrlEditorMode">{{breadcrumb?.name}}
            </span>
        </ng-container>
        <ng-container *ngIf="breadcrumbsLinks?.length === 1 && sectionsNode?.children !== null">
            <iris-icon-only type="chevron-right" size="16"></iris-icon-only>
            <span (click)="navigateToSections()" class="breadcrumbs-node breadcrumbs-label breadcrumbs-link"[class.disabled]="xbrlEditorMode">Sections
            </span>
        </ng-container>
    </nav>
    <div class="xbrl-editor-toggle-container" *ngIf="isAccountsProductionsXbrlEditorFeatureEnabled;">
        <iris-togglebutton id="xbrl-editor-toggle" value="XBRL Editor" (changed)="onXbrlEditorModeChanged($event)"
            [checked]="xbrlEditorMode" [disabled]="
            !hasPremiumOrEnterpriseLicense ||
            !permissionCriteria ||
            (periodData && periodData.status && periodData.status.name === 'ClientApproval') ||
            !isXbrlButtonEnabled" [rbacDisabled]="hasPremiumOrEnterpriseLicense && !permissionCriteria"></iris-togglebutton>
            <iris-tooltip [active]="!hasPremiumOrEnterpriseLicense" position="bottom" class="xbrl-editor-toggle-container-license-tooltip">Function not available for your subscription</iris-tooltip>
    </div>
</div>
<section class="report-title-container">
    <div class="report-title">
        <h2>{{currentNavigationNode?.label}}</h2>
        <p *ngIf="breadcrumbsLinks?.length === 1">Choose a report</p>
        <p *ngIf="breadcrumbsLinks?.length > 1">{{ reportingStandard }}</p>
    </div>
    <iris-icon *ngIf="breadcrumbsLinks?.length > 1 && sectionsNode?.children !== null && !xbrlEditorMode" shape="circle" size="28"
        type="chevron-left" background-colour="#ffffff" icon-colour="black" padding="0"
        (click)="navigateToPrevious()"></iris-icon>
    <iris-icon *ngIf="breadcrumbsLinks?.length === 1 && sectionsNode?.children !== null && !xbrlEditorMode" shape="circle" size="28"
        type="chevron-right" background-colour="#ffffff" icon-colour="black" padding="0"
        (click)="navigateToSections()"></iris-icon>
</section>
