import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Breadcrumb } from 'src/app/models/breadcrumb.model';
import {
  AnalysisOfCostOrValuation,
  AnalysisOfCostOrValuationItem,
  NotesFormTypeEnum,
  TangibleFixedAssetsNotes
} from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { ReportNotesService } from 'src/app/services/report-notes.service';
import * as cloneDeep from 'lodash/cloneDeep';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-analysis-of-cost-valuation',
  templateUrl: './analysis-of-cost-valuation.component.html',
  styleUrls: ['./analysis-of-cost-valuation.component.scss']
})
export class AnalysisOfCostValuationComponent implements OnInit {
  @Input() sectionReference: ReportSection;

  MIN_COST_LENGTH = 0;
  MAX_COST_LENGTH = ***********.99;

  MIN_TOTAL_LENGTH = -*************.99;
  MAX_TOTAL_LENGTH = *************.99;

  analysisOfCostOrValuationForm: UntypedFormGroup;
  tangibleFixedAssetsNotes: TangibleFixedAssetsNotes;

  isVisible: boolean = true;
  readonly: boolean = true;
  items: Array<number> = Array.from({ length: 10 }, (_, i) => i + 1);
  breadcrumbsLinks: Breadcrumb[] = [];
  lastFilledRowInOrderIndex: number = 0;
  selectedYearInputRowIndex: number = 0;

  alertIconColor: string = '#C8102E';

  constructor(
    private _formBuilder: UntypedFormBuilder,
    private reportNavigationService: ReportNavigationService,
    private reportNotesService: ReportNotesService
  ) {}

  ngOnInit() {
    this.initData();
    this.buildForm();
  }

  initData() {
    this.reportNavigationService.breadcrumbsNavigationsLinks.subscribe(newBreadcrumbsLinks => {
      this.breadcrumbsLinks = newBreadcrumbsLinks;
    });
    const notesName = NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES;
    this.tangibleFixedAssetsNotes = cloneDeep(this.reportNotesService.getReportNotesData(notesName));
    if (!this.tangibleFixedAssetsNotes) {
      this.tangibleFixedAssetsNotes = this.getNewInstance();
    } else if (!this.tangibleFixedAssetsNotes.analysisOfCostOrValuation) {
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation = this.getNewAnalysisOfCostInstance();
    }
    this.getLastFilledRowInOrderIndex();
  }

  buildForm() {
    this.analysisOfCostOrValuationForm = this._formBuilder.group({
      costLandAndBuildings: [this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costLandAndBuildings],
      costPlantAndMachineryEtc: [this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costPlantAndMachineryEtc],
      totalLandAndBuildings: [this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings],
      totalPlantAndMachineryEtc: [this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc],
      analysisOfCostOrValuationItems: [
        this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems
      ]
    });
  }

  get isAnalysisOfCostOrValuationMandatory() {
    return (
      !!this.tangibleFixedAssetsNotes.historicalCostBreakdown?.revaluedAssetClass &&
      !!this.tangibleFixedAssetsNotes.historicalCostBreakdown?.revaluedClassPronoun
    );
  }

  get isFormInvalid() {
    return (
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings < 0 ||
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc < 0
    );
  }

  getAnalysisOfCostValuationItem(index: number): AnalysisOfCostOrValuationItem {
    return this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.find(
      c => c.index == index
    );
  }

  calculateLandAndBuildingsTotal() {
    if (
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.length === 0 &&
      !this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costLandAndBuildings
    ) {
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings = null;
      return;
    }

    let totalLandAndBuildings = 0;

    this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.forEach(e => {
      totalLandAndBuildings += e.landAndBuildings;
    });

    totalLandAndBuildings += this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costLandAndBuildings;

    this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings = +totalLandAndBuildings.toFixed(2);
  }

  calculatePlantAndMachineryTotal() {
    if (
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.length === 0 &&
      !this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costPlantAndMachineryEtc
    ) {
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc = null;
      return;
    }

    let totalPlantAndMachineryEtc = 0;

    this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.forEach(e => {
      totalPlantAndMachineryEtc += e.plantAndMachineryEtc;
    });

    totalPlantAndMachineryEtc += this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costPlantAndMachineryEtc;

    this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc =
      +totalPlantAndMachineryEtc.toFixed(2);
  }

  getLastFilledRowInOrderIndex(): void {
    this.lastFilledRowInOrderIndex = 0;
    for (
      let i = 1;
      i < this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems.length + 1;
      i++
    ) {
      if (
        this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems?.find(
          c => c.index == i
        )
      ) {
        this.lastFilledRowInOrderIndex = i;
      } else {
        break;
      }
    }
  }

  isMissingRow(): boolean {
    return (
      this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems.length > 0 &&
      !(
        this.lastFilledRowInOrderIndex ==
        this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems.length
      )
    );
  }

  isMissingYear(): boolean {
    return (
      this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems?.length >= 0 &&
      this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems?.some(
        c => c.index != this.selectedYearInputRowIndex && (c.year <= 999 || c.year == null)
      )
    );
  }

  itemFormChanges(newValues) {
    const newAnalysisOfCostValuationItem: AnalysisOfCostOrValuationItem = {
      index: newValues.index,
      year: newValues.year,
      landAndBuildings: newValues.landAndBuildings,
      plantAndMachineryEtc: newValues.plantAndMachineryEtc
    };

    let analysisOfCostOrValuationItem =
      this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems?.find(
        c => c.index == newAnalysisOfCostValuationItem.index
      );

    if (!analysisOfCostOrValuationItem) {
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.push(
        newAnalysisOfCostValuationItem
      );

      this.getLastFilledRowInOrderIndex();
    } else if (
      newValues.year === null &&
      newValues.landAndBuildings === null &&
      newValues.plantAndMachineryEtc === null
    ) {
      const index = this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.analysisOfCostOrValuationItems?.findIndex(
        c => c.index == newAnalysisOfCostValuationItem.index
      );
      this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.splice(index, 1);

      this.getLastFilledRowInOrderIndex();
    } else {
      analysisOfCostOrValuationItem.year = newAnalysisOfCostValuationItem.year;
      analysisOfCostOrValuationItem.landAndBuildings = newAnalysisOfCostValuationItem.landAndBuildings;
      analysisOfCostOrValuationItem.plantAndMachineryEtc = newAnalysisOfCostValuationItem.plantAndMachineryEtc;
    }
    this.calculateLandAndBuildingsTotal();
    this.calculatePlantAndMachineryTotal();
  }

  onYearInputChange(indexOfSelectedRow) {
    this.selectedYearInputRowIndex = indexOfSelectedRow;
  }

  closeModalHandler(event) {
    this.isVisible = false;
    this.navigateToPrevious();
  }

  navigateToPrevious(): void {
    this.reportNavigationService.navigateToNode(this.breadcrumbsLinks[this.breadcrumbsLinks.length - 2].reference);
  }

  updateLandAndBuildingsCost({ detail }) {
    this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costLandAndBuildings = +detail;
    this.calculateLandAndBuildingsTotal();
    this.analysisOfCostOrValuationForm.patchValue({
      costLandAndBuildings: this.getNumberOrNull(detail)
    });
  }

  updatePlantAndMachineryEtcCost({ detail }) {
    this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costPlantAndMachineryEtc = +detail;
    this.calculatePlantAndMachineryTotal();
    this.analysisOfCostOrValuationForm.patchValue({
      costPlantAndMachineryEtc: this.getNumberOrNull(detail)
    });
  }

  saveAnalysisOfCost() {
    this.selectedYearInputRowIndex = 0;

    if (!this.isMissingYear()) {
      let hasError =
        this.isAnalysisOfCostOrValuationMandatory &&
        !this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings &&
        !this.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc;

      this.reportNotesService.updateReportNotesDataValues(
        this.tangibleFixedAssetsNotes,
        this.sectionReference,
        hasError,
        null,
        NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
      );
      this.isVisible = false;
      this.navigateToPrevious();
    }
  }

  private getNumberOrNull(value): number {
    return value?.length && !isNaN(value) ? +value : null;
  }

  private getNewInstance(): TangibleFixedAssetsNotes {
    return {
      analysisOfCostOrValuation: this.getNewAnalysisOfCostInstance()
    };
  }

  private getNewAnalysisOfCostInstance(): AnalysisOfCostOrValuation {
    return {
      analysisOfCostOrValuationItems: [],
      costLandAndBuildings: null,
      costPlantAndMachineryEtc: null,
      totalLandAndBuildings: null,
      totalPlantAndMachineryEtc: null
    };
  }
}
