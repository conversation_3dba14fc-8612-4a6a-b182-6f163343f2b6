import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { AnalysisOfCostOrValuationItem } from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { AnalysisOfCostValuationItemComponent } from './analysis-of-cost-valuation-item.component';
import * as cloneDeep from 'lodash/cloneDeep';

describe('AnalysisOfCostValuationItemComponent', () => {
  let component: AnalysisOfCostValuationItemComponent;
  let fixture: ComponentFixture<AnalysisOfCostValuationItemComponent>;
  const MOCK_ANALYSIS_OF_COST_DATA: AnalysisOfCostOrValuationItem = {
    index: 1,
    year: 2020,
    landAndBuildings: 100,
    plantAndMachineryEtc: 200
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AnalysisOfCostValuationItemComponent, CommaDelimiterPipe],
      providers: [UntypedFormBuilder]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AnalysisOfCostValuationItemComponent);
    component = fixture.componentInstance;
    component.analysisOfCostOrValuationItem = MOCK_ANALYSIS_OF_COST_DATA;
    component.lastFilledInOrderRowIndex = 8;
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('updateFormValue', () => {
    it('should update the form value', () => {
      const MOCK_ANALYSIS_OF_COST: AnalysisOfCostOrValuationItem = {
        index: 1,
        year: 2004,
        landAndBuildings: 2,
        plantAndMachineryEtc: 5
      };
      spyOn(component.onFormChanges, 'emit');
      component.updateFormValue(MOCK_ANALYSIS_OF_COST);
      expect(component.onFormChanges.emit).toHaveBeenCalledWith(MOCK_ANALYSIS_OF_COST);
    });
  });

  describe('onYearInputSelected', () => {
    it('should emit an index value when selected', () => {
      spyOn(component.onYearSelected, 'emit');
      component.onYearInputSelected();
      expect(component.onYearSelected.emit).toHaveBeenCalledWith(component.index);
    });
  });

  describe('onYearInputUnselected', () => {
    it('should emit an index value of zero when unselected', () => {
      spyOn(component.onYearSelected, 'emit');
      component.onYearInputUnselected();
      expect(component.onYearSelected.emit).toHaveBeenCalledWith(0);
    });
  });

  describe('updateYearValue', () => {
    it('should replace the year with new value', () => {
      const patchDescriptionSpy = spyOn(component.analysisOfCostValuationForm, 'patchValue');
      const value = '2004';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateYear({
        detail: value
      });
      expect(patchDescriptionSpy).toHaveBeenCalledWith({ year: MOCK_EVENT.detail });
    });
    it('should call onFormChanges with new year value', () => {
      spyOn(component.onFormChanges, 'emit');
      const value = '2023';
      component.updateYear({
        detail: value
      });
      expect(component.onFormChanges.emit).toHaveBeenCalledWith({
        index: null,
        year: 2023,
        landAndBuildings: 100,
        plantAndMachineryEtc: 200
      });
    });
    it('should replace the year with null value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostValuationForm, 'patchValue');
      const value = null;
      const MOCK_EVENT = {
        detail: value
      };
      component.updateYear({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ year: MOCK_EVENT.detail });
    });
    it('should call onFormChanges with year set as null', () => {
      spyOn(component.onFormChanges, 'emit');
      component.updateYear({
        detail: null
      });
      expect(component.onFormChanges.emit).toHaveBeenCalledWith({
        index: null,
        year: null,
        landAndBuildings: 100,
        plantAndMachineryEtc: 200
      });
    });
  });

  describe('updatePlantAndMachineryEtc', () => {
    it('should replace plantAndMachineryEtc with the new value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostValuationForm, 'patchValue');
      const value = '5';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updatePlantAndMachineryEtc({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ plantAndMachineryEtc: MOCK_EVENT.detail });
    });
    it('should call onFormChanges with new plantAndMachineryEtc value', () => {
      spyOn(component.onFormChanges, 'emit');
      const value = '500';
      component.updatePlantAndMachineryEtc({
        detail: value
      });
      expect(component.onFormChanges.emit).toHaveBeenCalledWith({
        index: null,
        year: 2020,
        landAndBuildings: 100,
        plantAndMachineryEtc: 500
      });
    });
    it('should replace plantAndMachineryEtc with null value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostValuationForm, 'patchValue');
      const value = null;
      const MOCK_EVENT = {
        detail: value
      };
      component.updatePlantAndMachineryEtc({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ plantAndMachineryEtc: MOCK_EVENT.detail });
    });
    it('should call onFormChanges with plantAndMachineryEtc set as null', () => {
      spyOn(component.onFormChanges, 'emit');
      component.updatePlantAndMachineryEtc({
        detail: null
      });
      expect(component.onFormChanges.emit).toHaveBeenCalledWith({
        index: null,
        year: 2020,
        landAndBuildings: 100,
        plantAndMachineryEtc: null
      });
    });
  });

  describe('updateLandAndBuilding', () => {
    it('should replace landAndBuilding with the new value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostValuationForm, 'patchValue');
      const value = '2';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateLandAndBuildings({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ landAndBuildings: MOCK_EVENT.detail });
    });
    it('should call onFormChanges with new landAndBuilding value', () => {
      spyOn(component.onFormChanges, 'emit');
      const value = '500';
      component.updateLandAndBuildings({
        detail: value
      });
      expect(component.onFormChanges.emit).toHaveBeenCalledWith({
        index: null,
        year: 2020,
        landAndBuildings: 500,
        plantAndMachineryEtc: 200
      });
    });
    it('should replace landAndBuilding with null value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostValuationForm, 'patchValue');
      const value = null;
      const MOCK_EVENT = {
        detail: value
      };
      component.updateLandAndBuildings({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ landAndBuildings: MOCK_EVENT.detail });
    });
    it('should call onFormChanges with landAndBuilding set as null', () => {
      spyOn(component.onFormChanges, 'emit');
      component.updateLandAndBuildings({
        detail: null
      });
      expect(component.onFormChanges.emit).toHaveBeenCalledWith({
        index: null,
        year: 2020,
        landAndBuildings: null,
        plantAndMachineryEtc: 200
      });
    });
  });

  describe('isRowUnderMissingRow', () => {
    it('should show that under the last missing row with landAndBuildings', () => {
      component.lastFilledInOrderRowIndex = 2;
      component.analysisOfCostOrValuationItem = cloneDeep({
        index: 10,
        year: 0,
        landAndBuildings: 100,
        plantAndMachineryEtc: null
      });

      expect(component.isRowUnderMissingRow()).toBeTrue();
    });

    it('should show that under the last missing row with plantAndMachineryEtc', () => {
      component.lastFilledInOrderRowIndex = 2;
      component.analysisOfCostOrValuationItem = cloneDeep({
        index: 10,
        year: 0,
        landAndBuildings: null,
        plantAndMachineryEtc: 100
      });

      expect(component.isRowUnderMissingRow()).toBeTrue();
    });

    it('should show that under the last missing row with year', () => {
      component.lastFilledInOrderRowIndex = 2;
      component.analysisOfCostOrValuationItem = cloneDeep({
        index: 10,
        year: 2020,
        landAndBuildings: null,
        plantAndMachineryEtc: null
      });

      expect(component.isRowUnderMissingRow()).toBeTrue();
    });

    it('should note show that under the last missing row with negative values', () => {
      component.lastFilledInOrderRowIndex = 2;
      component.analysisOfCostOrValuationItem = cloneDeep({
        index: 10,
        year: -1,
        landAndBuildings: -1,
        plantAndMachineryEtc: -1
      });

      expect(component.isRowUnderMissingRow()).toBeFalse();
    });

    it('should show that above the last missing row with plantAndMachineryEtc', () => {
      component.analysisOfCostOrValuationItem = cloneDeep({
        index: 6,
        year: 0,
        landAndBuildings: null,
        plantAndMachineryEtc: 0
      });

      component.lastFilledInOrderRowIndex = 8;

      expect(component.isRowUnderMissingRow()).toBeFalse();
    });

    it('should show that above the last missing row with landAndBuildings', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 0,
        landAndBuildings: 0,
        plantAndMachineryEtc: null
      };

      component.lastFilledInOrderRowIndex = 7;

      expect(component.isRowUnderMissingRow()).toBeFalse();
    });

    it('should show that above the last missing row with year', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 2020,
        landAndBuildings: null,
        plantAndMachineryEtc: null
      };

      component.lastFilledInOrderRowIndex = 7;

      expect(component.isRowUnderMissingRow()).toBeFalse();
    });

    it('should return false as form is null', () => {
      component.analysisOfCostOrValuationItem = null;

      component.lastFilledInOrderRowIndex = 8;

      expect(component.isRowUnderMissingRow()).toBeFalse();
    });

    it('should return false as form is empty', () => {
      component.analysisOfCostOrValuationItem = {
        index: null,
        year: null,
        landAndBuildings: null,
        plantAndMachineryEtc: null
      };
      component.lastFilledInOrderRowIndex = 8;

      expect(component.isRowUnderMissingRow()).toBeFalse();
    });
  });

  describe('isYearMissing', () => {
    it('should show year is missing with negative year', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: -1,
        landAndBuildings: 100,
        plantAndMachineryEtc: 100
      };
      expect(component.isYearMissing()).toBeTrue();
    });

    it('should show year is missing with negative year and negative values', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: -1,
        landAndBuildings: -1,
        plantAndMachineryEtc: -1
      };
      expect(component.isYearMissing()).toBeTrue();
    });

    it('should show year is missing with landAndBuildings', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 0,
        landAndBuildings: 99,
        plantAndMachineryEtc: null
      };
      expect(component.isYearMissing()).toBeTrue();
    });

    it('should show year is missing with plantAndMachineryEtc', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 0,
        landAndBuildings: null,
        plantAndMachineryEtc: 99
      };
      expect(component.isYearMissing()).toBeTrue();
    });

    it('should show year is missing with both', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 0,
        landAndBuildings: 100,
        plantAndMachineryEtc: 100
      };
      expect(component.isYearMissing()).toBeTrue();
    });

    it('should show year is not missing with plantAndMachineryEtc', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 2020,
        landAndBuildings: 0,
        plantAndMachineryEtc: 100
      };
      expect(component.isYearMissing()).toBeFalse();
    });

    it('should show year is not missing with landAndBuildings', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 2020,
        landAndBuildings: 100,
        plantAndMachineryEtc: null
      };
      expect(component.isYearMissing()).toBeFalse();
    });

    it('should show year is not missing with both', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 2020,
        landAndBuildings: 100,
        plantAndMachineryEtc: 100
      };
      expect(component.isYearMissing()).toBeFalse();
    });

    it('should not show year is missing with negative values', () => {
      component.analysisOfCostOrValuationItem = {
        index: 6,
        year: 2020,
        landAndBuildings: -1,
        plantAndMachineryEtc: -1
      };
      expect(component.isYearMissing()).toBeFalse();
    });
  });
});
