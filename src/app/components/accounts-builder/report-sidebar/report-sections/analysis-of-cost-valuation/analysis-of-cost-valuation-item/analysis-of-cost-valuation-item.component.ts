import { Component, EventEmitter, Input, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { AnalysisOfCostOrValuationItem } from 'src/app/models/report-sections/report-notes.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-analysis-of-cost-valuation-item',
  templateUrl: './analysis-of-cost-valuation-item.component.html',
  styleUrls: ['./analysis-of-cost-valuation-item.component.scss']
})
export class AnalysisOfCostValuationItemComponent {
  @Input() index: number;
  @Input() analysisOfCostOrValuationItem: AnalysisOfCostOrValuationItem;
  @Input() lastFilledInOrderRowIndex: number;

  @Output() onFormChanges: EventEmitter<AnalysisOfCostOrValuationItem> =
    new EventEmitter<AnalysisOfCostOrValuationItem>();

  @Output() onYearSelected: EventEmitter<number> = new EventEmitter<number>();

  MIN_YEAR_LENGTH = 0;
  MAX_YEAR_LENGTH = 9999;

  MIN_VALUATION_LENGTH = -***********.99;
  MAX_VALUATION_LENGTH = ***********.99;

  analysisOfCostValuationForm: UntypedFormGroup;

  constructor(private _formBuilder: UntypedFormBuilder) {}

  ngOnInit(): void {
    this.analysisOfCostValuationForm = this._formBuilder.group({
      landAndBuildings: [this.analysisOfCostOrValuationItem?.landAndBuildings],
      plantAndMachineryEtc: [this.analysisOfCostOrValuationItem?.plantAndMachineryEtc],
      year: [this.analysisOfCostOrValuationItem?.year],
      index: [this.index]
    });

    this.isYearMissing();

    this.analysisOfCostValuationForm.valueChanges.subscribe(newValues => {
      this.updateFormValue(newValues);
    });
  }

  updateFormValue(analysisOfCostOrValuationItem: AnalysisOfCostOrValuationItem) {
    this.onFormChanges.emit(analysisOfCostOrValuationItem);
  }

  isRowUnderMissingRow() {
    return (
      this.analysisOfCostOrValuationItem?.index > this.lastFilledInOrderRowIndex &&
      (this.analysisOfCostOrValuationItem?.year > 0 ||
        this.analysisOfCostOrValuationItem?.landAndBuildings >= 0 ||
        this.analysisOfCostOrValuationItem?.plantAndMachineryEtc >= 0)
    );
  }

  isYearMissing() {
    return (
      (this.analysisOfCostOrValuationItem?.landAndBuildings != 0 ||
        this.analysisOfCostOrValuationItem?.plantAndMachineryEtc != 0) &&
      this.analysisOfCostOrValuationItem?.year <= 999
    );
  }

  updateYear({ detail }) {
    this.analysisOfCostValuationForm.patchValue({
      year: this.getNumberOrNull(detail)
    });
  }

  updateLandAndBuildings({ detail }) {
    this.analysisOfCostValuationForm.patchValue({
      landAndBuildings: this.getNumberOrNull(detail)
    });
  }

  updatePlantAndMachineryEtc({ detail }) {
    this.analysisOfCostValuationForm.patchValue({
      plantAndMachineryEtc: this.getNumberOrNull(detail)
    });
  }

  private getNumberOrNull(value): number {
    return value?.length && !isNaN(value) ? +value : null;
  }

  onYearInputSelected() {
    this.onYearSelected.emit(this.index);
  }

  onYearInputUnselected() {
    this.onYearSelected.emit(0);
  }
}
