<div class="item-table-body">
    <div class="table-first">
        <span><b>{{ index }}.</b> Valuation in</span>
    </div>
    <div class="table-second current-value-input">
        <iris-numeric-input
        [value]="analysisOfCostOrValuationItem?.year"
        max-decimals="0"
        digit-group-separator= ''
        (valueChanged)="updateYear($event)"
        [colour]="isRowUnderMissingRow() || isYearMissing() ? 'error-input' : 'default'"
        [minValue]="MIN_YEAR_LENGTH"
        [maxValue]="MAX_YEAR_LENGTH"
        (focusout)="onYearInputUnselected()"
        (focusin)="onYearInputSelected()"
        placeholder-value="Year"
        ></iris-numeric-input>
    </div>
    <div class="table-third analysis-cost-input">
        <iris-numeric-input
        [value]="analysisOfCostOrValuationItem?.landAndBuildings"
        [colour]="isRowUnderMissingRow() ? 'error-input': 'default'"
        (valueChanged)="updateLandAndBuildings($event)"
        [minValue]="MIN_VALUATION_LENGTH"
        [maxValue]="MAX_VALUATION_LENGTH"
        placeholder-value="£0.00"
        currency-symbol="£"></iris-numeric-input> 
    </div>
    <div class="table-fourth analysis-cost-input">
        <iris-numeric-input
        [value]="analysisOfCostOrValuationItem?.plantAndMachineryEtc"
        (valueChanged)="updatePlantAndMachineryEtc($event)"
        [colour]="isRowUnderMissingRow()? 'error-input' : 'default'"
        [minValue]="MIN_VALUATION_LENGTH"
        [maxValue]="MAX_VALUATION_LENGTH"
        placeholder-value="£0.00"
        currency-symbol="£"></iris-numeric-input>
    </div>
</div>