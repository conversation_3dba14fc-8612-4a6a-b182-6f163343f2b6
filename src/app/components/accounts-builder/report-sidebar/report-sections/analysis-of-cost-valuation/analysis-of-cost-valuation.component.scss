@import '../../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';
@import '../../../../../../assets/mixins';

.modal {
  padding-bottom: $p-4;
  .modal-content {
    height: 70vh;
    width: 55vw;
    display: flex;
    .analysis-table {
      @include custom-scrollbar;
      height: calc(100vh - 330px);
      overflow-y: auto;
      flex-basis: 100%;
      .analysis-table-header {
        display: flex;
        margin: $p-2 0;
        font-weight: bold;
        .table-first {
          flex-basis: 70%;
          text-align: right;
          span {
            padding-right: 20.5%;
          }
        }
        .table-second {
          flex-basis: 19%;
        }
      }
      .table-cost {
        display: flex;
        margin: $p-2 0;
        font-weight: bold;
        .table-first {
          position: relative;
          flex-basis: 41.5%;

          span {
            position: absolute;
            top: $p-1;
          }
        }
        .table-second {
          flex-basis: 27%;
        }
        .table-third {
          flex-basis: 27%;
          margin-left: $p-2;
          margin-right: $p-1;
        }
      }
      .issue-detail-header {
        display: flex;
        flex-direction: row;
        flex-basis: 100%;
        padding: $p-2;
        margin-bottom: $p-1;
        padding-right: $p-1;
        font-size: 14px;
        font-weight: 700;
        color: $iris-red;
        background-color: #fae7ea;

        .header-desciption {
          padding-left: $p-2;
          color: black;
        }

        .issue-detail-header-item {
          margin-left: auto;
          margin-right: 0px;
          color: $grey-light-6;
          cursor: pointer;
        }
      }
    }

    iris-numeric-input {
      ::ng-deep input.error-input:disabled {
        border: 2px solid $iris-red;
      }
    }
  }
}

.modal-footer {
  padding-bottom: $p-4;
  .actions {
    display: flex;
    float: right;
    margin-right: $p-3;
    .btn-class {
      margin-right: $p-2;
      margin-top: $p-1;
      font-size: $p-2;
      color: $iris-blue;
      cursor: pointer;
      text-decoration: underline;
    }
  }
}
