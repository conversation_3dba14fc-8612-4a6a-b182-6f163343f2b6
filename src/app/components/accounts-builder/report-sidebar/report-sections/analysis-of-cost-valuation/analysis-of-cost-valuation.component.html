<iris-modal close-icon="x" [visible]="isVisible" close-icon="x" (close)="closeModalHandler($event)"
    header="Analysis of cost or valuation">
    <div slot="content" class="modal">
        <div class="modal-content">
            <div class="analysis-table">
                <div class="issue-detail-header" *ngIf = "isMissingRow()">
                    <iris-icon-only type="alert-triangle"
                      [colour]="alertIconColor" 
                      [size]="22">
                    </iris-icon-only>
                    <span class="header-desciption">
                        Years must be completed in order.
                    </span>
                </div>
                <div class="issue-detail-header" *ngIf = "isMissingYear()">
                    <iris-icon-only type="alert-triangle"
                      [colour]="alertIconColor" 
                      [size]="22">
                    </iris-icon-only>
                    <span class="header-desciption">
                        The year column must be completed to allow data to be processed correctly.
                    </span>
                </div>
                <div class="analysis-table-header">
                    <div class="table-first">
                        <span>Land and buildings</span>
                    </div>
                    <div class="table-second">
                        <span>Plant and machinery etc.</span>
                    </div>
                </div>
                <div>
                    <elements-accounts-builder-component-v01-pkg-analysis-of-cost-valuation-item
                        *ngFor="let item of items" [index]="item"
                        [lastFilledInOrderRowIndex] = "lastFilledRowInOrderIndex"
                        [analysisOfCostOrValuationItem]="getAnalysisOfCostValuationItem(item)"
                        (onFormChanges)="itemFormChanges($event)"
                        (onYearSelected) = "onYearInputChange($event)">
                    </elements-accounts-builder-component-v01-pkg-analysis-of-cost-valuation-item>
                </div>
                <div class="table-cost analysis-cost-input">
                    <div class="table-first">
                        <span>Cost</span>
                    </div>
                    <div class="table-second analysis-cost-input">
                        <iris-numeric-input
                            [value]="tangibleFixedAssetsNotes.analysisOfCostOrValuation.costLandAndBuildings"
                            (valueChanged)="updateLandAndBuildingsCost($event)" [minValue]="MIN_COST_LENGTH"
                            [maxValue]="MAX_COST_LENGTH" placeholder-value="£0.00"
                            currency-symbol="£"></iris-numeric-input>
                    </div>
                    <div class="table-third analysis-cost-input">
                        <iris-numeric-input
                            [value]="tangibleFixedAssetsNotes.analysisOfCostOrValuation.costPlantAndMachineryEtc"
                            (valueChanged)="updatePlantAndMachineryEtcCost($event)" [minValue]="MIN_COST_LENGTH"
                            [maxValue]="MAX_COST_LENGTH" placeholder-value="£0.00"
                            currency-symbol="£"></iris-numeric-input>
                    </div>
                </div>
                <div class="table-cost">
                    <div class="table-first">
                        <span>Total</span>
                    </div>
                    <div class="table-second analysis-cost-input">
                        <iris-numeric-input
                            [value]="tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings"
                            [colour]="tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings < 0 ? 'error-input' : 'default'"
                            [minValue]="MIN_TOTAL_LENGTH" [maxValue]="MAX_TOTAL_LENGTH" placeholder-value="£0.00"
                            currency-symbol="£" controlled="true" [disabled]="readonly"></iris-numeric-input>
                    </div>
                    <div class="table-third analysis-cost-input">
                        <iris-numeric-input
                            [value]="tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc"
                            [colour]="tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc < 0 ? 'error-input' : 'default'"
                            [minValue]="MIN_TOTAL_LENGTH" [maxValue]="MAX_TOTAL_LENGTH" placeholder-value="£0.00"
                            currency-symbol="£" controlled="true" [disabled]="readonly"></iris-numeric-input>
                    </div>
                </div>
            </div>

        </div>
        <div class="modal-footer">
            <div class="actions">
                <div class="btn-class">
                    <a type="button" colour="secondary"
                     (click)="closeModalHandler($event)"> Cancel </a>
                </div>
                <div>
                    <iris-button type="button"
                    [disabled]="isFormInvalid || isMissingRow() || isMissingYear()" (click)="saveAnalysisOfCost()"> Save </iris-button>
                </div>
            </div>
        </div>
    </div>
</iris-modal>