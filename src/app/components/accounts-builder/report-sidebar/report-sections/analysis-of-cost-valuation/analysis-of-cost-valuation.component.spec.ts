import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { Subject } from 'rxjs';
import { Breadcrumb } from 'src/app/models/breadcrumb.model';
import * as cloneDeep from 'lodash/cloneDeep';
import { AnalysisOfCostOrValuation, NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { ReportNotesService } from 'src/app/services/report-notes.service';

import { AnalysisOfCostValuationComponent } from './analysis-of-cost-valuation.component';

const MOCK_SECTIONS_BREADCRUMBS: Breadcrumb[] = [
  {
    name: 'Breadcrumbs 1',
    reference: {
      parent: null,
      label: 'Ref 1',
      isMandatory: false,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    }
  },
  {
    name: 'Breadcrumbs 2',
    reference: {
      parent: null,
      label: 'Ref 2',
      isMandatory: false,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    }
  },
  {
    name: 'Breadcrumbs 3',
    reference: {
      parent: null,
      label: 'Ref 3',
      isMandatory: false,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    }
  }
];

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'getReportNotesData',
  'updateReportNotesDataValues'
]);

const reportNavigationServiceMock = jasmine.createSpyObj('ReportNavigationService', ['navigateToNode']);

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.ANALYSIS_OF_COST_VALUATION,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_ANALYSIS_OF_COST_VALUATION: AnalysisOfCostOrValuation = {
  costLandAndBuildings: 1,
  costPlantAndMachineryEtc: 2,
  totalLandAndBuildings: 4,
  totalPlantAndMachineryEtc: 5,
  analysisOfCostOrValuationItems: [
    {
      index: 1,
      year: 2004,
      landAndBuildings: 1,
      plantAndMachineryEtc: 1
    },
    {
      index: 2,
      year: 2005,
      landAndBuildings: 2,
      plantAndMachineryEtc: 2
    }
  ]
};

describe('AnalysisOfCostValuationComponent', () => {
  let component: AnalysisOfCostValuationComponent;
  let fixture: ComponentFixture<AnalysisOfCostValuationComponent>;
  reportNavigationServiceMock.breadcrumbsNavigationsLinks = new Subject();
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AnalysisOfCostValuationComponent],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        {
          provide: ReportNavigationService,
          useValue: reportNavigationServiceMock
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AnalysisOfCostValuationComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    component.breadcrumbsLinks = MOCK_SECTIONS_BREADCRUMBS;
    component.lastFilledRowInOrderIndex = 2;
    reportNotesServiceMock.getReportNotesData.and.returnValue(cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION));
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('navigateToPrevious', () => {
    it('should trigger a navigation to the previous breadcrumb reference', () => {
      component.navigateToPrevious();
      expect(reportNavigationServiceMock.navigateToNode).toHaveBeenCalledWith(
        MOCK_SECTIONS_BREADCRUMBS[MOCK_SECTIONS_BREADCRUMBS.length - 2].reference
      );
    });
  });

  describe('ngOnInit', () => {
    it('should create the object', () => {
      reportNotesServiceMock.getReportNotesData.and.returnValue(null);
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costLandAndBuildings).toBe(null);
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.costPlantAndMachineryEtc).toBe(null);
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings).toBe(null);
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc).toBe(null);
    });
  });

  describe('modalPopup', () => {
    it('should close the popup', () => {
      const event = 'close';
      component.closeModalHandler(event);
      expect(component.isVisible).toBeFalse();
    });
  });

  describe('updatePlantAndMachineryEtcCost', () => {
    it('should replace the plantAndMachineryEtcCost with the new value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostOrValuationForm, 'patchValue');
      const value = '2';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updatePlantAndMachineryEtcCost({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ costPlantAndMachineryEtc: MOCK_EVENT.detail });
    });
  });

  describe('updateLandAndBuildingsCost', () => {
    it('should replace the landAndBuildingsCost with the new value', () => {
      const patchValuesSpy = spyOn(component.analysisOfCostOrValuationForm, 'patchValue');
      const value = '5';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateLandAndBuildingsCost({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ costLandAndBuildings: MOCK_EVENT.detail });
    });
  });

  describe('analysisOfCostFormValues', () => {
    it('should add the item into the array', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      const event = {
        index: 5,
        year: 2006,
        landAndBuildings: 5,
        plantAndMachineryEtc: 6
      };
      component.itemFormChanges(event);
      expect(
        component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.length > 0
      ).toBeTruthy();
    });
    it('should update the value into the existing item', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      const event = {
        index: 2,
        year: 2006,
        landAndBuildings: 5,
        plantAndMachineryEtc: 6
      };
      component.itemFormChanges(event);
      expect(
        component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.length > 0
      ).toBeTruthy();
    });
    it('should remove the item if the new value getting null', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      const event = {
        index: 2,
        year: null,
        landAndBuildings: null,
        plantAndMachineryEtc: null
      };
      component.itemFormChanges(event);
      expect(
        component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.length > 0
      ).toBeTruthy();
    });
  });

  describe('updateAnalysisOfCost', () => {
    it('should trigger service update with the new values', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      component.saveAnalysisOfCost();
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        component.tangibleFixedAssetsNotes,
        MOCK_SECTION_REF,
        false,
        null,
        NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
      );
    });

    it('should not trigger service update with the new values as missing year', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.push({
        index: 4,
        year: 23,
        landAndBuildings: 1,
        plantAndMachineryEtc: 1
      });
      const navigateToPreviousSpy = spyOn(component, 'navigateToPrevious');
      component.saveAnalysisOfCost();
      expect(navigateToPreviousSpy).toHaveBeenCalledTimes(0);
    });
  });

  describe('isAnalysisOfCostOrValuationMandatory', () => {
    it('should be true when historical cost breakdown is present', () => {
      component.tangibleFixedAssetsNotes = {
        historicalCostBreakdown: {
          revaluedAssetClass: 'historicDetails',
          revaluedClassPronoun: 'revaluedClassPronoun',
          currentReportingPeriodCost: 1,
          currentReportingPeriodAccumulatedDepreciation: 2
        },
        analysisOfCostOrValuation: cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION)
      };

      expect(component.isAnalysisOfCostOrValuationMandatory).toBe(true);
    });

    it('should be false when historical cost breakdown is not present', () => {
      component.tangibleFixedAssetsNotes = {
        historicalCostBreakdown: {
          revaluedAssetClass: null,
          revaluedClassPronoun: null,
          currentReportingPeriodCost: null,
          currentReportingPeriodAccumulatedDepreciation: null
        },
        analysisOfCostOrValuation: cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION)
      };

      expect(component.isAnalysisOfCostOrValuationMandatory).toBe(false);
    });
  });

  describe('isFormInvalid', () => {
    it('should return false when total is positive', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 2004,
              landAndBuildings: 1,
              plantAndMachineryEtc: 1
            }
          ]
        }
      };
      component.calculateLandAndBuildingsTotal();
      component.calculatePlantAndMachineryTotal();
      expect(component.isFormInvalid).toBe(false);
    });

    it('should return false when items array is empty and cost values are null', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      };
      component.calculateLandAndBuildingsTotal();
      component.calculatePlantAndMachineryTotal();
      expect(component.isFormInvalid).toBe(false);
    });

    it('should return true when total is negative', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 2004,
              landAndBuildings: -10,
              plantAndMachineryEtc: 1
            }
          ]
        }
      };
      component.calculateLandAndBuildingsTotal();
      component.calculatePlantAndMachineryTotal();
      expect(component.isFormInvalid).toBe(true);
    });
  });

  describe('calculateLandAndBuildingsTotal', () => {
    it('should calculate total when values are present', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      component.calculateLandAndBuildingsTotal();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings).toBe(4);
    });

    it('should calculate total when items is emtpy but cost is present', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: 1,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      };

      component.calculateLandAndBuildingsTotal();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings).toBe(1);
    });

    it('should set total as null when values are not present', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      };
      component.calculateLandAndBuildingsTotal();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalLandAndBuildings).toBe(null);
    });
  });

  describe('calculatePlantAndMachineryTotal', () => {
    it('should calculate total when values are present', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      component.calculatePlantAndMachineryTotal();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc).toBe(5);
    });

    it('should calculate total when items is emtpy but cost is present', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: 1,
          costPlantAndMachineryEtc: 2,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      };

      component.calculatePlantAndMachineryTotal();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc).toBe(2);
    });

    it('should set total as null when values are not present', () => {
      component.tangibleFixedAssetsNotes = {
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      };
      component.calculatePlantAndMachineryTotal();
      expect(component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.totalPlantAndMachineryEtc).toBe(null);
    });
  });

  describe('isMissingRow', () => {
    it('should have a missing row', () => {
      component.lastFilledRowInOrderIndex = 2;
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.push({
        index: 4,
        year: 2010,
        landAndBuildings: 1,
        plantAndMachineryEtc: 1
      });
      expect(component.isMissingRow()).toBeTrue();
    });

    it('should not have missing row', () => {
      component.lastFilledRowInOrderIndex = 2;
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
      expect(component.isMissingRow()).toBeFalse();
    });

    it('should not have missing row when empty', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      });
      component.lastFilledRowInOrderIndex = 0;
      expect(component.isMissingRow()).toBeFalse();
    });
  });

  describe('isMissingYear', () => {
    beforeEach(() => {
      component.lastFilledRowInOrderIndex = 3;
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
    });

    it('should not have a missing year', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 2004,
              landAndBuildings: -10,
              plantAndMachineryEtc: 1
            }
          ]
        }
      });
      expect(component.isMissingYear()).toBeFalse();
    });

    it('should have missing year when year is empty with landAndBuildings value', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: null,
              landAndBuildings: 1,
              plantAndMachineryEtc: null
            }
          ]
        }
      });
      expect(component.isMissingYear()).toBeTrue();
    });

    it('should have missing year when year is empty with plantAndMachineryEtc value', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: null,
              landAndBuildings: null,
              plantAndMachineryEtc: 1
            }
          ]
        }
      });

      expect(component.isMissingYear()).toBeTrue();
    });

    it('should have missing year when year is less than 999 with plantAndMachineryEtc value', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 99,
              landAndBuildings: null,
              plantAndMachineryEtc: 1
            }
          ]
        }
      });

      expect(component.isMissingYear()).toBeTrue();
    });

    it('should have missing year when year is 999 with plantAndMachineryEtc value', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 999,
              landAndBuildings: null,
              plantAndMachineryEtc: 1
            }
          ]
        }
      });

      expect(component.isMissingYear()).toBeTrue();
    });

    it('should have not missing year when tangibleFixedAssetsNotes is empty', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: null
      });
      expect(component.isMissingYear()).toBeFalse();
    });

    it('should have not missing year when analysisOfCostOrValuationItems is empty', () => {
      component.tangibleFixedAssetsNotes = cloneDeep({
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: null
        }
      });
      expect(component.isMissingYear()).toBeFalse();
    });
  });

  describe('getLastFilledRowInOrderIndex', () => {
    beforeEach(() => {
      component.lastFilledRowInOrderIndex = 2;
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation = cloneDeep(MOCK_ANALYSIS_OF_COST_VALUATION);
    });

    it('should bring back 2 without missing row', () => {
      component.getLastFilledRowInOrderIndex();
      expect(component.lastFilledRowInOrderIndex).toBe(2);
    });

    it('should bring back 2 with missing row', () => {
      component.tangibleFixedAssetsNotes.analysisOfCostOrValuation.analysisOfCostOrValuationItems.push({
        index: 4,
        year: 2010,
        landAndBuildings: 1,
        plantAndMachineryEtc: 1
      });
      component.getLastFilledRowInOrderIndex();
      expect(component.lastFilledRowInOrderIndex).toBe(2);
    });
  });

  describe('onYearInputChange', () => {
    it('should on year input change selected year input row index', () => {
      component.onYearInputChange(10);
      expect(component.selectedYearInputRowIndex).toBe(10);
    });
  });
});
