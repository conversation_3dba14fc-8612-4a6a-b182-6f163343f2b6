<div class="section-notes-textarea">
    <iris-textarea
        [label]="noteLabel"
        [required]="hasValidation"
        [colour]="hasValidation && !noteValue?.length ? 'error-input' : 'default'" 
        [maxlength]="MAX_TEXTAREA_LENGTH" 
        [value]="noteValue" 
        [resize]="'vertical'" 
        (changed)="updateEditNote($event)"
    ></iris-textarea>
</div>
<p *ngIf="!(hasValidation && !noteValue?.length)" class="note-textarea-remaining">{{(MAX_TEXTAREA_LENGTH - noteValue?.length) | commaDelimiter}} characters remaining</p>
<p *ngIf="hasValidation && !noteValue?.length"  class="note-textarea-error">
    <iris-icon-only type="alert-circle" size="14" padding="0"></iris-icon-only>
    Please enter the content for this note
</p>
