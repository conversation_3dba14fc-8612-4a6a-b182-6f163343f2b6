@import '../../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';
@import '../../../../../../assets/mixins';

:host {
  display: block;
  @include report-sidebar-form-border;
  &:last-child {
    &::before {
      height: 100%;
    }
  }
}

.note-textarea-label {
  display: block;
  font-size: 16px;
  color: $iris-grey-dark;
  margin-bottom: $p-0;
}

.note-textarea-remaining {
  font-size: 14px;
  color: $grey-light-6;
  margin: 0;
}

.note-textarea-error {
  font-size: 14px;
  margin: 0;
  color: $iris-red;
}
