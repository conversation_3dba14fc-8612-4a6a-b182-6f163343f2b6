import { Component, Input, OnInit } from '@angular/core';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { SectionsDataProxyService } from 'src/app/services/sections-data-proxy.service';

enum ParagraphLabelMapping {
  financialInstrumentsAccountingPolicy = 'Accounting policy for financial instruments',
  governmentGrantsAccountingPolicy = 'Accounting policy for government grants',
  intangibleAssetsRevaluation = 'Information to be disclosed in respect of the revaluation of intangible assets'
}
@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-section-notes-textarea',
  templateUrl: './section-notes-textarea.component.html',
  styleUrls: ['./section-notes-textarea.component.scss']
})
export class SectionNotesTextareaComponent implements OnInit {
  @Input() sectionReference: ReportSection;

  MAX_TEXTAREA_LENGTH = 10000;
  noteLabel = '';
  noteValue;
  hasValidation = false;

  constructor(private sectionsDataProxyService: SectionsDataProxyService) {}

  ngOnInit(): void {
    this.noteLabel = ParagraphLabelMapping[this.sectionReference.formConfigKey] ?? this.sectionReference.label;
    this.noteValue = this.sectionsDataProxyService.getFormData(
      this.sectionReference.dataSourceService,
      this.sectionReference.formConfigKey
    );
    if (this.sectionReference.formConfigKey === NotesFormTypeEnum.MEMBERS_LIABILITIES) {
      this.noteValue = this.noteValue ? this.noteValue.noteText : null;
    }
    this.hasValidation = this.sectionsDataProxyService.getFormValidation(
      this.sectionReference.dataSourceService,
      this.sectionReference.formConfigKey
    );
  }

  updateEditNote(newNote): void {
    this.noteValue = newNote.target.value;
    this.sectionsDataProxyService.updateServiceData(
      this.noteValue,
      this.sectionReference,
      !!this.hasValidation && !this.noteValue?.length
    );
  }
}
