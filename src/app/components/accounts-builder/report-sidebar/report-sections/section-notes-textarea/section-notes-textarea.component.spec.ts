import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { ReportNotesService } from 'src/app/services/report-notes.service';
import { SectionsDataProxyService } from 'src/app/services/sections-data-proxy.service';

import { SectionNotesTextareaComponent } from './section-notes-textarea.component';

describe('SectionNotesTextareaComponent', () => {
  let component: SectionNotesTextareaComponent;
  let fixture: ComponentFixture<SectionNotesTextareaComponent>;

  const sectionsDataProxyServiceMock = jasmine.createSpyObj('SectionsDataProxyService', [
    'getFormData',
    'getFormValidation',
    'updateServiceData'
  ]);

  const MOCK_SECTION_REF = {
    parent: null,
    children: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.GUARANTEES,
    label: 'mock section',
    errorCount: 0,
    warningCount: 0
  };

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SectionNotesTextareaComponent, CommaDelimiterPipe],
      providers: [
        {
          provide: SectionsDataProxyService,
          useValue: sectionsDataProxyServiceMock
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SectionNotesTextareaComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should update the label with the section reference label, when there is no match in the label mapping', () => {
      component.ngOnInit();
      expect(component.noteLabel).toBe(MOCK_SECTION_REF.label);
    });

    it('should update the label with the mapping label when there is a match', () => {
      const MOCK_MAPPING_SECTION = {
        ...MOCK_SECTION_REF,
        formConfigKey: NotesFormTypeEnum.GOVERNMENT_GRANTS
      };
      const EXPECTED_RESULT = 'Accounting policy for government grants';
      component.sectionReference = MOCK_MAPPING_SECTION;
      component.ngOnInit();
      expect(component.noteLabel).toBe(EXPECTED_RESULT);
    });
  });

  describe('updateEditNote', () => {
    it('should update the note content value', () => {
      const event = {
        target: {
          value: 'new updated note'
        }
      };
      component.updateEditNote(event);
      expect(component.noteValue).toEqual(event.target.value);
      expect(sectionsDataProxyServiceMock.updateServiceData).toHaveBeenCalledWith(
        event.target.value,
        component.sectionReference,
        false
      );
    });

    it('should update the note content and mark set the error flag to true', () => {
      const event = {
        target: {
          value: ''
        }
      };
      component.hasValidation = true;
      component.updateEditNote(event);
      expect(component.noteValue).toEqual(event.target.value);
      expect(sectionsDataProxyServiceMock.updateServiceData).toHaveBeenCalledWith(
        event.target.value,
        component.sectionReference,
        true
      );
    });

    it('should set noteValue to noteValue.noteText when the section is membersLiabilityText', () => {
      const MOCK_MAPPING_SECTION = {
        ...MOCK_SECTION_REF,
        formConfigKey: NotesFormTypeEnum.MEMBERS_LIABILITIES
      };
      const MOCK_NOTE_VALUE = {
        noteText: 'Sample note text'
      };
      component.sectionReference = MOCK_MAPPING_SECTION;
      sectionsDataProxyServiceMock.getFormData.and.returnValue(MOCK_NOTE_VALUE);
      component.ngOnInit();
      expect(component.noteValue).toEqual(MOCK_NOTE_VALUE.noteText);
    });

    it('should set noteValue to null when the section is membersLiabilityText and notes object is null', () => {
      const MOCK_MAPPING_SECTION = {
        ...MOCK_SECTION_REF,
        formConfigKey: NotesFormTypeEnum.MEMBERS_LIABILITIES
      };
      component.sectionReference = MOCK_MAPPING_SECTION;
      sectionsDataProxyServiceMock.getFormData.and.returnValue(null);
      component.ngOnInit();
      expect(component.noteValue).toEqual(null);
    });
  });
});
