import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, ViewChild, Input } from '@angular/core';
import { Subscription } from 'rxjs';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { DisplayedFormEnum, NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { ReportSectionsService } from 'src/app/services/report-sections.service';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import { hasLicenseWithValidLevel } from '@iris/platform-ui-core-utility-pkg';
import { features } from 'src/features';
import { AppSettings } from 'src/app/utils/appSettings';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-report-sections',
  templateUrl: './report-sections.component.html',
  styleUrls: ['./report-sections.component.scss']
})
export class ReportSectionsComponent implements OnDestroy {
  @Input() periodData: AccountProductionPeriod = null;
  @ViewChild('sectionsContainer') sectionsContainer: ElementRef;
  hasEssentialLicense = hasLicenseWithValidLevel(AppSettings.License.Code, [AppSettings.License.Level.Basic]);
  isAccountProductionLicenseEnabled: boolean = false;

  notesFormTypeEnum = NotesFormTypeEnum;
  displayedFormEnum = DisplayedFormEnum;

  displayedForm: DisplayedFormEnum;

  sectionsTreeNode: ReportSection;
  selectedSection: ReportSection;

  navigationNodeSubscription: Subscription = null;
  constructor(
    private reportSectionsService: ReportSectionsService,
    private reportNavigationService: ReportNavigationService
  ) {
    this.navigationNodeSubscription = this.reportNavigationService.currentNavigationNode.subscribe(navigationState => {
      this.sectionsTreeNode = navigationState;
      if (this.sectionsTreeNode.formConfigKey) {
        this.setDisplayedReport();
      }
      if (this.sectionsContainer) {
        this.sectionsContainer.nativeElement.scrollTo(0, 0);
      }
    });
  }

  ngOnInit(): void {
    features.getState(AppSettings.FeatureFlag.AccountsProduction.Licensing).subscribe(isToggledOn => {
      this.isAccountProductionLicenseEnabled = isToggledOn;
    });
  }

  navigateToSection(sectionNode: ReportSection): void {
    this.reportNavigationService.navigateToNode(sectionNode);
  }

  setSelectedSection(selectedSection: ReportSection): void {
    this.selectedSection = selectedSection;
    if (selectedSection.navigationRef) {
      this.reportSectionsService.updateBookmarkNavigation(selectedSection.navigationRef);
    }
  }

  setDisplayedReport(): void {
    switch (this.sectionsTreeNode.formConfigKey) {
      case this.notesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES:
        this.displayedForm = DisplayedFormEnum.AVERAGE_EMPLOYEES;
        break;
      case this.notesFormTypeEnum.ADVANCES_CREDITS:
        this.displayedForm = DisplayedFormEnum.ADVANCES_CREDITS;
        break;
      case this.notesFormTypeEnum.OPERATING_PROFIT_LOSS:
        this.displayedForm = DisplayedFormEnum.OPERATING_PROFIT_LOSS;
        break;
      case this.notesFormTypeEnum.VALUATION_IN_CURRENT_REPORTING_PERIOD:
        this.displayedForm = DisplayedFormEnum.VALUATION_CURRENT_PERIOD;
        break;
      case this.notesFormTypeEnum.HISTORICAL_COST_BREAKDOWN:
        this.displayedForm = DisplayedFormEnum.HISTORICAL_BREAKDOWN;
        break;
      case this.notesFormTypeEnum.ANALYSIS_OF_COST_VALUATION:
        this.displayedForm = DisplayedFormEnum.ANALYSIS_OF_COST_VALUATION;
        break;
      case this.notesFormTypeEnum.MEMBERS_LIABILITY_MODAL:
        this.displayedForm = DisplayedFormEnum.MEMBERS_LIABILITY_MODAL;
        break;
      // case this.notesFormTypeEnum.TEST_MODAL:
      case this.notesFormTypeEnum.MEMBERS_LIABILITIES:
        this.displayedForm = DisplayedFormEnum.PARAGRAPH;
        break;
      case this.notesFormTypeEnum.ADDITIONAL_NOTE_1:
      case this.notesFormTypeEnum.ADDITIONAL_NOTE_2:
        this.displayedForm = DisplayedFormEnum.TITLED_PARAGRAPH;
        break;
      case this.notesFormTypeEnum.EXEMPTION_FINANCIAL:
        this.displayedForm = DisplayedFormEnum.EXEMPTION_FINANCIAL;
        break;
      case this.notesFormTypeEnum.FOREIGN_CURRENCIES:
      case this.notesFormTypeEnum.PRESENTATION_CURRENCY:
      case this.notesFormTypeEnum.RESEARCH_AND_DEVELOPMENT:
        this.displayedForm = DisplayedFormEnum.CHECKBOX_FORM;
        break;
      case this.notesFormTypeEnum.GOODWILL_MATERIAL:
        this.displayedForm = DisplayedFormEnum.GOODWILL_MATERIAL;
        break;
      case this.notesFormTypeEnum.IMPROVEMENTS_PROPERTY:
      case this.notesFormTypeEnum.PLANT_AND_MACHINERY:
      case this.notesFormTypeEnum.FIXTURE_AND_FITTINGS:
      case this.notesFormTypeEnum.MOTOR_VEHICLES:
      case this.notesFormTypeEnum.COMPUTER_EQUIPMENT:
      case this.notesFormTypeEnum.GOODWILL:
      case this.notesFormTypeEnum.PATENTS_AND_LICENSES:
      case this.notesFormTypeEnum.DEVELOPMENT_COSTS:
      case this.notesFormTypeEnum.COMPUTER_SOFTWARE:
      case this.notesFormTypeEnum.FREEHOLD_PROPERTY:
      case this.notesFormTypeEnum.SHORT_LEASEHOLD_PROPERTY:
      case this.notesFormTypeEnum.LONG_LEASEHOLD_PROPERTY:
        this.displayedForm = DisplayedFormEnum.ASSETS_BASIS;
        break;
      case this.notesFormTypeEnum.LAND_AND_BUILDING_RENAME:
      case this.notesFormTypeEnum.PLANT_AND_MACHINERY_RENAME:
        this.displayedForm = DisplayedFormEnum.SECTION_RENAME;
        break;
      case this.notesFormTypeEnum.LOANS_DEBTS:
        this.displayedForm = DisplayedFormEnum.PARAGRAPH;
        break;
      default:
        this.displayedForm = DisplayedFormEnum.PARAGRAPH;
    }
  }

  ngOnDestroy() {
    this.navigationNodeSubscription?.unsubscribe();
  }
}
