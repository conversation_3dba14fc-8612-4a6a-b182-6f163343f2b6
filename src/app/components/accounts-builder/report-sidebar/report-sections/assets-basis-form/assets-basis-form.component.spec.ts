import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { AssetsBasis, NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

import { ActiveInputTypeEnum, AssetsBasisFormComponent } from './assets-basis-form.component';

describe('AssetsBasisFormComponent', () => {
  let component: AssetsBasisFormComponent;
  let fixture: ComponentFixture<AssetsBasisFormComponent>;
  const accountingPoliciesService = jasmine.createSpyObj('AccountingPoliciesService', [
    'updateCustomizationDataValues',
    'getCustomizationData',
    'hasSectionValidation'
  ]);
  const MOCK_CUSTOMIZATION_DATA: AssetsBasis = {
    categoryDescription: 'mock category',
    reducingBalanceBasis: 4,
    straightLineBasis: 2,
    alternativeBasis: 'mock alternative basis'
  };

  const MOCK_SECTION_REF: ReportSection = {
    parent: null,
    children: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.EXEMPTION_FINANCIAL,
    label: 'mock section',
    errorCount: 0,
    warningCount: 0
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AssetsBasisFormComponent, CommaDelimiterPipe],
      providers: [
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesService
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AssetsBasisFormComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    accountingPoliciesService.getCustomizationData.and.returnValue(MOCK_CUSTOMIZATION_DATA);
    accountingPoliciesService.hasSectionValidation.and.returnValue(false);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('updateCategoryDescription', () => {
    it('should replace the current name with the new value', () => {
      const patchValuesSpy = spyOn(component.assetsBasisForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated category name'
      };
      component.updateCategoryDescription(MOCK_EVENT);
      expect(component.categoryDescription).toBe(MOCK_EVENT.detail);
      expect(patchValuesSpy).toHaveBeenCalledWith({ categoryDescription: MOCK_EVENT.detail });
    });
  });

  describe('updateReducingBalanceBasis', () => {
    it('should replace the current name with the new value', () => {
      const patchValuesSpy = spyOn(component.assetsBasisForm, 'patchValue');
      const MOCK_EVENT = {
        detail: '4'
      };
      component.updateReducingBalanceBasis(MOCK_EVENT);
      expect(component.reducingBalanceBasis).toBe(+MOCK_EVENT.detail);
      expect(patchValuesSpy).toHaveBeenCalledWith({
        reducingBalanceBasis: +MOCK_EVENT.detail,
        straightLineBasis: null,
        alternativeBasis: null
      });
    });
  });

  describe('updateStraightLineBasis', () => {
    it('should replace the current name with the new value', () => {
      const patchValuesSpy = spyOn(component.assetsBasisForm, 'patchValue');
      const MOCK_EVENT = {
        detail: '2'
      };
      component.updateStraightLineBasis(MOCK_EVENT);
      expect(component.straightLineBasis).toBe(+MOCK_EVENT.detail);
      expect(patchValuesSpy).toHaveBeenCalledWith({
        reducingBalanceBasis: null,
        straightLineBasis: +MOCK_EVENT.detail,
        alternativeBasis: null
      });
    });
  });

  describe('updateAlternativeBasis', () => {
    it('should replace the current name with the new value', () => {
      const patchValuesSpy = spyOn(component.assetsBasisForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'mock change'
      };
      component.updateAlternativeBasis(MOCK_EVENT);
      expect(component.alternativeBasis).toBe(MOCK_EVENT.detail);
      expect(patchValuesSpy).toHaveBeenCalledWith({
        reducingBalanceBasis: null,
        straightLineBasis: null,
        alternativeBasis: MOCK_EVENT.detail
      });
    });
  });

  describe('updateDisabledInputs', () => {
    it('should set activeInput to null if there is no value in any field', () => {
      const MOCK_NEW_VALUES: AssetsBasis = {
        categoryDescription: 'mock category',
        reducingBalanceBasis: null,
        straightLineBasis: null,
        alternativeBasis: null
      };
      component.updateDisabledInputs(MOCK_NEW_VALUES);
      expect(component.activeInput).toBeNull();
    });

    it('should set activate reducing balance basis field if there is a value for it', () => {
      const MOCK_NEW_VALUES: AssetsBasis = {
        categoryDescription: 'mock category',
        reducingBalanceBasis: 1,
        straightLineBasis: null,
        alternativeBasis: null
      };
      component.updateDisabledInputs(MOCK_NEW_VALUES);
      expect(component.activeInput).toBe(ActiveInputTypeEnum.reducingBalanceBasis);
    });

    it('should set activate straight line basis field if there is a value for it', () => {
      const MOCK_NEW_VALUES: AssetsBasis = {
        categoryDescription: 'mock category',
        reducingBalanceBasis: null,
        straightLineBasis: 1,
        alternativeBasis: null
      };
      component.updateDisabledInputs(MOCK_NEW_VALUES);
      expect(component.activeInput).toBe(ActiveInputTypeEnum.straightLineBasis);
    });

    it('should set activate alternative basis field if there is a value for it', () => {
      const MOCK_NEW_VALUES: AssetsBasis = {
        categoryDescription: 'mock category',
        reducingBalanceBasis: null,
        straightLineBasis: null,
        alternativeBasis: 'mock value'
      };
      component.updateDisabledInputs(MOCK_NEW_VALUES);
      expect(component.activeInput).toBe(ActiveInputTypeEnum.alternativeBasis);
    });
  });

  describe('updateAssetsBasis', () => {
    it('should call the updateCustomizationDataValue using the corect parameters', () => {
      const MOCK_NEW_VALUES: AssetsBasis = {
        categoryDescription: 'mock category',
        reducingBalanceBasis: 3,
        straightLineBasis: 1,
        alternativeBasis: 'mock alt basis'
      };

      component.updateAssetsBasis(MOCK_NEW_VALUES);
      expect(accountingPoliciesService.updateCustomizationDataValues).toHaveBeenCalledWith(
        MOCK_NEW_VALUES,
        component.sectionReference,
        false
      );
    });
  });
});
