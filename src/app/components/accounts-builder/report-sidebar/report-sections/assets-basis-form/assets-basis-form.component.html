<div class="assets-basis-form">
    <div class="note-input">
        <iris-input
            [maxlength]="MAX_CATEGORY_DESCRIPTION_LENGTH"
            [label]="categoryLabel"
            [labelStyle]="'normal'"
            [value]="categoryDescription"
            [disabled]="hasEssentialLicense"
            (changed)="updateCategoryDescription($event)"
            [colour]="categoryDescriptionControl.invalid ? 'error-input' : 'default'"
        ></iris-input>
        <p *ngIf="!hasEssentialLicense && !categoryDescriptionControl.invalid" class="helper-text">{{(MAX_CATEGORY_DESCRIPTION_LENGTH - categoryDescription?.length) | commaDelimiter}} characters remaining</p>
        <p *ngIf="!hasEssentialLicense && categoryDescriptionControl.invalid" class="helper-text helper-text--error">
            <iris-icon-only type="alert-circle" size="14" padding="0"></iris-icon-only>
            Category description must not be blank
        </p>
        <p *ngIf="hasEssentialLicense" class="helper-text license-information">
          <iris-icon-only [color]="'#1B69B9'" type="alert-circle" size="14" padding="0"></iris-icon-only>
          Editing available with <strong>Professional licence</strong>
        </p>
    </div>
    <div class="note-input">
        <iris-numeric-input
            [label]="'Reducing balance basis'"
            label-style="normal"
            [value]="reducingBalanceBasis"
            (valueChanged)="updateReducingBalanceBasis($event)"
            [minValue]="MIN_PERCENTAGE_VALUE"
            [maxValue]="MAX_PERCENTAGE_VALUE"
            [maxDecimals]="MAX_PERCENTAGE_DECIMALS"
            suffix-text="%"
            empty-input-behavior="press"
            [disabled] ="activeInput && activeInput !== activeInputTypeEnum.reducingBalanceBasis"
        ></iris-numeric-input>
        <p class="helper-text">{{helperText}}</p>
    </div>
    <div class="note-input">
        <iris-numeric-input
        [label]="'Straight line basis'"
        label-style="normal"
        [value]="straightLineBasis"
        (valueChanged)="updateStraightLineBasis($event)"
        [minValue]="MIN_PERCENTAGE_VALUE"
        [maxValue]="MAX_PERCENTAGE_VALUE"
        [maxDecimals]="MAX_PERCENTAGE_DECIMALS"
        suffix-text="%"
        empty-input-behavior="press"
        [disabled] ="activeInput && activeInput !== activeInputTypeEnum.straightLineBasis"
        ></iris-numeric-input>
        <p class="helper-text">{{helperText}}</p>
    </div>
    <div class="note-input">
        <iris-textarea
            resize="vertical"
            [label]="'Alternative basis - enter text exactly as it is to be displayed in the accounting policy below:'"
            [maxlength]="MAX_ALTERNATIVE_BASIS_LENGTH"
            [value]="alternativeBasis"
            (changed)="updateAlternativeBasis($event)"
            [disabled] ="activeInput && activeInput !== activeInputTypeEnum.alternativeBasis"
        ></iris-textarea>
        <p class="helper-text">{{(MAX_ALTERNATIVE_BASIS_LENGTH - alternativeBasis?.length) | commaDelimiter}} characters remaining</p>
    </div>
</div>
