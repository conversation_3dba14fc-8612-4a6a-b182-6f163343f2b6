import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';
export enum ActiveInputTypeEnum {
  reducingBalanceBasis = 'reducingBalanceBasis',
  straightLineBasis = 'straightLineBasis',
  alternativeBasis = 'alternativeBasis'
}
@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-assets-basis-form',
  templateUrl: './assets-basis-form.component.html',
  styleUrls: ['./assets-basis-form.component.scss']
})
export class AssetsBasisFormComponent implements OnInit {
  MAX_CATEGORY_DESCRIPTION_LENGTH = 120;
  MAX_ALTERNATIVE_BASIS_LENGTH = 10000;
  MIN_PERCENTAGE_VALUE = 0;
  MAX_PERCENTAGE_VALUE = 100;
  MAX_PERCENTAGE_DECIMALS = 2;
  @Input() sectionReference: ReportSection;
  @Input() hasEssentialLicense: boolean;

  categoryLabel =
    'This uses a single category description (%CATEGORY_NAME%) throughout the financial statements. Amend default below if required:';
  helperText = 'Enter the accounting policy in respect of %CATEGORY_NAME%';

  isDataRequired = false;
  categoryName: string;
  categoryDescription: string;
  reducingBalanceBasis: number;
  straightLineBasis: number;
  alternativeBasis: string;

  activeInputTypeEnum = ActiveInputTypeEnum;
  activeInput: ActiveInputTypeEnum;

  assetsBasisForm: UntypedFormGroup;
  constructor(private _formBuilder: UntypedFormBuilder, private accountingPoliciesService: AccountingPoliciesService) {}

  ngOnInit(): void {
    this.categoryLabel = this.categoryLabel.replace('%CATEGORY_NAME%', this.sectionReference.label);
    this.helperText = this.helperText.replace('%CATEGORY_NAME%', this.sectionReference.label);

    const assetsBasisData = this.accountingPoliciesService.getCustomizationData(this.sectionReference.formConfigKey);
    this.isDataRequired = this.accountingPoliciesService.hasSectionValidation(
      this.sectionReference.validationIssueCode
    );

    this.categoryDescription =
      assetsBasisData?.categoryDescription !== null ? assetsBasisData.categoryDescription : this.sectionReference.label;
    this.reducingBalanceBasis = assetsBasisData?.reducingBalanceBasis ?? null;
    this.straightLineBasis = assetsBasisData?.straightLineBasis ?? null;
    this.alternativeBasis = assetsBasisData?.alternativeBasis ?? null;

    this.assetsBasisForm = this._formBuilder.group({
      categoryDescription: [this.categoryDescription, Validators.required],
      reducingBalanceBasis: [this.reducingBalanceBasis],
      straightLineBasis: [this.straightLineBasis],
      alternativeBasis: [this.alternativeBasis]
    });
    this.updateDisabledInputs({
      categoryDescription: this.categoryDescription,
      reducingBalanceBasis: this.reducingBalanceBasis,
      straightLineBasis: this.straightLineBasis,
      alternativeBasis: this.alternativeBasis
    });
    this.assetsBasisForm.valueChanges.subscribe(newValues => {
      this.updateDisabledInputs(newValues);
      this.updateAssetsBasis(newValues);
    });
  }

  get categoryDescriptionControl() {
    return this.assetsBasisForm.get('categoryDescription');
  }

  updateDisabledInputs(newValues): void {
    if (
      newValues.reducingBalanceBasis === null &&
      newValues.straightLineBasis === null &&
      !newValues.alternativeBasis
    ) {
      this.activeInput = null;
    } else if (newValues.reducingBalanceBasis !== null) {
      this.activeInput = ActiveInputTypeEnum.reducingBalanceBasis;
    } else if (newValues.straightLineBasis !== null) {
      this.activeInput = ActiveInputTypeEnum.straightLineBasis;
    } else if (newValues.alternativeBasis) {
      this.activeInput = ActiveInputTypeEnum.alternativeBasis;
    }
  }

  updateCategoryDescription({ detail }): void {
    this.categoryDescription = detail;
    this.assetsBasisForm.patchValue({ categoryDescription: detail });
  }

  updateReducingBalanceBasis({ detail }): void {
    this.reducingBalanceBasis = detail?.length ? +detail : null;
    this.assetsBasisForm.patchValue({
      reducingBalanceBasis: this.reducingBalanceBasis,
      straightLineBasis: null,
      alternativeBasis: null
    });
  }

  updateStraightLineBasis({ detail }): void {
    this.straightLineBasis = detail?.length ? +detail : null;
    this.assetsBasisForm.patchValue({
      straightLineBasis: this.straightLineBasis,
      reducingBalanceBasis: null,
      alternativeBasis: null
    });
  }

  updateAlternativeBasis({ detail }): void {
    this.alternativeBasis = detail;
    this.assetsBasisForm.patchValue({ alternativeBasis: detail, reducingBalanceBasis: null, straightLineBasis: null });
  }

  updateAssetsBasis(newValues): void {
    const hasError =
      this.isDataRequired &&
      (this.assetsBasisForm.invalid ||
        (this.reducingBalanceBasis === null && this.straightLineBasis === null && !this.alternativeBasis));
    this.accountingPoliciesService.updateCustomizationDataValues(newValues, this.sectionReference, hasError);
  }
}
