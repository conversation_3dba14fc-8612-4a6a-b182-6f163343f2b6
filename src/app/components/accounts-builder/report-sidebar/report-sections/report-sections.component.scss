@import '../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/variables';
@import '../../../../../assets/mixins';

$total-tabs-width: 480px;
$tabs-left-spacing: 32px;

$tabs-width: calc(#{$total-tabs-width} - #{$tabs-left-spacing});

$tabs-spacing: 64px;

.report-sections-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: unset;
  @include custom-scrollbar;
}
.section-list {
  list-style-type: none;
  margin: 0;
  padding: $p-3 0 0 $p-4;

  li {
    margin-bottom: $p-3;
    color: $grey-light-6;
    display: flex;
    max-width: $tabs-width;
    padding-right: $p-4;
    span {
      line-height: inherit;
    }

    .section-bookmark-item {
      flex: 1;
    }

    .section-active-icon,
    .section-edit-icon {
      opacity: 0;
    }
    .section-active-icon {
      color: $iris-link-active;
      align-self: baseline;
      padding-right: $p-1;
    }

    &:hover:not(.selected) {
      color: $iris-blue;
      text-decoration: underline;
      cursor: pointer;
    }

    &.selected {
      font-weight: 700;
      .section-active-icon,
      .section-edit-icon {
        opacity: 1;
      }
    }

    &.subsection-title {
      .section-active-icon {
        cursor: pointer;
      }
    }

    .section-edit-icon {
      color: $iris-link-active;
      cursor: pointer;
      align-self: center;
    }
  }
}

.subsection-title-container {
  max-width: $tabs-width;
  display: flex;
  margin-top: $p-3;
  .subsection-title {
    color: $grey-light-6;
    display: inline-block;
    margin: 0;
    font-weight: 700;
    line-height: inherit;
  }

  .subsection-title-icon {
    color: $iris-link-active;
    padding-right: $p-1;
    cursor: pointer;
  }
}
