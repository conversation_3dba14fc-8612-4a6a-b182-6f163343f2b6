import { Component, Input, OnInit } from '@angular/core';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

enum ReportCheckboxFormLabel {
  researchAndDevelopment = 'Tick to include this accounting policy where expediture on research and development is expensed as it is incurred',
  DEFAULT = 'Tick to include this accounting policy'
}

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-checkbox-form',
  templateUrl: './checkbox-form.component.html',
  styleUrls: ['./checkbox-form.component.scss']
})
export class CheckboxFormComponent implements OnInit {
  @Input() sectionReference: ReportSection;

  checkboxValue: boolean = false;
  checkboxLabel: string;

  constructor(private accountingPoliciesService: AccountingPoliciesService) {}

  ngOnInit(): void {
    const formData = this.accountingPoliciesService.getCustomizationData(this.sectionReference.formConfigKey);
    this.checkboxValue = !!formData;

    if (ReportCheckboxFormLabel[this.sectionReference.formConfigKey]) {
      this.checkboxLabel = ReportCheckboxFormLabel[this.sectionReference.formConfigKey];
    } else {
      this.checkboxLabel = ReportCheckboxFormLabel.DEFAULT;
    }
  }

  updateCheckboxValue(event) {
    const checked = event.target.checked;
    this.checkboxValue = checked;
    this.updateServiceData();
  }

  updateServiceData() {
    this.accountingPoliciesService.updateCustomizationDataValues(this.checkboxValue, this.sectionReference);
  }
}
