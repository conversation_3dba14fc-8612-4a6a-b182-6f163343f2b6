import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

import { CheckboxFormComponent } from './checkbox-form.component';

const MOCK_SECTION_REF: ReportSection = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.RESEARCH_AND_DEVELOPMENT,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

describe('CheckboxFormComponent', () => {
  let component: CheckboxFormComponent;
  let fixture: ComponentFixture<CheckboxFormComponent>;
  const accountingPoliciesService = jasmine.createSpyObj('AccountingPoliciesService', [
    'updateCustomizationDataValues',
    'getCustomizationData'
  ]);
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CheckboxFormComponent],
      providers: [
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesService
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CheckboxFormComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    accountingPoliciesService.getCustomizationData.and.returnValue(false);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the component', () => {
    component.ngOnInit();
    expect(component.checkboxValue).toBe(false);
  });

  it('should set the label to the default value', () => {
    component.sectionReference.formConfigKey = NotesFormTypeEnum.FOREIGN_CURRENCIES;
    component.ngOnInit();
    expect(component.checkboxValue).toBe(false);
    expect(component.checkboxLabel).toBe('Tick to include this accounting policy');
  });

  describe('updateCheckboxValue', () => {
    it('should update the checkbox value', () => {
      const updateServiceDataSpy = spyOn(component, 'updateServiceData');
      const MOCK_EVENT = {
        target: {
          checked: true
        }
      };
      component.updateCheckboxValue(MOCK_EVENT);
      expect(component.checkboxValue).toBe(true);
      expect(updateServiceDataSpy).toHaveBeenCalled();
    });
  });

  describe('updateServiceData', () => {
    it('should trigger the value update inside the service', () => {
      component.checkboxValue = true;
      component.updateServiceData();
      expect(accountingPoliciesService.updateCustomizationDataValues).toHaveBeenCalledWith(true, MOCK_SECTION_REF);
    });
  });
});
