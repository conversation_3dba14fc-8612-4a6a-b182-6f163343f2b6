import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, FormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { AdditionalNote } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-titled-paragraph-note',
  templateUrl: './titled-paragraph-note.component.html',
  styleUrls: ['./titled-paragraph-note.component.scss']
})
export class TitledParagraphNoteComponent implements OnInit {
  @Input() sectionReference: ReportSection;

  MAX_TITLE_LENGTH = 120;
  MAX_TEXTAREA_LENGTH = 10000;

  titleLabel = 'Note title';
  paragraphLabel = 'Contents';
  paragraphValue = '';
  titleValue = '';

  additionalNoteForm: UntypedFormGroup;

  constructor(private _formBuilder: UntypedFormBuilder, private reportNotesService: ReportNotesService) {}

  ngOnInit(): void {
    const noteData: AdditionalNote = this.reportNotesService.getReportNotesData(this.sectionReference.formConfigKey);
    this.paragraphValue = noteData?.noteText;
    this.titleValue = noteData?.noteTitle;
    this.additionalNoteForm = this._formBuilder.group({
      titleValue: [this.titleValue],
      paragraphValue: [this.paragraphValue, this.titleValue ? Validators.required : null]
    });

    this.additionalNoteForm.valueChanges.subscribe(newValues => {
      this.updateParagraphValidators();
      this.updateAdditionalNote(newValues);
    });
  }

  get titleValueControl() {
    return this.additionalNoteForm.get('titleValue');
  }

  get paragraphValueControl() {
    return this.additionalNoteForm.get('paragraphValue');
  }

  updateParagraphValue({ detail }): void {
    this.paragraphValue = detail;
    this.additionalNoteForm.patchValue({ paragraphValue: detail });
  }

  updateTitleValue({ detail }): void {
    this.titleValue = detail;
    this.additionalNoteForm.patchValue({ titleValue: detail });
  }

  updateParagraphValidators(): void {
    if (!this.titleValueControl.value && this.paragraphValueControl.validator) {
      this.paragraphValueControl.clearValidators();
      this.paragraphValueControl.updateValueAndValidity();
    } else if (this.titleValueControl.value && !this.paragraphValueControl.validator) {
      this.paragraphValueControl.setValidators([Validators.required]);
      this.paragraphValueControl.updateValueAndValidity();
    }
  }

  updateAdditionalNote(newValues): void {
    const additionalNoteValues: AdditionalNote = {
      noteTitle: newValues.titleValue,
      noteText: newValues.paragraphValue
    };
    this.reportNotesService.updateReportNotesDataValues(
      additionalNoteValues,
      this.sectionReference,
      false,
      this.paragraphValueControl.invalid
    );
  }
}
