<div class="notes-titled-paragraph">
    <div class="note-input">
        <iris-input [maxlength]="MAX_TITLE_LENGTH" [label]="titleLabel" [labelStyle]="'normal'" [value]="titleValue" (changed)="updateTitleValue($event)"></iris-input>
        <p class="helper-text"></p>
    </div>
    <div class="note-input">
        <iris-textarea resize="vertical" [label]="paragraphLabel" [maxlength]="MAX_TEXTAREA_LENGTH" [value]="paragraphValue" (changed)="updateParagraphValue($event)"></iris-textarea>
        <p *ngIf="!paragraphValueControl.invalid" class="helper-text">{{(MAX_TEXTAREA_LENGTH - paragraphValue?.length) | commaDelimiter}} characters remaining</p>
        <p *ngIf="paragraphValueControl.invalid" class="helper-text helper-text--warning">
            <iris-icon-only type="alert-circle" size="14" padding="0"></iris-icon-only>
            Fill all the missing fields to include this note in the Report
        </p>
    </div>
</div>
