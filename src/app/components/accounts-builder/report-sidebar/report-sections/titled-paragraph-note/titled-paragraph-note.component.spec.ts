import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { ReportNotesService } from 'src/app/services/report-notes.service';

import { TitledParagraphNoteComponent } from './titled-paragraph-note.component';

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'getReportNotesData',
  'updateReportNotesDataValues'
]);

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.ADDITIONAL_NOTE_1,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_REPORT_NOTES_DATA = {
  additionalNote1: {
    noteTitle: 'mock title',
    noteText: 'mock paragraph'
  }
};

describe('TitledParagraphNoteComponent', () => {
  let component: TitledParagraphNoteComponent;
  let fixture: ComponentFixture<TitledParagraphNoteComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TitledParagraphNoteComponent, CommaDelimiterPipe],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TitledParagraphNoteComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_REPORT_NOTES_DATA.additionalNote1);
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize the component', () => {
      component.ngOnInit();
      expect(reportNotesServiceMock.getReportNotesData).toHaveBeenCalledWith(MOCK_SECTION_REF.formConfigKey);
      expect(component.paragraphValue).toBe(MOCK_REPORT_NOTES_DATA.additionalNote1.noteText);
      expect(component.titleValue).toBe(MOCK_REPORT_NOTES_DATA.additionalNote1.noteTitle);
      expect(component.paragraphValueControl.validator).not.toBe(null);

      const updateValidatorsSpy = spyOn(component, 'updateParagraphValidators');
      component.updateTitleValue({ detail: '' });
      expect(component.paragraphValueControl.invalid).toBe(false);
      component.updateParagraphValue({ detail: '' });
      expect(updateValidatorsSpy).toHaveBeenCalled();
      expect(component.paragraphValueControl.invalid).toBe(true);
    });
  });

  describe('updateParagraphValue', () => {
    it('should update the paragraph with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.additionalNoteForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated paragraph value'
      };
      component.updateParagraphValue(MOCK_EVENT);
      expect(component.paragraphValue).toBe(MOCK_EVENT.detail);
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ paragraphValue: MOCK_EVENT.detail });
    });
  });

  describe('updateTitleValue', () => {
    it('should update the title with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.additionalNoteForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated title value'
      };
      component.updateTitleValue(MOCK_EVENT);
      expect(component.titleValue).toBe(MOCK_EVENT.detail);
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ titleValue: MOCK_EVENT.detail });
    });
  });

  describe('updateAdditionalNote', () => {
    it('should trigger service update with the new values', () => {
      const MOCK_NEW_VALUES = {
        titleValue: 'current title',
        paragraphValue: 'paragraph text'
      };
      component.updateAdditionalNote(MOCK_NEW_VALUES);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        {
          noteTitle: 'current title',
          noteText: 'paragraph text'
        },
        MOCK_SECTION_REF,
        false,
        false
      );
    });

    it('should trigger service update with the new values and toggle the warning flag', () => {
      const MOCK_NEW_VALUES = {
        titleValue: 'current title',
        paragraphValue: ''
      };
      component.paragraphValueControl.setValue(MOCK_NEW_VALUES.paragraphValue);
      component.updateAdditionalNote(MOCK_NEW_VALUES);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        {
          noteTitle: 'current title',
          noteText: ''
        },
        MOCK_SECTION_REF,
        false,
        true
      );
    });
  });

  describe('updateParagraphValidators', () => {
    it('should clear the validators if there is no title value', () => {
      const clearValidatorsSpy = spyOn(component.paragraphValueControl, 'clearValidators');
      const updateValueAndValiditySpy = spyOn(component.paragraphValueControl, 'updateValueAndValidity');
      component.titleValueControl.setValue('');
      component.paragraphValueControl.setValidators([Validators.required]);
      component.updateParagraphValidators();

      expect(clearValidatorsSpy).toHaveBeenCalled();
      expect(updateValueAndValiditySpy).toHaveBeenCalled();
    });

    it('should set the required validator if there is a title value', () => {
      const setValidatorsSpy = spyOn(component.paragraphValueControl, 'setValidators');
      const updateValueAndValiditySpy = spyOn(component.paragraphValueControl, 'updateValueAndValidity');
      component.titleValueControl.setValue('current title');
      component.paragraphValueControl.clearValidators();
      component.updateParagraphValidators();

      expect(setValidatorsSpy).toHaveBeenCalledWith([Validators.required]);
      expect(updateValueAndValiditySpy).toHaveBeenCalled();
    });
  });
});
