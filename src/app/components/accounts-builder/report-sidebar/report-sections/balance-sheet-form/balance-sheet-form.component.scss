@import '../../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';
@import '../../../../../../assets/mixins';

.balance-sheet-section-form {
  margin-left: calc(#{$p-0} + #{$p-4});
  padding: 0 $p-4 0 20px;
  iris-radio {
    margin-left: -$p-4;
    display: block;
    label {
      font-weight: normal;
    }
    &:not(:first-child) {
      margin-top: $p-6;
    }
    &:before {
      content: '';
      position: absolute;
      height: calc(100% + #{$p-6});
      border-left: 1px solid $grey-light-6;
      left: 12px;
      top: 0;
      z-index: 0;
    }
    &:last-child::before {
      height: 0;
    }
  }
}
