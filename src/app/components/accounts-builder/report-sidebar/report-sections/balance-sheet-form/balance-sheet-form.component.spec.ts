import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

import { BalanceSheetFormComponent } from './balance-sheet-form.component';

const MOCK_SECTION_REF: ReportSection = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.RESEARCH_AND_DEVELOPMENT,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};
const accountingPoliciesService = jasmine.createSpyObj('AccountingPoliciesService', [
  'updateCustomizationDataValues',
  'getCustomizationData'
]);

describe('BalanceSheetFormComponent', () => {
  let component: BalanceSheetFormComponent;
  let fixture: ComponentFixture<BalanceSheetFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BalanceSheetFormComponent],
      providers: [
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesService
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BalanceSheetFormComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    accountingPoliciesService.getCustomizationData.and.returnValue(false);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the component', () => {
    component.ngOnInit();
    expect(component.isGoodwillIncluded).toBe(false);
  });

  describe('updateGoodwillValue', () => {
    it('should update the goodwill value', () => {
      const updateServiceDataSpy = spyOn(component, 'updateServiceData');
      const MOCK_EVENT = true;
      component.updateGoodwillValue(MOCK_EVENT);
      expect(component.isGoodwillIncluded).toBe(true);
      expect(updateServiceDataSpy).toHaveBeenCalled();
    });
  });

  describe('updateServiceData', () => {
    it('should trigger the value update inside the service', () => {
      component.isGoodwillIncluded = true;
      component.updateServiceData();
      expect(accountingPoliciesService.updateCustomizationDataValues).toHaveBeenCalledWith(true, MOCK_SECTION_REF);
    });
  });
});
