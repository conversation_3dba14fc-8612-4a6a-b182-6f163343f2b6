import { Component, Input, OnInit } from '@angular/core';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-balance-sheet-form',
  templateUrl: './balance-sheet-form.component.html',
  styleUrls: ['./balance-sheet-form.component.scss']
})
export class BalanceSheetFormComponent implements OnInit {
  @Input() sectionReference: ReportSection;

  isGoodwillIncluded: boolean = false;

  constructor(private accountingPoliciesService: AccountingPoliciesService) {}

  ngOnInit(): void {
    const formData = this.accountingPoliciesService.getCustomizationData(this.sectionReference.formConfigKey);
    this.isGoodwillIncluded = !!formData;
  }

  updateGoodwillValue(event) {
    this.isGoodwillIncluded = event;
    this.updateServiceData();
  }

  updateServiceData() {
    this.accountingPoliciesService.updateCustomizationDataValues(this.isGoodwillIncluded, this.sectionReference);
  }
}
