<div *ngIf="sectionsTreeNode.formConfigKey">
  <elements-accounts-builder-component-v01-pkg-section-notes-textarea
    *ngIf="displayedForm === displayedFormEnum.PARAGRAPH"
    [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-section-notes-textarea>

  <elements-accounts-builder-component-v01-pkg-average-employees-form
    *ngIf="displayedForm === displayedFormEnum.AVERAGE_EMPLOYEES"
    [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-average-employees-form>

  <elements-accounts-builder-component-v01-pkg-titled-paragraph-note
    *ngIf="displayedForm === displayedFormEnum.TITLED_PARAGRAPH"
    [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-titled-paragraph-note>

  <elements-accounts-builder-component-v01-pkg-exemption-form
    *ngIf="displayedForm === displayedFormEnum.EXEMPTION_FINANCIAL"
    [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-exemption-form>

  <elements-accounts-builder-component-v01-pkg-checkbox-form
    *ngIf="displayedForm === displayedFormEnum.CHECKBOX_FORM"
    [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-checkbox-form>

  <elements-accounts-builder-component-v01-pkg-balance-sheet-form
  *ngIf="displayedForm === displayedFormEnum.GOODWILL_MATERIAL"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-balance-sheet-form>

  <elements-accounts-builder-component-v01-pkg-operating-profit-loss-note
  *ngIf="displayedForm === displayedFormEnum.OPERATING_PROFIT_LOSS"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-operating-profit-loss-note>

  <elements-accounts-builder-component-v01-pkg-assets-basis-form
  *ngIf="displayedForm === displayedFormEnum.ASSETS_BASIS"
  [sectionReference]="sectionsTreeNode"
  [hasEssentialLicense]="hasEssentialLicense && isAccountProductionLicenseEnabled">
  </elements-accounts-builder-component-v01-pkg-assets-basis-form>

  <elements-accounts-builder-component-v01-pkg-valuation-current-reporting-period
  *ngIf="displayedForm === displayedFormEnum.VALUATION_CURRENT_PERIOD"
  [periodData]="periodData"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-valuation-current-reporting-period>

  <elements-accounts-builder-component-v01-pkg-historical-cost-breakdown
  *ngIf="displayedForm === displayedFormEnum.HISTORICAL_BREAKDOWN"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-historical-cost-breakdown>

  <elements-accounts-builder-component-v01-pkg-analysis-of-cost-valuation
  *ngIf="displayedForm === displayedFormEnum.ANALYSIS_OF_COST_VALUATION"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-analysis-of-cost-valuation>

  <elements-accounts-builder-component-v01-pkg-members-liability-modal
  *ngIf="displayedForm === displayedFormEnum.MEMBERS_LIABILITY_MODAL"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-members-liability-modal>

  <elements-accounts-builder-component-v01-pkg-director-edit
  *ngIf="displayedForm === displayedFormEnum.ADVANCES_CREDITS"
  [sectionReference]="sectionsTreeNode">
  </elements-accounts-builder-component-v01-pkg-director-edit>

  <elements-accounts-builder-component-v01-pkg-section-rename-form
  *ngIf="displayedForm === displayedFormEnum.SECTION_RENAME"
  [sectionReference]="sectionsTreeNode"
  [hasEssentialLicense]="hasEssentialLicense && isAccountProductionLicenseEnabled">
  </elements-accounts-builder-component-v01-pkg-section-rename-form>
</div>

<div class="report-sections-container" *ngIf="sectionsTreeNode.children" #sectionsContainer>
  <ng-container *ngFor="let sectionNode of sectionsTreeNode.children; let i = index">
    <elements-accounts-builder-component-v01-pkg-report-item
      [sectionReference]="sectionNode"
      [isFirstSection]="i === 0 && !sectionsTreeNode.formConfigKey"
      [isLastSection]="i === sectionsTreeNode.children.length - 1"
      [isActive]="selectedSection === sectionNode"
      (onSelect)="setSelectedSection($event)"
      (onEdit)="navigateToSection(sectionNode)">
    </elements-accounts-builder-component-v01-pkg-report-item>
  </ng-container>
</div>
