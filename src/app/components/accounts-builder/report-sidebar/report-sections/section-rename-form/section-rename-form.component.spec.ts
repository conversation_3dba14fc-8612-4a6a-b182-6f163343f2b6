import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

import { SectionRenameFormComponent } from './section-rename-form.component';

describe('SectionRenameFormComponent', () => {
  let component: SectionRenameFormComponent;
  let fixture: ComponentFixture<SectionRenameFormComponent>;
  const accountingPoliciesService = jasmine.createSpyObj('AccountingPoliciesService', [
    'updateCustomizationDataValues',
    'getCustomizationData'
  ]);

  const MOCK_SECTION_REF: ReportSection = {
    parent: null,
    children: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.EXEMPTION_FINANCIAL,
    label: 'mock section',
    errorCount: 0,
    warningCount: 0
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SectionRenameFormComponent],
      providers: [
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesService
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SectionRenameFormComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    accountingPoliciesService.getCustomizationData.and.returnValue('mock rename');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('updateNameOverwrite', () => {
    it('should replace the current name with the new value', () => {
      const MOCK_EVENT = {
        detail: 'updated class name'
      };
      component.updateNameOverwrite(MOCK_EVENT);
      expect(component.nameOverwrite).toBe(MOCK_EVENT.detail);
      expect(accountingPoliciesService.updateCustomizationDataValues).toHaveBeenCalledWith(
        MOCK_EVENT.detail,
        MOCK_SECTION_REF
      );
    });
  });
});
