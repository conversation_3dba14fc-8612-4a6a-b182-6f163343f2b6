import { Component, Input, OnInit } from '@angular/core';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-section-rename-form',
  templateUrl: './section-rename-form.component.html',
  styleUrls: ['./section-rename-form.component.scss']
})
export class SectionRenameFormComponent implements OnInit {
  MAX_CLASS_NAME_LENGTH = 50;
  @Input() sectionReference: ReportSection;
  @Input() hasEssentialLicense: boolean;
  nameOverwrite: string;

  constructor(private accountingPoliciesService: AccountingPoliciesService) {}

  ngOnInit(): void {
    this.nameOverwrite = this.accountingPoliciesService.getCustomizationData(this.sectionReference.formConfigKey);
  }

  updateNameOverwrite({ detail }): void {
    this.nameOverwrite = detail;
    this.accountingPoliciesService.updateCustomizationDataValues(this.nameOverwrite, this.sectionReference);
  }
}
