@import '../../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

$chevron-right-size: 24px;
$section-item-top-line: calc(#{$p-3} + #{$chevron-right-size} / 2);
$section-content-width: 392px;

.section-item-container {
  display: flex;
  flex-direction: column;
}

.section-item {
  padding: 0 $p-3;
  background-color: $white;
  display: flex;
  gap: $p-1;
  cursor: pointer;
  .item-chevron-container,
  .item-content-container {
    padding: $p-3 0;
  }
  .item-chevron-container {
    flex: 0;
    position: relative;
    z-index: 2;
    &::before {
      content: ' ';
      height: $section-item-top-line;
      position: absolute;
      top: 0px;
      left: 50%;
      z-index: -1;
      border-left: 1px solid $grey-light-6;
    }
    &::after {
      content: ' ';
      height: calc(100% - #{$section-item-top-line});
      position: absolute;
      bottom: 0px;
      left: 50%;
      z-index: -1;
      border-left: 1px solid $grey-light-6;
    }
  }

  .item-content-container {
    flex: 1;
    width: $section-content-width;
    .title-container {
      display: flex;
      justify-content: space-between;
      .truncated-title {
        flex: 1;
        min-width: 0;
      }
      .title {
        margin: 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;
        color: $iris-grey-dark;
      }
      .icons-list {
        display: flex;
        iris-icon,
        iris-icon-only {
          margin-left: 8px;
        }
      }
    }
  }

  &.section-item--active,
  &:hover {
    background-color: $grey-light-1;
    .item-chevron-container {
      &::before,
      &::after {
        border-left: 1px solid $grey-light-7;
      }
    }
    .item-content-container .title-container .title {
      font-weight: 700;
    }
    .item-content-container .helper-text {
      padding: 0;
      margin: 0;
      font-size: 14px;
      margin-top: $p-0;
      color: $iris-grey-dark;
      .required {
        color: $iris-red;
      }
    }
  }

  &.section-item--first {
    .item-chevron-container {
      &::before {
        border-left: none;
      }
    }
  }

  &.section-item--last {
    .item-chevron-container {
      &::after {
        border-left: none;
      }
    }
  }
}

.btn-customize {
  display: flex;
  height: $p-7;
  padding: 0 $p-3;
  background: $iris-blue;
  justify-content: space-between;
  border: none;
  cursor: pointer;
  span {
    margin-top: 16px;
    color: #ffffff;
    font-size: 16px;
    font-style: normal;
    line-height: 1.5;
  }
  .right-arrow {
    margin-top: $p-2;
  }
}
