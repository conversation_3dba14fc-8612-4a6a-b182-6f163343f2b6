import {
  AfterViewChecked,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-report-item',
  templateUrl: './report-item.component.html',
  styleUrls: ['./report-item.component.scss']
})
export class ReportItemComponent implements OnInit, OnChanges, AfterViewChecked {
  @ViewChild('sectionItem') sectionItem: ElementRef;

  @Input() sectionReference: ReportSection;
  @Input() isActive: boolean;
  @Input() isFirstSection: boolean;
  @Input() isLastSection: boolean;

  @Output() onSelect = new EventEmitter<ReportSection>();
  @Output() onEdit = new EventEmitter<ReportSection>();

  triggerScrollIntoView: boolean;
  chevronIconColor: string;
  chevronBackgroundColor: string;
  chevronBorderColor: string;
  alertIconColor: string;
  sectionHelperText: string;

  isHovered: boolean = false;

  ngOnInit() {
    if (this.isFirstSection) {
      this.triggerScrollIntoView = true;
    }
    this.setChevronBackgroundColor();
    this.setChevronIconColor();
    this.setChevronBorderColor();
    this.setAlertIconColor();
    this.setSectionHelperText();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.setChevronBackgroundColor();
    this.setChevronIconColor();
    this.setChevronBorderColor();
    this.setAlertIconColor();
    if (changes.isActive && changes.isActive.currentValue === true) {
      this.triggerScrollIntoView = true;
    }
  }

  ngAfterViewChecked(): void {
    if (this.triggerScrollIntoView) {
      this.triggerScrollIntoView = false;
      this.sectionItem.nativeElement.scrollIntoView(false);
    }
  }

  onSectionSelect(): void {
    this.onSelect.emit(this.sectionReference);
  }

  onEditTrigger(): void {
    this.onEdit.emit(this.sectionReference);
  }

  toggleHover(newValue: boolean) {
    this.isHovered = newValue;
    this.setChevronBackgroundColor();
    this.setChevronIconColor();
    this.setChevronBorderColor();
  }

  setChevronIconColor(): void {
    if (this.sectionReference.isMandatory) {
      this.chevronIconColor = this.isActive || this.isHovered ? '#ffffff' : '#505962';
    } else if (!this.sectionReference.hasData) {
      this.chevronIconColor = '#ffffff';
    } else {
      this.chevronIconColor = this.isActive || this.isHovered ? '#ffffff' : '#1B69B9';
    }
  }

  setChevronBackgroundColor(): void {
    if (this.sectionReference.isMandatory) {
      this.chevronBackgroundColor = this.isActive || this.isHovered ? '#505962' : '#ffffff';
    } else {
      this.chevronBackgroundColor = this.isActive || this.isHovered ? '#1B69B9' : '#ffffff';
    }
  }

  setChevronBorderColor(): void {
    if (!this.sectionReference.isMandatory) {
      if (this.sectionReference.hasData) {
        this.chevronBorderColor = !(this.isActive || this.isHovered) ? '#1B69B9' : 'transparent';
      } else {
        this.chevronBorderColor = !(this.isActive || this.isHovered) ? '#666E76' : 'transparent';
      }
    } else {
      this.chevronBorderColor = 'transparent';
    }
  }

  setEditButtonText(): string {
    if (this.sectionReference.errorCount || this.sectionReference.warningCount) return 'Fix';
    return !this.sectionReference.hasData && !this.sectionReference.isMandatory ? 'Add and Edit' : 'Edit';
  }

  setAlertIconColor(): void {
    if (this.sectionReference.errorCount > 0) {
      this.alertIconColor = '#C8102E';
    } else if (this.sectionReference.warningCount > 0) {
      this.alertIconColor = '#FF9504';
    }
  }

  setSectionHelperText(): void {
    if (this.sectionReference.errorCount === 0 && this.sectionReference.warningCount > 0) {
      this.sectionHelperText = 'The Note will not be included in the submitted Report, due to missing fields';
    } else {
      this.sectionHelperText = '';
    }
  }

  hasFormKey(): boolean {
    return !!this.sectionReference.formConfigKey;
  }
}
