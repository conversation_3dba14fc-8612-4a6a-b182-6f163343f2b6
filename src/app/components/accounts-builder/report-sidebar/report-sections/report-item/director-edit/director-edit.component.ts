import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import {
  AdvancesCreditAndGuaranteesGrantedToDirectorsExtended,
  AdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem
} from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-director-edit',
  templateUrl: './director-edit.component.html',
  styleUrls: ['./director-edit.component.scss']
})
export class DirectorEditComponent implements OnInit {
  @Input() sectionReference: ReportSection;
  advancesCreditAndGuaranteesGrantedToDirectorsExtended: AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = null;
  advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem: AdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem =
    null;

  MIN_VALUE_LENGTH = -***********.99;
  MAX_VALUE_LENGTH = ***********.99;
  MAX_TEXTAREA_LENGTH = 10000;

  directorInfoForm: UntypedFormGroup;
  readonly: boolean = true;
  advancesCredit = '';
  balanceOutstandingEndYear: number;

  constructor(private _formBuilder: UntypedFormBuilder, private reportNotesService: ReportNotesService) {}

  ngOnInit(): void {
    this.advancesCreditAndGuaranteesGrantedToDirectorsExtended =
      this.reportNotesService.getReportNotesData(this.sectionReference.formConfigKey) ?? this.getNewInstance();
    this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem =
      this.advancesCreditAndGuaranteesGrantedToDirectorsExtended?.items?.find(
        c => c.involvementClientGuid === this.sectionReference.director.involvementClientGuid
      );

    this.directorInfoForm = this._formBuilder.group({
      balanceOutstandingAtStartOfYear: [
        this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.balanceOutstandingAtStartOfYear
      ],
      amountsAdvanced: [this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsAdvanced],
      amountsRepaid: [this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsRepaid],
      amountsWrittenOff: [this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsWrittenOff],
      amountsWaived: [this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsWaived],
      advanceCreditConditions: [
        this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.advanceCreditConditions
      ],
      guarantees: [this.advancesCreditAndGuaranteesGrantedToDirectorsExtended?.guarantees]
    });

    this.balanceOutstandingEndYear = this.calculateBalanceOutstandingEndYear(
      this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem
    );
    this.advancesCredit = this.advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.advanceCreditConditions ?? '';
    this.directorInfoForm.valueChanges.subscribe(newValues => {
      this.updateFormValue(newValues);
    });
  }

  calculateBalanceOutstandingEndYear(
    advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem: AdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem
  ): number {
    this.balanceOutstandingEndYear = advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem
      ? (advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.balanceOutstandingAtStartOfYear ?? 0) +
        (advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsAdvanced ?? 0) -
        (advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsRepaid ?? 0) -
        (advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsWrittenOff ?? 0) -
        (advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsWaived ?? 0)
      : 0;
    return this.balanceOutstandingEndYear;
  }

  getNewInstance() {
    const result: AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = {
      guarantees: null,
      items: []
    };
    return result;
  }

  updateFormValue(newValues) {
    const newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem: AdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem =
      {
        index: this.sectionReference.director.index,
        involvementClientGuid: this.sectionReference.director.involvementClientGuid,
        directorName: this.sectionReference.director.directorName,
        balanceOutstandingAtStartOfYear: newValues.balanceOutstandingAtStartOfYear,
        amountsAdvanced: newValues.amountsAdvanced,
        amountsRepaid: newValues.amountsRepaid,
        amountsWrittenOff: newValues.amountsWrittenOff,
        amountsWaived: newValues.amountsWaived,
        balanceOutstandingAtEndOfYear: this.calculateBalanceOutstandingEndYear(newValues),
        advanceCreditConditions: newValues.advanceCreditConditions
      };

    this.advancesCreditAndGuaranteesGrantedToDirectorsExtended.guarantees =
      this.sectionReference.director.index === 1 ? newValues.guarantees : null;

    let advancesCreditAndGuaranteesGrantedToDirectorItem =
      this.advancesCreditAndGuaranteesGrantedToDirectorsExtended?.items?.find(
        c => c.involvementClientGuid == this.sectionReference.director.involvementClientGuid
      );

    if (!advancesCreditAndGuaranteesGrantedToDirectorItem) {
      this.advancesCreditAndGuaranteesGrantedToDirectorsExtended.items.push(
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem
      );
    } else {
      advancesCreditAndGuaranteesGrantedToDirectorItem.balanceOutstandingAtStartOfYear =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.balanceOutstandingAtStartOfYear;
      advancesCreditAndGuaranteesGrantedToDirectorItem.amountsAdvanced =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsAdvanced;
      advancesCreditAndGuaranteesGrantedToDirectorItem.amountsRepaid =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsRepaid;
      advancesCreditAndGuaranteesGrantedToDirectorItem.amountsWrittenOff =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsWrittenOff;
      advancesCreditAndGuaranteesGrantedToDirectorItem.amountsWaived =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.amountsWaived;
      advancesCreditAndGuaranteesGrantedToDirectorItem.balanceOutstandingAtEndOfYear =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.balanceOutstandingAtEndOfYear;
      advancesCreditAndGuaranteesGrantedToDirectorItem.advanceCreditConditions =
        newAdvancesCreditAndGuaranteesGrantedToDirectorsExtendedItem.advanceCreditConditions;
    }
    this.reportNotesService.updateReportNotesDataValues(
      this.advancesCreditAndGuaranteesGrantedToDirectorsExtended,
      this.sectionReference
    );
  }

  updateBalanceOutstandingAtStartOfYear({ detail }): void {
    this.directorInfoForm.patchValue({
      balanceOutstandingAtStartOfYear: detail?.length && !isNaN(detail) ? +detail : null
    });
  }

  updateAmountsAdvanced({ detail }): void {
    this.directorInfoForm.patchValue({
      amountsAdvanced: detail?.length && !isNaN(detail) ? +detail : null
    });
  }

  updateAmountsRepaid({ detail }): void {
    this.directorInfoForm.patchValue({
      amountsRepaid: detail?.length && !isNaN(detail) ? +detail : null
    });
  }

  updateAmountsWrittenOff({ detail }): void {
    this.directorInfoForm.patchValue({
      amountsWrittenOff: detail?.length && !isNaN(detail) ? +detail : null
    });
  }

  updateAmountsWaived({ detail }): void {
    this.directorInfoForm.patchValue({
      amountsWaived: detail?.length && !isNaN(detail) ? +detail : null
    });
  }

  updateAdvanceCreditConditions({ detail }): void {
    this.advancesCredit = detail;
    this.directorInfoForm.patchValue({ advanceCreditConditions: detail });
  }

  updateGuarantees({ detail }): void {
    this.directorInfoForm.patchValue({ guarantees: detail });
  }
}
