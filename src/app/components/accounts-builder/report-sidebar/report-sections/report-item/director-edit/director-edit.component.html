<div class="item-section">
    <div class="current-value-input">
        <iris-numeric-input label="Balance outstanding at start of year"
        [value]="advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.balanceOutstandingAtStartOfYear"
        (valueChanged)="updateBalanceOutstandingAtStartOfYear($event)"
        [minValue]="MIN_VALUE_LENGTH"
        [maxValue]="MAX_VALUE_LENGTH"
        currency-symbol="£"
        placeholder-value="£0.00"></iris-numeric-input>
    </div>
    <div class="current-value-input">
        <iris-numeric-input label="Amounts advanced"
        (valueChanged)="updateAmountsAdvanced($event)"
        [value]="advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsAdvanced"
        [minValue]="MIN_VALUE_LENGTH"
        currency-symbol="£"
        [maxValue]="MAX_VALUE_LENGTH" placeholder-value="£0.00"></iris-numeric-input>
    </div>
    <div class="current-value-input">
        <iris-numeric-input label="Amounts repaid"
        (valueChanged)="updateAmountsRepaid($event)"
        [value]="advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsRepaid"
        [minValue]="MIN_VALUE_LENGTH"
        currency-symbol="£"
        [maxValue]="MAX_VALUE_LENGTH" placeholder-value="£0.00"></iris-numeric-input>
    </div>
    <div class="current-value-input">
        <iris-numeric-input label="Amounts written off"
        (valueChanged)="updateAmountsWrittenOff($event)"
        [value]="advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsWrittenOff"
        [minValue]="MIN_VALUE_LENGTH"
        currency-symbol="£"
        [maxValue]="MAX_VALUE_LENGTH" placeholder-value="£0.00"></iris-numeric-input>
    </div>
    <div class="current-value-input">
        <iris-numeric-input label="Amounts waived"
        (valueChanged)="updateAmountsWaived($event)"
        [value]="advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.amountsWaived"
        [minValue]="MIN_VALUE_LENGTH"
        currency-symbol="£"
        [maxValue]="MAX_VALUE_LENGTH" 
        placeholder-value="£0.00"></iris-numeric-input>
    </div>
</div>

<div class="balance-section">
    <div class="current-value-input">
        <iris-numeric-input placeholder-value="£0.00" label="Balance outstanding at end of year" 
        currency-symbol="£"
        controlled="true"
        [disabled]="readonly"
        [value]="balanceOutstandingEndYear"></iris-numeric-input>
    </div>
</div>

<div class="item-section">
    <div class="current-value-input">
        <iris-textarea label="Provide an indication of the interest rate and the main conditions
        applying to the advance or credit:"
        (changed)="updateAdvanceCreditConditions($event)"
        [maxlength]="MAX_TEXTAREA_LENGTH"
        [value]="advancesCreditAndGuaranteesGrantedToDirectorsExtendedItem?.advanceCreditConditions" resize="vertical"></iris-textarea>
        <p class="helper-text">{{MAX_TEXTAREA_LENGTH - advancesCredit.length}} characters remaining</p>
    </div>

    <div>
        <iris-textarea label="Guarantees"
        [maxlength]="MAX_TEXTAREA_LENGTH"
        [disabled]="sectionReference.director.index !== 1"
        (changed)="updateGuarantees($event)"
        [value]="sectionReference.director.index === 1 ? advancesCreditAndGuaranteesGrantedToDirectorsExtended?.guarantees : null" resize="vertical"></iris-textarea>
        <p *ngIf="sectionReference.director.index === 1" class="helper-text">{{MAX_TEXTAREA_LENGTH - advancesCreditAndGuaranteesGrantedToDirectorsExtended?.guarantees?.length}} characters remaining</p>
    </div>
</div>