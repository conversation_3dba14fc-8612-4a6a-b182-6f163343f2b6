import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import {
  AdvancesCreditAndGuaranteesGrantedToDirectorsExtended,
  NotesFormTypeEnum
} from 'src/app/models/report-sections/report-notes.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';
import { DirectorEditComponent } from './director-edit.component';

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'getReportNotesData',
  'updateReportNotesDataValues'
]);

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.ADVANCES_CREDITS,
  director: {
    index: 1,
    involvementClientGuid: '23-345-569',
    directorName: 'John'
  },
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_ADVANCES_CREDIT_DIRECTORS: AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = {
  guarantees: 'guarantees',
  items: [
    {
      index: 1,
      involvementClientGuid: '23-345-569',
      directorName: 'John',
      balanceOutstandingAtStartOfYear: 231,
      amountsAdvanced: 34,
      amountsRepaid: 345,
      amountsWrittenOff: 456,
      amountsWaived: 234,
      balanceOutstandingAtEndOfYear: 456,
      advanceCreditConditions: 'advanceCredit'
    },
    {
      index: 2,
      involvementClientGuid: '23-345-345',
      directorName: 'John',
      balanceOutstandingAtStartOfYear: 231,
      amountsAdvanced: 34,
      amountsRepaid: 345,
      amountsWrittenOff: 456,
      amountsWaived: 234,
      balanceOutstandingAtEndOfYear: 456,
      advanceCreditConditions: 'advanceCredit'
    }
  ]
};

describe('DirectorEditComponent', () => {
  let component: DirectorEditComponent;
  let fixture: ComponentFixture<DirectorEditComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DirectorEditComponent],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DirectorEditComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_ADVANCES_CREDIT_DIRECTORS);
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call the get new instance', () => {
    expect(component.getNewInstance().items.length).toBe(0);
  });

  describe('updateBalanceOutstandingAtStartOfYear', () => {
    it('should replace the value with the new value', () => {
      const patchValuesSpy = spyOn(component.directorInfoForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateBalanceOutstandingAtStartOfYear({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ balanceOutstandingAtStartOfYear: MOCK_EVENT.detail });
    });
  });

  describe('updateAmountsAdvanced', () => {
    it('should replace the amount advanced with the new value', () => {
      const patchValuesSpy = spyOn(component.directorInfoForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateAmountsAdvanced({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ amountsAdvanced: MOCK_EVENT.detail });
    });
  });

  describe('updateAmountsAdvanced', () => {
    it('should replace the value with the new value', () => {
      const patchValuesSpy = spyOn(component.directorInfoForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateAmountsAdvanced({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ amountsAdvanced: MOCK_EVENT.detail });
    });
  });

  describe('updateAmountsRepaid', () => {
    it('should replace the amounts repaid with the new value', () => {
      const patchValuesSpy = spyOn(component.directorInfoForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateAmountsRepaid({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ amountsRepaid: MOCK_EVENT.detail });
    });
  });

  describe('updateAmountsWrittenOff', () => {
    it('should replace the amounts written off with the new value', () => {
      const patchValuesSpy = spyOn(component.directorInfoForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateAmountsWrittenOff({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ amountsWrittenOff: MOCK_EVENT.detail });
    });
  });

  describe('updateAmountsWaived', () => {
    it('should replace the amounts waived with the new value', () => {
      const patchValuesSpy = spyOn(component.directorInfoForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateAmountsWaived({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ amountsWaived: MOCK_EVENT.detail });
    });
  });

  describe('updateAdvanceCreditConditions', () => {
    it('should replace the advance credit conditions with the new value', () => {
      const patchDescriptionSpy = spyOn(component.directorInfoForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated description'
      };
      component.updateAdvanceCreditConditions(MOCK_EVENT);
      expect(patchDescriptionSpy).toHaveBeenCalledWith({ advanceCreditConditions: MOCK_EVENT.detail });
    });
  });

  describe('updateGuarantees', () => {
    it('should replace the guarantees with the new value', () => {
      const patchDescriptionSpy = spyOn(component.directorInfoForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated Guarantees'
      };
      component.updateGuarantees(MOCK_EVENT);
      expect(patchDescriptionSpy).toHaveBeenCalledWith({ guarantees: MOCK_EVENT.detail });
    });
  });

  describe('updateFormValue', () => {
    it('should update the form value', () => {
      component.advancesCreditAndGuaranteesGrantedToDirectorsExtended = MOCK_ADVANCES_CREDIT_DIRECTORS;
      const event = {
        index: 3,
        involvementClientGuid: '23-345-345',
        directorName: 'John',
        balanceOutstandingAtStartOfYear: 231,
        amountsAdvanced: 34,
        amountsRepaid: 345,
        amountsWrittenOff: 456,
        amountsWaived: 234,
        balanceOutstandingAtEndOfYear: 456,
        advanceCreditConditions: 'advanceCredit'
      };
      component.updateFormValue(event);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_ADVANCES_CREDIT_DIRECTORS,
        MOCK_SECTION_REF
      );
    });

    it('should created the new form value', () => {
      component.sectionReference = {
        parent: null,
        children: null,
        isMandatory: false,
        hasData: false,
        formConfigKey: NotesFormTypeEnum.ADVANCES_CREDITS,
        director: {
          index: 1,
          involvementClientGuid: '23-345-569-987',
          directorName: 'John'
        },
        label: 'mock section',
        errorCount: 0,
        warningCount: 0
      };
      const newValue = {
        balanceOutstandingAtStartOfYear: 231,
        amountsAdvanced: 34,
        amountsRepaid: 345,
        amountsWrittenOff: 456,
        amountsWaived: 234,
        advanceCreditConditions: 'advanceCredit'
      };
      component.updateFormValue(newValue);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_ADVANCES_CREDIT_DIRECTORS,
        component.sectionReference
      );
    });
  });

  describe('balanceOutstandingEndYear', () => {
    it('should balance outstanding end year 0 if the form has empty', () => {
      component.calculateBalanceOutstandingEndYear(null);
      expect(component.balanceOutstandingEndYear).toBe(0);
    });
  });
});
