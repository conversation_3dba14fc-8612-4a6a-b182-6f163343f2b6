import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReportItemComponent } from './report-item.component';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';

describe('ReportItemComponent', () => {
  let component: ReportItemComponent;
  let fixture: ComponentFixture<ReportItemComponent>;

  const MOCK_SECTIONS_REFERENCE: ReportSection = {
    parent: null,
    label: 'Sections reference',
    isMandatory: false,
    hasData: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ReportItemComponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ReportItemComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTIONS_REFERENCE;
    fixture.detectChanges();
  });

  beforeEach(() => {
    component.isActive = false;
    component.isHovered = false;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should select the perticular section.', () => {
    spyOn(component.onSelect, 'emit');
    component.onSectionSelect();
    expect(component.onSelect.emit).toHaveBeenCalledWith(MOCK_SECTIONS_REFERENCE);
  });

  it('should edit the navigation section.', () => {
    spyOn(component.onEdit, 'emit');
    component.onEditTrigger();
    expect(component.onEdit.emit).toHaveBeenCalledWith(MOCK_SECTIONS_REFERENCE);
  });

  it('should highlighted the section.', () => {
    component.toggleHover(true);
    expect(component.isHovered).toBeTruthy();
  });

  describe('ngOnChanges', () => {
    it('should update the chevron and the button', () => {
      const setChevronBackgroundColorSpy = spyOn(component, 'setChevronBackgroundColor');
      const setChevronIconColorSpy = spyOn(component, 'setChevronIconColor');
      const setChevronBorderColorSpy = spyOn(component, 'setChevronBorderColor');

      component.ngOnChanges({});
      expect(setChevronBackgroundColorSpy).toHaveBeenCalled();
      expect(setChevronIconColorSpy).toHaveBeenCalled();
      expect(setChevronBorderColorSpy).toHaveBeenCalled();
    });

    it('should trigger the scroll into view flag', () => {
      const MOCK_SIMPLE_CHANGES = {
        isActive: {
          currentValue: true,
          previousValue: false,
          firstChange: false,
          isFirstChange: null
        }
      };
      component.triggerScrollIntoView = false;
      component.ngOnChanges(MOCK_SIMPLE_CHANGES);
      expect(component.triggerScrollIntoView).toBe(true);
    });

    it('should NOT trigger the scroll into view flag when section is NOT active', () => {
      const MOCK_SIMPLE_CHANGES = {
        isActive: {
          currentValue: false,
          previousValue: true,
          firstChange: false,
          isFirstChange: null
        }
      };
      component.triggerScrollIntoView = false;
      component.ngOnChanges(MOCK_SIMPLE_CHANGES);
      expect(component.triggerScrollIntoView).toBe(false);
    });
  });

  describe('ngAfterViewChecked', () => {
    it('should trigger a scroll into view', () => {
      const scrollIntoViewSpy = spyOn(component.sectionItem.nativeElement, 'scrollIntoView');
      component.triggerScrollIntoView = true;
      component.ngAfterViewChecked();
      expect(component.triggerScrollIntoView).toBe(false);
      expect(scrollIntoViewSpy).toHaveBeenCalled();
    });

    it('should trigger a scroll into view', () => {
      const scrollIntoViewSpy = spyOn(component.sectionItem.nativeElement, 'scrollIntoView');
      component.triggerScrollIntoView = false;
      component.ngAfterViewChecked();
      expect(scrollIntoViewSpy).not.toHaveBeenCalled();
    });
  });

  describe('setChevronIconColor', () => {
    it('should return icon colors when is mandatory', () => {
      component.sectionReference.isMandatory = true;
      component.setChevronIconColor();
      expect(component.chevronIconColor).toBe('#505962');

      component.isActive = true;
      component.setChevronIconColor();
      expect(component.chevronIconColor).toBe('#ffffff');
    });

    it('should return icon colors when is NOT mandatory and has data', () => {
      component.sectionReference.isMandatory = false;
      component.sectionReference.hasData = true;
      component.setChevronIconColor();
      expect(component.chevronIconColor).toBe('#1B69B9');

      component.isActive = true;
      component.setChevronIconColor();
      expect(component.chevronIconColor).toBe('#ffffff');
    });

    it('should return icon colors when is NOT mandatory and has NO data', () => {
      component.sectionReference.isMandatory = false;
      component.sectionReference.hasData = false;
      component.setChevronIconColor();
      expect(component.chevronIconColor).toBe('#ffffff');
    });
  });

  describe('setChevronBackgroundColor', () => {
    it('should return background colors when is mandatory', () => {
      component.sectionReference.isMandatory = true;
      component.setChevronBackgroundColor();
      expect(component.chevronBackgroundColor).toBe('#ffffff');

      component.isActive = true;
      component.setChevronBackgroundColor();
      expect(component.chevronBackgroundColor).toBe('#505962');
    });

    it('should return icon colors when is NOT mandatory', () => {
      component.sectionReference.isMandatory = false;
      component.sectionReference.hasData = true;
      component.setChevronBackgroundColor();
      expect(component.chevronBackgroundColor).toBe('#ffffff');

      component.isActive = true;
      component.setChevronBackgroundColor();
      expect(component.chevronBackgroundColor).toBe('#1B69B9');
    });
  });

  describe('setChevronBorderColor', () => {
    it('should return border colors when is mandatory', () => {
      component.sectionReference.isMandatory = true;
      component.setChevronBorderColor();
      expect(component.chevronBorderColor).toBe('transparent');
    });

    it('should return border colors when is NOT mandatory and has data', () => {
      component.sectionReference.isMandatory = false;
      component.sectionReference.hasData = true;
      component.setChevronBorderColor();
      expect(component.chevronBorderColor).toBe('#1B69B9');

      component.isActive = true;
      component.setChevronBorderColor();
      expect(component.chevronBorderColor).toBe('transparent');
    });

    it('should return border colors when is NOT mandatory and has NO data', () => {
      component.sectionReference.isMandatory = false;
      component.sectionReference.hasData = false;
      component.setChevronBorderColor();
      expect(component.chevronBorderColor).toBe('#666E76');

      component.isActive = true;
      component.setChevronBorderColor();
      expect(component.chevronBorderColor).toBe('transparent');
    });
  });

  describe('setEditButtonText', () => {
    beforeEach(() => {
      component.sectionReference.isMandatory = false;
      component.sectionReference.hasData = false;
      component.sectionReference.errorCount = 0;
      component.sectionReference.warningCount = 0;
    });
    it('should return the Add and Edit button text when section not mandatory and there is no data', () => {
      expect(component.setEditButtonText()).toBe('Add and Edit');
    });

    it('should return Edit button text when section is mandatory or there it has data', () => {
      component.sectionReference.isMandatory = true;
      expect(component.setEditButtonText()).toBe('Edit');

      component.sectionReference.hasData = true;
      expect(component.setEditButtonText()).toBe('Edit');
    });

    it('should return Fix when there is at least a error or warning', () => {
      component.sectionReference.errorCount = 1;
      expect(component.setEditButtonText()).toBe('Fix');

      component.sectionReference.errorCount = 0;
      component.sectionReference.warningCount = 1;
      expect(component.setEditButtonText()).toBe('Fix');
    });
  });

  describe('setAlertIconColor', () => {
    it('should set the alertIconColor to the error hex when the error flag is true', () => {
      component.sectionReference.errorCount = 1;
      component.setAlertIconColor();
      expect(component.alertIconColor).toBe('#C8102E');
    });

    it('should set the alertIconColor to the warning hex when the warning flag is true', () => {
      component.sectionReference.errorCount = 0;
      component.sectionReference.warningCount = 1;
      component.setAlertIconColor();
      expect(component.alertIconColor).toBe('#FF9504');
    });

    it('should leave the alertIconColor unchanged when the alert flags are false', () => {
      const MOCK_INITIAL_ALERT_COLOR = 'mock color';
      component.alertIconColor = MOCK_INITIAL_ALERT_COLOR;
      component.sectionReference.errorCount = 0;
      component.sectionReference.warningCount = 0;
      component.setAlertIconColor();
      expect(component.alertIconColor).toBe(MOCK_INITIAL_ALERT_COLOR);
    });
  });

  describe('setSectionHelperText', () => {
    it('should set the warning message when there is a warning and no error', () => {
      component.sectionReference.errorCount = 0;
      component.sectionReference.warningCount = 1;
      component.setSectionHelperText();
      expect(component.sectionHelperText).toBe(
        'The Note will not be included in the submitted Report, due to missing fields'
      );
    });

    it('should set helper text empty when there is no warning or error', () => {
      component.sectionReference.errorCount = 0;
      component.sectionReference.warningCount = 0;
      component.setSectionHelperText();
      expect(component.sectionHelperText).toBe('');
    });
  });
  describe('hasFormKey', () => {
    it('should return false if there is no form key', () => {
      component.sectionReference.formConfigKey = undefined;
      expect(component.hasFormKey()).toBe(false);
    });
  });
});
