<div class="section-item-container" #sectionItem>
    <div class="section-item" 
        [ngClass]="{'section-item--active': isActive, 'section-item--first': isFirstSection, 'section-item--last': isLastSection}" 
        (click)="onSectionSelect()" 
        (mouseenter)="toggleHover(true)" 
        (mouseleave)="toggleHover(false)">
        <div class="item-chevron-container">
            <iris-icon [borderColour]="chevronBorderColor" [iconColour]="chevronIconColor" [backgroundColour]="chevronBackgroundColor" [type]=" !sectionReference.isMandatory && !sectionReference.hasData ? 'plus' : 'chevron-right'" size="18" padding="2" shape="circle"></iris-icon>
        </div>
        <div class="item-content-container">
            <div class="title-container">
                <div class="truncated-title">
                    <h2 class="title">{{sectionReference?.label}}</h2>
                </div>
                <div class="icons-list">
                    <iris-icon *ngIf="sectionReference.errorCount || sectionReference.warningCount" [iconColour]="alertIconColor" background-colour="transparent" type="alert-triangle" size="24" padding="0" shape="circle"></iris-icon>
                    <iris-icon *ngIf="isActive || isHovered" [iconColour]="isActive || isHovered ? '#505962' : '#ffffff'" background-colour="transparent" type="more-vertical" size="24" padding="0" shape="circle"></iris-icon>
                </div>
            </div>
            <ng-container *ngIf="!sectionReference.errorCount; else errorMessages">
                <p *ngIf="isActive && !sectionReference.isMandatory && (sectionReference.formConfigKey || sectionReference.children?.length)" class="helper-text">
                    <ng-container *ngIf="sectionReference.validationMessage?.optionalMessage; else validationMessages">
                        {{sectionReference.validationMessage.optionalMessage}}
                    </ng-container>
                    <ng-template #validationMessages>
                        This is an Optional Section. To include it in your report, please fill at least the mandatory <span class="required">*</span> fields from inside
                    </ng-template>
                </p>
                <p *ngIf="(isActive && sectionReference.isMandatory) && (sectionReference.formConfigKey || sectionReference.children?.length)" class="helper-text">
                    <ng-container *ngIf="sectionReference.validationMessage?.mandatoryMessage; else validationMessages">
                        {{sectionReference.validationMessage.mandatoryMessage}}
                    </ng-container>
                    <ng-template #validationMessages>
                        This is a Mandatory Section, click to Customise it or fill any missing data in mandatory fields marked with the asterisk <span class="required">*</span>
                    </ng-template>
                </p>
                <p *ngIf="isActive" class="helper-text">
                    {{sectionHelperText}}
                </p>
            </ng-container>
            <ng-template #errorMessages>
                <p *ngIf="isActive" class="helper-text">
                    This section must be included in the report but it has missing information, please fill all mandatory <span class="required">*</span> fields
                </p>
            </ng-template>
        </div>
    </div>
    <button *ngIf="isActive && (sectionReference.children?.length || hasFormKey())" class="btn-customize" (click)="onEditTrigger()">
        <span>{{setEditButtonText()}}</span>
        <iris-icon class="right-arrow" icon-colour="#fff" background-colour="#1B69B9"  type="arrow-right" size="20" padding="2" shape="circle"></iris-icon>
    </button>
</div>