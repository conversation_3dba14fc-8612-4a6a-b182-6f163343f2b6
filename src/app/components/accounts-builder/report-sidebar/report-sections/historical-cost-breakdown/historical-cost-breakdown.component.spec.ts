import { ComponentFixture, TestBed, tick } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import {
  HistoricalCostBreakdown,
  NotesFormTypeEnum,
  TangibleFixedAssetsNotes,
  ValuationInCurrentReportingPeriod
} from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { ReportNotesService } from 'src/app/services/report-notes.service';
import { HistoricCostBreakdown } from './historical-cost-breakdown.component';

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'getReportNotesData',
  'updateReportNotesDataValues'
]);

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_REPORT_HISTORIC_DATA: HistoricalCostBreakdown = {
  revaluedAssetClass: 'historicDetails',
  revaluedClassPronoun: 'revaluedClassPronoun',
  currentReportingPeriodCost: 1,
  currentReportingPeriodAccumulatedDepreciation: 2
};

const MOCK_TANGIBLE_FIXED_ASSETS_NOTES: TangibleFixedAssetsNotes = {
  historicalCostBreakdown: MOCK_REPORT_HISTORIC_DATA
};

describe('HistoricCostBreakdown', () => {
  let component: HistoricCostBreakdown;
  let fixture: ComponentFixture<HistoricCostBreakdown>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HistoricCostBreakdown, CommaDelimiterPipe],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HistoricCostBreakdown);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_TANGIBLE_FIXED_ASSETS_NOTES);
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize the component', () => {
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.historicalCostBreakdown.revaluedAssetClass
      );
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.historicalCostBreakdown.revaluedClassPronoun
      );
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodCost).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.historicalCostBreakdown.currentReportingPeriodCost
      );
      expect(
        component.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation
      ).toBe(MOCK_TANGIBLE_FIXED_ASSETS_NOTES.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation);
    });

    it('should initialize the historicalCostBreakdown object', () => {
      const MOCK_TANGIBLE_FIXED_ASSETS_NOTES: TangibleFixedAssetsNotes = {
        historicalCostBreakdown: null
      };
      reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_TANGIBLE_FIXED_ASSETS_NOTES);
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass).toBe('');
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun).toBe('');
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodCost).toBe(null);
      expect(
        component.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation
      ).toBe(null);
    });

    it('should create the instance if the return value gets null', () => {
      reportNotesServiceMock.getReportNotesData.and.returnValue(null);
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass).toEqual('');
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun).toEqual('');
    });
  });

  describe('updateValuationDetails', () => {
    it('should update the revalued asset class details with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.historicalCostBreakdownForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated revalued asset class'
      };
      component.updateRevaluedAssetClass(MOCK_EVENT);
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass).toBe(MOCK_EVENT.detail);
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ revaluedAssetClass: MOCK_EVENT.detail });
    });
  });

  describe('updateIndependentValuerInvolved', () => {
    it('should update the revalued class pronoun value', () => {
      const builderPatchValuesSpy = spyOn(component.historicalCostBreakdownForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated revalued class pronoun'
      };
      component.updateRevaluedClassPronoun(MOCK_EVENT);
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun).toBe(MOCK_EVENT.detail);
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ revaluedClassPronoun: MOCK_EVENT.detail });
    });
  });

  describe('current reporting period cost', () => {
    it('should update the current reporting period cost with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.historicalCostBreakdownForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 1
      };
      component.updateCurrentReportingPeriodCost(MOCK_EVENT);
      expect(component.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodCost).toBe(
        MOCK_EVENT.detail
      );
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ currentReportingPeriodCost: MOCK_EVENT.detail });
    });
  });

  describe('current reporting accumulated depreciation period cost', () => {
    it('should update the current reporting accumulated depreciation cost with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.historicalCostBreakdownForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 2
      };
      component.updateCurrentReportingPeriodAccumulatedDepreciation(MOCK_EVENT);
      expect(
        component.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation
      ).toBe(MOCK_EVENT.detail);
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({
        currentReportingPeriodAccumulatedDepreciation: MOCK_EVENT.detail
      });
    });
  });

  describe('updateHistoricalCostBreakdown', () => {
    it('should trigger service update with the new values', () => {
      const MOCK_NEW_VALUES = {
        revaluedAssetClass: 'historicDetails',
        revaluedClassPronoun: 'revaluedClassPronoun',
        currentReportingPeriodCost: 1,
        currentReportingPeriodAccumulatedDepreciation: 2
      };
      component.updateHistoricalCostBreakdown(MOCK_NEW_VALUES);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES,
        MOCK_SECTION_REF,
        false,
        null,
        NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
      );
    });
    it('should trigger service update with hasError true when mandatory field is null', () => {
      const MOCK_TANGIBLE_FIXED_ASSETS_NOTES: TangibleFixedAssetsNotes = {
        historicalCostBreakdown: null,
        analysisOfCostOrValuation: {
          costLandAndBuildings: 1,
          costPlantAndMachineryEtc: 2,
          totalLandAndBuildings: 4,
          totalPlantAndMachineryEtc: 5,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 2004,
              landAndBuildings: 1,
              plantAndMachineryEtc: 1
            },
            {
              index: 2,
              year: 2005,
              landAndBuildings: 2,
              plantAndMachineryEtc: 2
            }
          ]
        }
      };
      reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_TANGIBLE_FIXED_ASSETS_NOTES);

      component.ngOnInit();

      const MOCK_NEW_VALUES = {
        revaluedAssetClass: null,
        revaluedClassPronoun: 'revaluedClassPronoun',
        currentReportingPeriodCost: 1,
        currentReportingPeriodAccumulatedDepreciation: 2
      };
      const MOCK_EVENT = {
        detail: null
      };
      component.updateRevaluedAssetClass(MOCK_EVENT);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES,
        MOCK_SECTION_REF,
        true,
        null,
        NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
      );
    });
  });

  describe('isHistoricalCostBreakdownMandatory', () => {
    it('should be true when analyis of cost or valuation is present', () => {
      component.tangibleFixedAssetsNotes = {
        historicalCostBreakdown: MOCK_REPORT_HISTORIC_DATA,
        analysisOfCostOrValuation: {
          costLandAndBuildings: 1,
          costPlantAndMachineryEtc: 2,
          totalLandAndBuildings: 5,
          totalPlantAndMachineryEtc: 5,
          analysisOfCostOrValuationItems: [
            {
              index: 1,
              year: 2004,
              landAndBuildings: 1,
              plantAndMachineryEtc: 1
            },
            {
              index: 2,
              year: 2005,
              landAndBuildings: 2,
              plantAndMachineryEtc: 2
            }
          ]
        }
      };

      expect(component.isHistoricalCostBreakdownMandatory).toBe(true);
    });

    it('should be false when analyis of cost or valuation is not present', () => {
      component.tangibleFixedAssetsNotes = {
        historicalCostBreakdown: MOCK_REPORT_HISTORIC_DATA,
        analysisOfCostOrValuation: {
          costLandAndBuildings: null,
          costPlantAndMachineryEtc: null,
          totalLandAndBuildings: null,
          totalPlantAndMachineryEtc: null,
          analysisOfCostOrValuationItems: []
        }
      };

      expect(component.isHistoricalCostBreakdownMandatory).toBe(false);
    });
  });
});
