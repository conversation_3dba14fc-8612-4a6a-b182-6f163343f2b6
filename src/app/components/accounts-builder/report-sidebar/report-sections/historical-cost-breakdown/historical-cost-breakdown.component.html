<div class="assets-basis-form">
    <div class="note-input p-b-0">
        <iris-input
            label="Enter revalued asset class e.g. freehold property"
            [required]="isHistoricalCostBreakdownMandatory"
            [value]="tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass"
            [maxlength]="MAX_INPUT_LENGTH"
            (changed)="updateRevaluedAssetClass($event)"
            label-style="normal"
        ></iris-input>
        <p class="count-text">{{MAX_INPUT_LENGTH - tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass?.length}} characters remaining</p>
    </div>
    <div class="note-input p-b-0">
        <iris-input
            label="Enter revalued class pronoun"
            [required]="isHistoricalCostBreakdownMandatory"
            [value]="tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun"
            [maxlength]="MAX_INPUT_LENGTH"
            (changed)="updateRevaluedClassPronoun($event)"
            label-style="normal"
        ></iris-input>
        <p class="count-text">{{MAX_INPUT_LENGTH - tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun?.length}} characters remaining</p>
    </div>
    <div class="note-input p-b-0">
        <iris-numeric-input
            label="Current reporting period - cost"
            currency-symbol="£"
            placeholder-value="£0.00"
            label-style="normal"
            [value]="tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodCost"
            (valueChanged)="updateCurrentReportingPeriodCost($event)"
            [minValue]="MIN_VALUE_LENGTH"
            [maxValue]="MAX_VALUE_LENGTH"
        ></iris-numeric-input>
        
    </div>
    <div class="note-input">
        <iris-numeric-input
            label="Current reporting period - accumulated depreciation"
            currency-symbol="£"
            placeholder-value="£0.00"
            label-style="normal"
            [value]="tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation"
            (valueChanged)="updateCurrentReportingPeriodAccumulatedDepreciation($event)"
            [minValue]="MIN_VALUE_LENGTH"
            [maxValue]="MAX_VALUE_LENGTH"
        ></iris-numeric-input>
    </div>
</div>