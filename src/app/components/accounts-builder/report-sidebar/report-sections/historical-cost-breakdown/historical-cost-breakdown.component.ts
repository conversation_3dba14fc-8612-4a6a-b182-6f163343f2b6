import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
  HistoricalCostBreakdown,
  NotesFormTypeEnum,
  TangibleFixedAssetsNotes
} from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-historical-cost-breakdown',
  templateUrl: './historical-cost-breakdown.component.html',
  styleUrls: ['./historical-cost-breakdown.component.scss']
})
export class HistoricCostBreakdown implements OnInit {
  @Input() sectionReference: ReportSection;

  MAX_INPUT_LENGTH = 120;
  MIN_VALUE_LENGTH = -***********.99;
  MAX_VALUE_LENGTH = ***********.99;
  historicalCostBreakdownForm: UntypedFormGroup;

  tangibleFixedAssetsNotes: TangibleFixedAssetsNotes;

  constructor(private _formBuilder: UntypedFormBuilder, private reportNotesService: ReportNotesService) {}

  ngOnInit() {
    this.initData();
    this.buildForm();
  }

  initData() {
    const notesName = NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES;
    this.tangibleFixedAssetsNotes = this.reportNotesService.getReportNotesData(notesName);
    if (!this.tangibleFixedAssetsNotes) {
      this.tangibleFixedAssetsNotes = this.getNewInstance();
    } else if (!this.tangibleFixedAssetsNotes.historicalCostBreakdown) {
      this.tangibleFixedAssetsNotes.historicalCostBreakdown = this.getNewHistoricalCostBreakdownInstance();
    }
  }

  buildForm() {
    this.historicalCostBreakdownForm = this._formBuilder.group({
      revaluedAssetClass: [
        this.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass,
        this.isHistoricalCostBreakdownMandatory ? Validators.required : null
      ],
      revaluedClassPronoun: [
        this.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun,
        this.isHistoricalCostBreakdownMandatory ? Validators.required : null
      ],
      currentReportingPeriodCost: [this.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodCost],
      currentReportingPeriodAccumulatedDepreciation: [
        this.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation
      ]
    });

    this.subscribeHistoricalCostBreakdownForm();
  }

  get isHistoricalCostBreakdownMandatory() {
    return (
      !!this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.totalLandAndBuildings ||
      !!this.tangibleFixedAssetsNotes?.analysisOfCostOrValuation?.totalPlantAndMachineryEtc
    );
  }

  subscribeHistoricalCostBreakdownForm() {
    this.historicalCostBreakdownForm.valueChanges.subscribe(newValues => {
      this.updateHistoricalCostBreakdown(newValues);
    });
  }

  updateRevaluedAssetClass({ detail }) {
    this.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedAssetClass = detail;
    this.historicalCostBreakdownForm.patchValue({ revaluedAssetClass: detail });
  }

  updateRevaluedClassPronoun({ detail }): void {
    this.tangibleFixedAssetsNotes.historicalCostBreakdown.revaluedClassPronoun = detail;
    this.historicalCostBreakdownForm.patchValue({ revaluedClassPronoun: detail });
  }

  updateCurrentReportingPeriodCost({ detail }): void {
    this.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodCost = detail;
    this.historicalCostBreakdownForm.patchValue({ currentReportingPeriodCost: detail });
  }

  updateCurrentReportingPeriodAccumulatedDepreciation({ detail }): void {
    this.tangibleFixedAssetsNotes.historicalCostBreakdown.currentReportingPeriodAccumulatedDepreciation = detail;
    this.historicalCostBreakdownForm.patchValue({ currentReportingPeriodAccumulatedDepreciation: detail });
  }

  updateHistoricalCostBreakdown(newValues) {
    this.tangibleFixedAssetsNotes.historicalCostBreakdown = newValues;

    this.reportNotesService.updateReportNotesDataValues(
      this.tangibleFixedAssetsNotes,
      this.sectionReference,
      this.historicalCostBreakdownForm.invalid,
      null,
      NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
    );
  }

  private getNewInstance(): TangibleFixedAssetsNotes {
    return {
      historicalCostBreakdown: this.getNewHistoricalCostBreakdownInstance()
    };
  }

  private getNewHistoricalCostBreakdownInstance(): HistoricalCostBreakdown {
    return {
      revaluedAssetClass: '',
      revaluedClassPronoun: '',
      currentReportingPeriodCost: null,
      currentReportingPeriodAccumulatedDepreciation: null
    };
  }
}
