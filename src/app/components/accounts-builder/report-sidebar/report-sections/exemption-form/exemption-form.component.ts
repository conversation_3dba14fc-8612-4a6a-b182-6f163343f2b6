import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ExemptionsFinancialStatements, ExemptionTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-exemption-form',
  templateUrl: './exemption-form.component.html',
  styleUrls: ['./exemption-form.component.scss']
})
export class ExemptionFormComponent implements OnInit {
  @Input() sectionReference: ReportSection;

  MAX_TITLE_LENGTH = 120;
  MAX_TEXTAREA_LENGTH = 10000;
  exemptionTypeEnum = ExemptionTypeEnum;

  nameLabel = 'Name of parent';
  addressLabel = `Address of parent's registered office`;

  exemptionType = ExemptionTypeEnum.NO_EXEMPTION;
  nameValue: string = '';
  addressValue: string = '';
  textInputsDisabled: boolean = true;

  exemptionForm: UntypedFormGroup;

  constructor(private _formBuilder: UntypedFormBuilder, private accountingPoliciesService: AccountingPoliciesService) {}

  ngOnInit(): void {
    const formData: ExemptionsFinancialStatements = this.accountingPoliciesService.getCustomizationData(
      this.sectionReference.formConfigKey
    );
    this.exemptionType = formData.section !== null ? formData.section : ExemptionTypeEnum.NO_EXEMPTION;
    this.nameValue = formData.parentName ?? '';
    this.addressValue = formData.parentAddress ?? '';
    this.exemptionForm = this._formBuilder.group({
      section: [this.exemptionType],
      parentName: [this.nameValue],
      parentAddress: [this.addressValue]
    });
    this.updateInputsDisabledState();

    this.exemptionForm.valueChanges.subscribe(newValues => {
      this.updateExemptionsData(newValues);
    });
  }

  updateNameValue({ detail }): void {
    this.nameValue = detail;
    this.exemptionForm.patchValue({ parentName: detail });
  }

  updateAddressValue({ detail }): void {
    this.addressValue = detail;
    this.exemptionForm.patchValue({ parentAddress: detail });
  }

  updateSelectedSection(newSelectedOption: ExemptionTypeEnum): void {
    this.exemptionType = newSelectedOption;
    this.exemptionForm.patchValue({ section: newSelectedOption });
    this.updateInputsDisabledState();
  }

  updateInputsDisabledState(): void {
    if (this.exemptionType === ExemptionTypeEnum.SECTION_400 || this.exemptionType === ExemptionTypeEnum.SECTION_401) {
      this.textInputsDisabled = false;
    } else {
      this.textInputsDisabled = true;
      this.updateNameValue({ detail: '' });
      this.updateAddressValue({ detail: '' });
    }
  }

  updateExemptionsData(newValues: ExemptionsFinancialStatements): void {
    this.accountingPoliciesService.updateCustomizationDataValues(newValues, this.sectionReference);
  }
}
