import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import {
  AccountingPoliciesData,
  ExemptionTypeEnum,
  NotesFormTypeEnum
} from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';

import { ExemptionFormComponent } from './exemption-form.component';

const MOCK_SECTION_REF: ReportSection = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.EXEMPTION_FINANCIAL,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_CUSTOMIZATION_DATA: Partial<AccountingPoliciesData> = {
  exemptionsFinancialStatements: {
    section: 2,
    parentName: 'mock title',
    parentAddress: 'mock paragraph'
  }
};

describe('ExemptionFormComponent', () => {
  let component: ExemptionFormComponent;
  let fixture: ComponentFixture<ExemptionFormComponent>;
  const accountingPoliciesService = jasmine.createSpyObj('AccountingPoliciesService', [
    'updateCustomizationDataValues',
    'getCustomizationData'
  ]);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ExemptionFormComponent, CommaDelimiterPipe],
      providers: [
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesService
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ExemptionFormComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    accountingPoliciesService.getCustomizationData.and.returnValue(
      MOCK_CUSTOMIZATION_DATA.exemptionsFinancialStatements
    );
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.nameValue).toBe(MOCK_CUSTOMIZATION_DATA.exemptionsFinancialStatements.parentName);
    expect(component.addressValue).toBe(MOCK_CUSTOMIZATION_DATA.exemptionsFinancialStatements.parentAddress);
    expect(component.exemptionType).toBe(MOCK_CUSTOMIZATION_DATA.exemptionsFinancialStatements.section);
  });

  it('should initialize the inputs as empty strings when the data received is null', () => {
    accountingPoliciesService.getCustomizationData.and.returnValue({
      section: null,
      parentName: null,
      parentAddress: null
    });
    component.ngOnInit();
    expect(component.exemptionType).toBe(ExemptionTypeEnum.NO_EXEMPTION);
    expect(component.nameValue).toBe('');
    expect(component.addressValue).toBe('');
  });

  describe('updateNameValue', () => {
    it('should replace the current name with the new value', () => {
      const patchValuesSpy = spyOn(component.exemptionForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated parent name'
      };
      component.updateNameValue(MOCK_EVENT);
      expect(component.nameValue).toBe(MOCK_EVENT.detail);
      expect(patchValuesSpy).toHaveBeenCalledWith({ parentName: MOCK_EVENT.detail });
    });
  });

  describe('updateAddressValue', () => {
    it('should replace the current address with the new value', () => {
      const patchValuesSpy = spyOn(component.exemptionForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated address value'
      };
      component.updateAddressValue(MOCK_EVENT);
      expect(component.addressValue).toBe(MOCK_EVENT.detail);
      expect(patchValuesSpy).toHaveBeenCalledWith({ parentAddress: MOCK_EVENT.detail });
    });
  });

  describe('updateSelectedSection', () => {
    it('should replace the current selected option with the new option', () => {
      const patchValuesSpy = spyOn(component.exemptionForm, 'patchValue');
      const updateInputsDisabledStateSpy = spyOn(component, 'updateInputsDisabledState');
      component.updateSelectedSection(0);
      expect(updateInputsDisabledStateSpy).toHaveBeenCalled();
      expect(component.exemptionType).toBe(0);
      expect(patchValuesSpy).toHaveBeenCalledWith({ section: 0 });
    });
  });

  describe('updateInputsDisabledState', () => {
    it('disable and empty the inputs', () => {
      component.updateSelectedSection(0);
      expect(component.textInputsDisabled).toBe(true);
      expect(component.nameValue).toBe('');
      expect(component.addressValue).toBe('');
    });

    it('enable the inputs', () => {
      component.updateSelectedSection(3);
      expect(component.textInputsDisabled).toBe(false);
    });
  });

  describe('updateExemptionsData', () => {
    it('should call the updateCustomizationDataValue using the corect parameters', () => {
      const MOCK_NEW_VALUES = {
        section: 0,
        parentName: 'new name',
        parentAddress: 'new address'
      };

      component.updateExemptionsData(MOCK_NEW_VALUES);
      expect(accountingPoliciesService.updateCustomizationDataValues).toHaveBeenCalledWith(
        MOCK_NEW_VALUES,
        component.sectionReference
      );
    });
  });
});
