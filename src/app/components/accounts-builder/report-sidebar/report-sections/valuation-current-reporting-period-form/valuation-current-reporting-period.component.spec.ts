import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import {
  HistoricalCostBreakdown,
  NotesFormTypeEnum,
  TangibleFixedAssetsNotes,
  ValuationInCurrentReportingPeriod
} from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { ReportNotesService } from 'src/app/services/report-notes.service';

import { ValuationCurrentReportingPeriod } from './valuation-current-reporting-period.component';

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'getReportNotesData',
  'updateReportNotesDataValues'
]);

const PERIOD_DATA: AccountProductionPeriod = {
  id: 1,
  clientUuid: '123-321',
  status: { id: 1, name: 'InProgress' },
  endDate: '2025-05-10T00:00:00Z',
  periodId: '321-123'
};

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_REPORT_VALUATION_DATA: ValuationInCurrentReportingPeriod = {
  valuationDetails: 'valuationdetails',
  independentValuerInvolved: false,
  revaluationBasis: 'revaluationbasis',
  dateOfRevaluation: '2024-02-12'
};

const MOCK_TANGIBLE_FIXED_ASSETS_NOTES: TangibleFixedAssetsNotes = {
  valuationInCurrentReportingPeriod: MOCK_REPORT_VALUATION_DATA
};

describe('ValuationCurrentReportingPeriod', () => {
  let component: ValuationCurrentReportingPeriod;
  let fixture: ComponentFixture<ValuationCurrentReportingPeriod>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ValuationCurrentReportingPeriod, CommaDelimiterPipe],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ValuationCurrentReportingPeriod);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    component.periodData = PERIOD_DATA;
    reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_TANGIBLE_FIXED_ASSETS_NOTES);
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize the component', () => {
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.valuationDetails).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.valuationInCurrentReportingPeriod.valuationDetails
      );
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.valuationInCurrentReportingPeriod.independentValuerInvolved
      );
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.revaluationBasis).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.valuationInCurrentReportingPeriod.revaluationBasis
      );
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation).toBe(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES.valuationInCurrentReportingPeriod.dateOfRevaluation
      );
    });

    it('should set the object null', () => {
      const MOCK_TANGIBLE_FIXED_ASSETS_NOTES: TangibleFixedAssetsNotes = {
        valuationInCurrentReportingPeriod: null
      };
      reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_TANGIBLE_FIXED_ASSETS_NOTES);
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.valuationDetails).toBe('');
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved).toBe(
        false
      );
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.revaluationBasis).toBe('');
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation).toBe('2025-05-10');
    });

    it('should create the object', () => {
      reportNotesServiceMock.getReportNotesData.and.returnValue(null);
      component.ngOnInit();
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.valuationDetails).toBe('');
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved).toBe(
        false
      );
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.revaluationBasis).toBe('');
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation).toBe('2025-05-10');
    });
  });

  describe('updateValuationDetails', () => {
    it('should update the valuation details with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.valuationCurrentReportingForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated valuation details value'
      };
      component.updatevaluationDetails(MOCK_EVENT);
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.valuationDetails).toBe(
        MOCK_EVENT.detail
      );
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ valuationDetails: MOCK_EVENT.detail });
    });
  });

  describe('updateindependentValuerInvolved', () => {
    it('should update the checkbox value', () => {
      const MOCK_EVENT = {
        target: {
          checked: true
        }
      };
      component.updateIndependentValuerInvolved(MOCK_EVENT);
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved).toBeTrue();
    });
  });

  describe('update revaluation basis value', () => {
    it('should update the revaluation basis with a new value', () => {
      const builderPatchValuesSpy = spyOn(component.valuationCurrentReportingForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated title value'
      };
      component.updateRevaluationBasis(MOCK_EVENT);
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.revaluationBasis).toBe(
        MOCK_EVENT.detail
      );
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ revaluationBasis: MOCK_EVENT.detail });
    });
  });

  describe('updateDateOfRevaluation ', () => {
    it('should changed the date of revaluation', () => {
      let dateOfRevaluation = {
        detail: '2024-02-12'
      };
      component.updateDateOfRevaluation(dateOfRevaluation);
      expect(component.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation).toEqual(
        dateOfRevaluation.detail
      );
    });
  });

  describe('updateValuationInCurrentReportingPeriod', () => {
    it('should trigger service update with the new values', () => {
      const MOCK_NEW_VALUES = {
        valuationDetails: 'valuationdetails',
        independentValuerInvolved: false,
        revaluationBasis: 'revaluationbasis',
        dateOfRevaluation: '2024-02-12'
      };
      component.updateValuationInCurrentReportingPeriod(MOCK_NEW_VALUES);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_TANGIBLE_FIXED_ASSETS_NOTES,
        MOCK_SECTION_REF,
        false,
        null,
        NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
      );
    });
  });
});
