import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import {
  NotesFormTypeEnum,
  TangibleFixedAssetsNotes,
  ValuationInCurrentReportingPeriod
} from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-valuation-current-reporting-period',
  templateUrl: './valuation-current-reporting-period.component.html',
  styleUrls: ['./valuation-current-reporting-period.component.scss']
})
export class ValuationCurrentReportingPeriod implements OnInit {
  @Input() sectionReference: ReportSection;
  @Input() periodData: AccountProductionPeriod = null;
  valuationCurrentReportingForm: UntypedFormGroup;

  MAX_REVALUATION_LENGTH = 120;
  MAX_TEXTAREA_LENGTH = 10000;

  tangibleFixedAssetsNotes: TangibleFixedAssetsNotes;

  constructor(private _formBuilder: UntypedFormBuilder, private reportNotesService: ReportNotesService) {}

  ngOnInit() {
    this.initData();
    this.buildForm();
  }

  initData() {
    const notesName = NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES;
    this.tangibleFixedAssetsNotes = this.reportNotesService.getReportNotesData(notesName);
    if (!this.tangibleFixedAssetsNotes) {
      this.tangibleFixedAssetsNotes = this.getNewInstance();
    } else if (!this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod) {
      this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod =
        this.getNewValuationCurrentReportingPeriodInstance();
    }
    this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation =
      this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation !== ''
        ? this.getDateOfRevaluation(this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation)
        : this.getDateOfRevaluation(this.periodData.endDate);
  }

  buildForm() {
    this.valuationCurrentReportingForm = this._formBuilder.group({
      valuationDetails: [this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.valuationDetails],
      independentValuerInvolved: [
        this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved
      ],
      revaluationBasis: [this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.revaluationBasis],
      dateOfRevaluation: [this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation]
    });

    this.valuationCurrentReportingForm.valueChanges.subscribe(newValues => {
      this.updateValuationInCurrentReportingPeriod(newValues);
    });
  }

  updateValuationInCurrentReportingPeriod(newValues): void {
    this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod = newValues;

    this.reportNotesService.updateReportNotesDataValues(
      this.tangibleFixedAssetsNotes,
      this.sectionReference,
      false,
      null,
      NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
    );
  }

  updateIndependentValuerInvolved(event) {
    this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved = event.target.checked;
    this.valuationCurrentReportingForm.patchValue({
      independentValuerInvolved:
        this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.independentValuerInvolved
    });
  }

  updatevaluationDetails({ detail }): void {
    this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.valuationDetails = detail;
    this.valuationCurrentReportingForm.patchValue({ valuationDetails: detail });
  }

  updateRevaluationBasis({ detail }): void {
    this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.revaluationBasis = detail;
    this.valuationCurrentReportingForm.patchValue({ revaluationBasis: detail });
  }

  updateDateOfRevaluation({ detail }): void {
    this.tangibleFixedAssetsNotes.valuationInCurrentReportingPeriod.dateOfRevaluation = detail;
    this.valuationCurrentReportingForm.patchValue({ dateOfRevaluation: detail });
  }

  private getNewInstance(): TangibleFixedAssetsNotes {
    const result = {
      valuationInCurrentReportingPeriod: this.getNewValuationCurrentReportingPeriodInstance()
    };
    return result;
  }

  private getNewValuationCurrentReportingPeriodInstance(): ValuationInCurrentReportingPeriod {
    return {
      valuationDetails: '',
      independentValuerInvolved: false,
      revaluationBasis: '',
      dateOfRevaluation: ''
    };
  }

  private getDateOfRevaluation(date: string): string {
    return date.split('T')[0];
  }
}
