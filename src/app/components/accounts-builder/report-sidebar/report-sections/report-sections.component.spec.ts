import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { Subject, of } from 'rxjs';
import { DisplayedFormEnum, NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNavigationService } from 'src/app/services/report-navigation.service';
import { ReportSectionsService } from 'src/app/services/report-sections.service';

import { ReportSectionsComponent } from './report-sections.component';
import { AppSettings } from 'src/app/utils/appSettings';
import { features } from 'src/features';

const MOCK_NAVIGATION_SECTION = {
  parent: null,
  label: 'Sections',
  isMandatory: false,
  formConfigKey: null,
  children: null,
  errorCount: 0,
  warningCount: 0
};

const MOCK_NAVIGATION_CHILDREN = [
  {
    parent: MOCK_NAVIGATION_SECTION,
    label: 'Mock children 1',
    isMandatory: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: MOCK_NAVIGATION_SECTION,
    label: 'Mock children 2',
    isMandatory: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: MOCK_NAVIGATION_SECTION,
    label: 'Mock children 3',
    isMandatory: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: MOCK_NAVIGATION_SECTION,
    label: 'Mock children 4',
    isMandatory: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: MOCK_NAVIGATION_SECTION,
    label: 'Mock children 5',
    isMandatory: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  },
  {
    parent: MOCK_NAVIGATION_SECTION,
    label: 'Mock children 6',
    isMandatory: false,
    formConfigKey: null,
    children: null,
    errorCount: 0,
    warningCount: 0
  }
];
MOCK_NAVIGATION_SECTION.children = MOCK_NAVIGATION_CHILDREN;

describe('ReportSectionsComponent', () => {
  let component: ReportSectionsComponent;
  let fixture: ComponentFixture<ReportSectionsComponent>;
  const reportNavigationServiceMock = jasmine.createSpyObj('ReportNavigationService', [
    'navigateToEditableSection',
    'navigateToNode',
    'navigateToSubsectionEditForm'
  ]);
  const reportSectionsServiceMock = jasmine.createSpyObj('ReportSectionsService', ['updateBookmarkNavigation']);
  reportSectionsServiceMock.bookmarkList = new Subject();
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ReportSectionsComponent],
      providers: [
        {
          provide: ReportNavigationService,
          useValue: reportNavigationServiceMock
        },
        {
          provide: ReportSectionsService,
          useValue: reportSectionsServiceMock
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    reportNavigationServiceMock.currentNavigationNode = new Subject();
    reportNavigationServiceMock.currentNavigationNode.next(MOCK_NAVIGATION_SECTION);
    fixture = TestBed.createComponent(ReportSectionsComponent);
    component = fixture.componentInstance;
    component.sectionsTreeNode = MOCK_NAVIGATION_SECTION;
    component.selectedSection = MOCK_NAVIGATION_CHILDREN[0];
    fixture.detectChanges();
  });

  it('should create', () => {
    const setDisplayedReportSpy = spyOn(component, 'setDisplayedReport');
    expect(component).toBeTruthy();
    expect(component.sectionsTreeNode).toBe(MOCK_NAVIGATION_SECTION);
    expect(setDisplayedReportSpy).not.toHaveBeenCalled();

    reportNavigationServiceMock.currentNavigationNode.next({
      ...MOCK_NAVIGATION_SECTION,
      formConfigKey: 'averageNumberOfEmployees'
    });
    expect(setDisplayedReportSpy).toHaveBeenCalled();
  });

  it('should set isAccountProductionLicenseEnabled to true', () => {
    spyOn(features, 'getState').and.returnValue(of(true));
    component.ngOnInit();
    expect(component.isAccountProductionLicenseEnabled).toBeTrue();
  });

  it('should set isAccountProductionLicenseEnabled to false', () => {
    spyOn(features, 'getState').and.returnValue(of(false));
    component.ngOnInit();
    expect(component.isAccountProductionLicenseEnabled).toBeFalse();
  });

  describe('setSelectedSection', () => {
    it('should emit a navigation bookmark when there is a navigation reference', () => {
      const MOCK_SECTION_REF: ReportSection = {
        label: 'Section ref',
        parent: null,
        isMandatory: true,
        formConfigKey: null,
        children: null,
        errorCount: 0,
        warningCount: 0,
        navigationRef: {
          Text: 'section text',
          PageIndex: 1,
          Indices: '2, 1'
        }
      };

      component.setSelectedSection(MOCK_SECTION_REF);
      expect(component.selectedSection).toBe(MOCK_SECTION_REF);
      expect(reportSectionsServiceMock.updateBookmarkNavigation).toHaveBeenCalledWith(MOCK_SECTION_REF.navigationRef);
    });

    it('should only update the selected section when there is NO navigation reference', () => {
      const MOCK_SECTION_REF: ReportSection = {
        label: 'Section ref',
        parent: null,
        isMandatory: true,
        formConfigKey: null,
        children: null,
        navigationRef: null,
        errorCount: 0,
        warningCount: 0
      };
      component.setSelectedSection(MOCK_SECTION_REF);
      expect(component.selectedSection).toBe(MOCK_SECTION_REF);
    });
  });

  describe('navigateToSection', () => {
    it('emit a section reference for the navigation update', () => {
      component.navigateToSection(MOCK_NAVIGATION_CHILDREN[1]);
      expect(reportNavigationServiceMock.navigateToNode).toHaveBeenCalledWith(MOCK_NAVIGATION_CHILDREN[1]);
    });
  });

  describe('setDisplayedReport', () => {
    it('should set the displayForm to match average employees', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.AVERAGE_EMPLOYEES);
    });

    it('should set the displayForm to match advances credits', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.ADVANCES_CREDITS;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ADVANCES_CREDITS);
    });

    it('should set the displayForm to match exemption financial', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.EXEMPTION_FINANCIAL;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.EXEMPTION_FINANCIAL);
    });

    it('should set the displayForm to match valuation in current reporting period', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.VALUATION_IN_CURRENT_REPORTING_PERIOD;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.VALUATION_CURRENT_PERIOD);
    });

    it('should set the displayForm to match foreign currencies', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.FOREIGN_CURRENCIES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.CHECKBOX_FORM);
    });

    it('should set the displayForm to match presentation currency', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.PRESENTATION_CURRENCY;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.CHECKBOX_FORM);
    });

    it('should set the displayForm to match research and development', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.RESEARCH_AND_DEVELOPMENT;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.CHECKBOX_FORM);
    });

    it('should set the displayForm to match land and building rename', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.LAND_AND_BUILDING_RENAME;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.SECTION_RENAME);
    });

    it('should set the displayForm to match plant and machinery rename', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.PLANT_AND_MACHINERY_RENAME;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.SECTION_RENAME);
    });

    it('should set the displayForm to match loans debts', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.LOANS_DEBTS;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.PARAGRAPH);
    });

    it('should set the displayForm to match paragraph note', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.OFF_BALANCE_SHEETS;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(2);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.ADVANCES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(2);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.CTRL_PARTY_NOTE;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(2);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.GUARANTEES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(2);
    });

    it('should set the displayForm to match titled paragraph note', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.ADDITIONAL_NOTE_1;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(3);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.ADDITIONAL_NOTE_2;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(3);
    });

    it('should set the displayForm to match operating profit loss', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.OPERATING_PROFIT_LOSS;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(7);
    });

    it('should set the displayForm to match goodwill material', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.GOODWILL_MATERIAL;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.GOODWILL_MATERIAL);
    });

    it('should set the displayForm to match historical break down  ', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.HISTORICAL_COST_BREAKDOWN;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.HISTORICAL_BREAKDOWN);
    });

    it('should set the displayForm to match analysis of cost valuation', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.ANALYSIS_OF_COST_VALUATION;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ANALYSIS_OF_COST_VALUATION);
    });

    it('should set the displayForm to match members liability', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.MEMBERS_LIABILITIES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.PARAGRAPH);
    });

    it('should set the displayForm to match members liability modal', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.MEMBERS_LIABILITY_MODAL;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.MEMBERS_LIABILITY_MODAL);
    });

    it('should set the displayForm to match assets basis', () => {
      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.IMPROVEMENTS_PROPERTY;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.PLANT_AND_MACHINERY;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.FIXTURE_AND_FITTINGS;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.MOTOR_VEHICLES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.COMPUTER_EQUIPMENT;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.GOODWILL;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.PATENTS_AND_LICENSES;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.DEVELOPMENT_COSTS;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.COMPUTER_SOFTWARE;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.FREEHOLD_PROPERTY;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.SHORT_LEASEHOLD_PROPERTY;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);

      component.sectionsTreeNode.formConfigKey = NotesFormTypeEnum.LONG_LEASEHOLD_PROPERTY;
      component.setDisplayedReport();
      expect(component.displayedForm).toBe(DisplayedFormEnum.ASSETS_BASIS);
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from the section nodes list', () => {
      const navigationNodeUnsubscribeSpy = spyOn(component.navigationNodeSubscription, 'unsubscribe');
      component.ngOnDestroy();
      expect(navigationNodeUnsubscribeSpy).toHaveBeenCalled();
    });
  });
});
