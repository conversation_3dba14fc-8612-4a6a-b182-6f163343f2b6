import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotesFormTypeEnum, OperatingProfitLossNote } from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { ReportNotesService } from 'src/app/services/report-notes.service';

import { OperatingProfitLossNoteComponent } from './operating-profit-loss-note.component';

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'getReportNotesData',
  'updateReportNotesDataValues'
]);

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.OPERATING_PROFIT_LOSS,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_OPERATING_PROFIT_LOSS_NOTE: OperatingProfitLossNote = {
  isEnabled: true,
  items: [
    {
      index: 1,
      description: 'item 1',
      value: 1
    },
    {
      index: 2,
      description: 'item 2',
      value: 2
    }
  ]
};

describe('OperatingProfitLossNoteComponent', () => {
  let component: OperatingProfitLossNoteComponent;
  let fixture: ComponentFixture<OperatingProfitLossNoteComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OperatingProfitLossNoteComponent, CommaDelimiterPipe],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OperatingProfitLossNoteComponent);
    component = fixture.componentInstance;
    component.sectionReference = MOCK_SECTION_REF;
    reportNotesServiceMock.getReportNotesData.and.returnValue(MOCK_OPERATING_PROFIT_LOSS_NOTE);
    fixture.detectChanges();
    component.ngOnInit();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call the get new instance', () => {
    expect(component.getNewInstance().items.length).toBe(0);
  });

  describe('updateOperatingProfitNoteYesNo', () => {
    it('should update the checkbox value', () => {
      const MOCK_EVENT = {
        target: {
          checked: true
        }
      };
      component.updateOperatingProfitNoteYesNo(MOCK_EVENT);
      expect(component.readOnly).toBeTrue();
    });
  });

  describe('updateAdditionalNote', () => {
    it('should update the new item', () => {
      component.operatingProfitLossNote = MOCK_OPERATING_PROFIT_LOSS_NOTE;
      const event = {
        index: 1,
        description: 'item 1',
        value: '1'
      };
      component.itemFormChanges(event);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_OPERATING_PROFIT_LOSS_NOTE,
        MOCK_SECTION_REF
      );
    });
  });
});
