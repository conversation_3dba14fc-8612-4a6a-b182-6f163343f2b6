import { Component, Input, OnInit } from '@angular/core';
import {
  OperatingProfitLossNote,
  OperatingProfitLossNoteItem
} from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-operating-profit-loss-note',
  templateUrl: './operating-profit-loss-note.component.html',
  styleUrls: ['./operating-profit-loss-note.component.scss']
})
export class OperatingProfitLossNoteComponent implements OnInit {
  @Input() sectionReference: ReportSection;
  OperatingProfitLossNoteItem: OperatingProfitLossNoteItem[] = [];
  operatingProfitLossNote: OperatingProfitLossNote;
  readOnly: boolean = false;
  items: Array<number> = Array.from({ length: 5 }, (_, i) => i + 1);

  constructor(private reportNotesService: ReportNotesService) {}

  ngOnInit(): void {
    this.operatingProfitLossNote =
      this.reportNotesService.getReportNotesData(this.sectionReference.formConfigKey) ?? this.getNewInstance();
    this.readOnly = this.operatingProfitLossNote?.isEnabled;
  }

  getNewInstance() {
    const result: OperatingProfitLossNote = {
      isEnabled: false,
      items: []
    };

    return result;
  }

  getOperatingProfitLossNoteItem(index: number): OperatingProfitLossNoteItem {
    return this.operatingProfitLossNote?.items?.find(c => c.index == index);
  }

  updateOperatingProfitNoteYesNo(event) {
    this.operatingProfitLossNote.isEnabled = event.target.checked;
    this.readOnly = this.operatingProfitLossNote.isEnabled;
    this.reportNotesService.updateReportNotesDataValues(this.operatingProfitLossNote, this.sectionReference);
  }

  itemFormChanges(event) {
    const newOperatingProfitLossNoteItem = {
      description: event.description,
      value: event.value,
      index: event.index
    };

    let operatingProfitLossNoteItem = this.operatingProfitLossNote?.items?.find(
      c => c.index == newOperatingProfitLossNoteItem.index
    );

    if (!operatingProfitLossNoteItem) {
      this.operatingProfitLossNote.items.push(newOperatingProfitLossNoteItem);
    } else {
      operatingProfitLossNoteItem.description = newOperatingProfitLossNoteItem.description;
      operatingProfitLossNoteItem.value = newOperatingProfitLossNoteItem.value;
    }
    this.reportNotesService.updateReportNotesDataValues(this.operatingProfitLossNote, this.sectionReference);
  }
}
