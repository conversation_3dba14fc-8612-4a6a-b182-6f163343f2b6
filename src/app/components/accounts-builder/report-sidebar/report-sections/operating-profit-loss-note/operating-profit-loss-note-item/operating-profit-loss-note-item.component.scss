@import '../../../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

.item-section {
  display: block;
  margin-left: $p-4 + 4;
  margin-bottom: $p-4;
  padding-right: $p-3;
  padding-left: $p-2 + 4;
  border-left: 1px solid $grey-light-6;
  .helper-text {
    font-size: $text-size-base + 2;
    color: $grey-light-6;
    margin: 0;
  }
  .helper-text-warning {
    font-size: $text-size-base + 2;
    margin: 0;
    color: $iris-red;
  }
  .item-text {
    font-weight: bold;
    font-size: $text-size-base + 6;
    color: $iris-grey-dark;
    padding-bottom: $p-1;
  }
  .current-value-input {
    position: relative;
    padding: $p-3 0;
  }
}
