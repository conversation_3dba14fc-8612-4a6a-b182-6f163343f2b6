<div class="item-section">
    <div class="item-text">
        <span>Item {{ index }}</span>
    </div>
    <div>
        <iris-textarea label="Description" [value]="operatingProfitLossNoteItem?.description" [disabled]="readOnly"
            (changed)="updateDescription($event)" [maxlength]="MAX_TEXTAREA_LENGTH" resize="vertical"></iris-textarea>
        <p class="helper-text">{{(MAX_TEXTAREA_LENGTH - operatingProfitLossNoteItem?.description?.length) | commaDelimiter}} characters remaining</p>
    </div>
    <div class="current-value-input">
        <iris-numeric-input label="Current value"
         placeholder-value="0.00"
         [value]="operatingProfitLossNoteItem?.value" 
         [disabled]="readOnly"
         (valueChanged)="updateCurrentValue($event)"
         [minValue]="MIN_VALUE_LENGTH"
         [maxValue]="MAX_VALUE_LENGTH"></iris-numeric-input>
    </div>
</div>