import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, FormControl, UntypedFormGroup } from '@angular/forms';
import { OperatingProfitLossNoteItem } from 'src/app/models/report-sections/report-notes.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-operating-profit-loss-note-item',
  templateUrl: './operating-profit-loss-note-item.component.html',
  styleUrls: ['./operating-profit-loss-note-item.component.scss']
})
export class OperatingProfitLossNoteItemComponent implements OnInit {
  @Input() index: number;
  @Input() readOnly: boolean = false;
  @Input() operatingProfitLossNoteItem: OperatingProfitLossNoteItem = null;

  @Output() onFormChanges: EventEmitter<OperatingProfitLossNoteItem> = new EventEmitter<OperatingProfitLossNoteItem>();

  MAX_TEXTAREA_LENGTH = 120;
  MIN_VALUE_LENGTH = -***********.99;
  MAX_VALUE_LENGTH = ***********.99;

  operatingProfitLossForm: UntypedFormGroup;

  constructor(private _formBuilder: UntypedFormBuilder) {}

  ngOnInit(): void {
    this.operatingProfitLossForm = this._formBuilder.group({
      description: [this.operatingProfitLossNoteItem?.description],
      value: [this.operatingProfitLossNoteItem?.value],
      index: [this.index]
    });

    this.operatingProfitLossForm.valueChanges.subscribe(newValues => {
      this.updateFormValue(newValues);
    });
  }

  updateFormValue(operatingProfitLossNoteItem: OperatingProfitLossNoteItem) {
    this.onFormChanges.emit(operatingProfitLossNoteItem);
  }

  updateCurrentValue({ detail }): void {
    this.operatingProfitLossForm.patchValue({
      value: detail?.length && !isNaN(detail) ? +detail : null
    });
  }

  updateDescription({ detail }): void {
    this.operatingProfitLossForm.patchValue({ description: detail });
  }
}
