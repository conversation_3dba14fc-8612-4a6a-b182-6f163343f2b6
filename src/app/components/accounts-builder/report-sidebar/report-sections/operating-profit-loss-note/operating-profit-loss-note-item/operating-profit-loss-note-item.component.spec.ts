import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { OperatingProfitLossNoteItem } from 'src/app/models/report-sections/report-notes.model';
import { CommaDelimiterPipe } from 'src/app/pipes/comma-delimiter.pipe';
import { OperatingProfitLossNoteItemComponent } from './operating-profit-loss-note-item.component';

describe('OperatingProfitLossNoteItemComponent', () => {
  let component: OperatingProfitLossNoteItemComponent;
  let fixture: ComponentFixture<OperatingProfitLossNoteItemComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OperatingProfitLossNoteItemComponent, CommaDelimiterPipe],
      providers: [UntypedFormBuilder]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OperatingProfitLossNoteItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('updateFormValue', () => {
    it('should update the form value', () => {
      const MOCK_PROFIT_NOTE: OperatingProfitLossNoteItem = {
        description: 'Description',
        value: 1,
        index: 1
      };
      spyOn(component.onFormChanges, 'emit');
      component.updateFormValue(MOCK_PROFIT_NOTE);
      expect(component.onFormChanges.emit).toHaveBeenCalledWith(MOCK_PROFIT_NOTE);
    });
  });

  describe('updateDescriptionValue', () => {
    it('should replace the description with the new value', () => {
      const patchDescriptionSpy = spyOn(component.operatingProfitLossForm, 'patchValue');
      const MOCK_EVENT = {
        detail: 'updated description'
      };
      component.updateDescription(MOCK_EVENT);
      expect(patchDescriptionSpy).toHaveBeenCalledWith({ description: MOCK_EVENT.detail });
    });
  });

  describe('updateValue', () => {
    it('should replace the value with the new value', () => {
      const patchValuesSpy = spyOn(component.operatingProfitLossForm, 'patchValue');
      const value = '123';
      const MOCK_EVENT = {
        detail: +value
      };
      component.updateCurrentValue({
        detail: value
      });
      expect(patchValuesSpy).toHaveBeenCalledWith({ value: MOCK_EVENT.detail });
    });
  });
});
