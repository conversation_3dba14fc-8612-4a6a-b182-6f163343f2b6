<div class="note-checkbox">
    <div class="operating-checkbox">
        <iris-checkbox [checked]="operatingProfitLossNote.isEnabled" (changed)="updateOperatingProfitNoteYesNo($event)"></iris-checkbox>
    </div>
    <div>
        <span class="checkbox-text">Operating Profit Note. Please tick to include note itself. <b>Additional items of
                income or expense of
                exceptional size or instance to be noted below:</b></span>
    </div>
</div>

<elements-accounts-builder-component-v01-pkg-operating-profit-loss-note-item
    *ngFor="let item of items"
    [index]="item"
    [readOnly]="!readOnly"
    [operatingProfitLossNoteItem]="getOperatingProfitLossNoteItem(item)"
    (onFormChanges)="itemFormChanges($event)">
</elements-accounts-builder-component-v01-pkg-operating-profit-loss-note-item>

