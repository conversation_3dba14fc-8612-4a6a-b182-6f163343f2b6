import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { NotesFormTypeEnum } from 'src/app/models/report-sections/report-notes.model';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

import { AverageEmployeesFormComponent } from './average-employees-form.component';

const MOCK_SECTION_REF: ReportSection = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

describe('AverageEmployeesFormComponent', () => {
  let component: AverageEmployeesFormComponent;
  let fixture: ComponentFixture<AverageEmployeesFormComponent>;
  const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
    'updateReportNotesDataValues',
    'getReportNotesData'
  ]);
  reportNotesServiceMock.reportNotesData = {
    averageNumberOfEmployees: {
      currentPeriod: 100,
      previousPeriod: 200
    }
  };

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AverageEmployeesFormComponent],
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        UntypedFormBuilder
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AverageEmployeesFormComponent);
    component = fixture.componentInstance;

    component.sectionReference = MOCK_SECTION_REF;
    reportNotesServiceMock.getReportNotesData
      .withArgs(MOCK_SECTION_REF.formConfigKey)
      .and.returnValue(reportNotesServiceMock.reportNotesData.averageNumberOfEmployees);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize the component', () => {
      component.ngOnInit();
      expect(component.currentYearValue).toBe('100');
      expect(component.previousYearValue).toBe('200');
    });
  });

  describe('updateCurrentYear', () => {
    it('should update the current year value and trigger preview update', () => {
      const builderPatchValuesSpy = spyOn(component.avgNumberOfEmployeesForm, 'patchValue');
      component.updateCurrentYear({ detail: '1234' });
      expect(component.currentYearValue).toEqual('1234');
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ currentYear: '1234' });
    });
  });

  describe('updatePreviousYear', () => {
    it('should update the previous year value and trigger preview update', () => {
      const builderPatchValuesSpy = spyOn(component.avgNumberOfEmployeesForm, 'patchValue');
      component.updatePreviousYear({ detail: '4321' });
      expect(component.previousYearValue).toEqual('4321');
      expect(builderPatchValuesSpy).toHaveBeenCalledWith({ previousYear: '4321' });
    });
  });

  describe('updateAvgNumberOfEmployees', () => {
    it('should trigger service update with the new values', () => {
      const MOCK_NEW_VALUES = {
        currentYear: '30',
        previousYear: '40'
      };
      component.updateAvgNumberOfEmployees(MOCK_NEW_VALUES);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        {
          currentPeriod: 30,
          previousPeriod: 40
        },
        MOCK_SECTION_REF,
        false
      );
    });

    it('should trigger service update with the new values and toggle the error flag', () => {
      const MOCK_NEW_VALUES = {
        currentYear: '',
        previousYear: '40'
      };
      component.currentYearControl.setValue(MOCK_NEW_VALUES.currentYear);
      component.updateAvgNumberOfEmployees(MOCK_NEW_VALUES);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        {
          currentPeriod: null,
          previousPeriod: 40
        },
        MOCK_SECTION_REF,
        true
      );
    });
  });
});
