
<div class="average-employees-container">
    <div class="note-input">
        <iris-numeric-input
            class="average-employees-input"
            [label]="CURRENT_PERIOD_LABEL"
            label-style="normal"
            required="true"
            [colour]="currentYearControl.invalid ? 'error-input' : 'default'"
            [value]="currentYearValue"
            (valueChanged)="updateCurrentYear($event)"
            [minValue]="AVERAGE_EMPLOYEES_MIN_VALUE"
            [maxValue]="AVERAGE_EMPLOYEES_MAX_VALUE"
            [maxDecimals]="AVERAGE_EMPLOYEES_MAX_DECIMALS"
        ></iris-numeric-input>
        <p *ngIf="currentYearControl.invalid" class="helper-text" [ngClass]="{'helper-text--error': currentYearControl.invalid}">
            <iris-icon-only type="alert-circle" size="14" padding="0"></iris-icon-only>
            Please enter the average number of employees
        </p>
    </div>
    <div class="note-input">
        <iris-numeric-input
            [label]="PREVIOUS_PERIOD_LABEL"
            label-style="normal"
            class="average-employees-input"
            required="true"
            [colour]="previousYearControl.invalid ? 'error-input' : 'default'"
            [value]="previousYearValue"
            (valueChanged)="updatePreviousYear($event)"
            [minValue]="AVERAGE_EMPLOYEES_MIN_VALUE"
            [maxValue]="AVERAGE_EMPLOYEES_MAX_VALUE"
            [maxDecimals]="AVERAGE_EMPLOYEES_MAX_DECIMALS"
        ></iris-numeric-input>
        <p *ngIf="previousYearControl.invalid" class="helper-text" [ngClass]="{'helper-text--error': previousYearControl.invalid}">
            <iris-icon-only type="alert-circle" size="14" padding="0"></iris-icon-only>
            Please enter the average number of employees
        </p>
    </div>
</div>