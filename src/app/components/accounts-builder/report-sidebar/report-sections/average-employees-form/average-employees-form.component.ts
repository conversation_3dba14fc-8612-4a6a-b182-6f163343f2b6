import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ReportSection } from 'src/app/models/report-sections/report-sections.model';
import { ReportNotesService } from 'src/app/services/report-notes.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-average-employees-form',
  templateUrl: './average-employees-form.component.html',
  styleUrls: ['./average-employees-form.component.scss']
})
export class AverageEmployeesFormComponent implements OnInit, OnDestroy {
  AVERAGE_EMPLOYEES_MIN_VALUE = 0;
  AVERAGE_EMPLOYEES_MAX_VALUE = 9999999;
  AVERAGE_EMPLOYEES_MAX_DECIMALS = 0;
  CURRENT_PERIOD_LABEL = 'Average number of employees (current period)';
  PREVIOUS_PERIOD_LABEL = 'Average number of employees (previous period)';

  @Input() sectionReference: ReportSection;

  avgNumberOfEmployeesForm: UntypedFormGroup;
  currentYearValue: string = null;
  previousYearValue: string = null;

  averageEmployeesPreview: string = '';

  $subscriptions: Subscription[] = [];

  constructor(private _formBuilder: UntypedFormBuilder, private reportNotesService: ReportNotesService) {}

  ngOnInit() {
    const averageNumberOfEmployees = this.reportNotesService.getReportNotesData(this.sectionReference.formConfigKey);
    this.currentYearValue =
      averageNumberOfEmployees.currentPeriod === null ? null : `${averageNumberOfEmployees.currentPeriod}`;
    this.previousYearValue =
      averageNumberOfEmployees.previousPeriod === null ? null : `${averageNumberOfEmployees.previousPeriod}`;

    this.avgNumberOfEmployeesForm = this._formBuilder.group({
      currentYear: [this.currentYearValue, Validators.required],
      previousYear: [this.previousYearValue, Validators.required]
    });

    this.avgNumberOfEmployeesForm.valueChanges.subscribe(newValues => {
      this.updateAvgNumberOfEmployees(newValues);
    });
  }

  get currentYearControl() {
    return this.avgNumberOfEmployeesForm.get('currentYear');
  }

  get previousYearControl() {
    return this.avgNumberOfEmployeesForm.get('previousYear');
  }

  updateCurrentYear({ detail }): void {
    this.currentYearValue = detail;
    this.avgNumberOfEmployeesForm.patchValue({ currentYear: detail });
  }

  updatePreviousYear({ detail }): void {
    this.previousYearValue = detail;
    this.avgNumberOfEmployeesForm.patchValue({ previousYear: detail });
  }

  updateAvgNumberOfEmployees(newValues): void {
    this.reportNotesService.updateReportNotesDataValues(
      {
        currentPeriod: newValues.currentYear?.length && !isNaN(newValues.currentYear) ? +newValues.currentYear : null,
        previousPeriod:
          newValues.previousYear?.length && !isNaN(newValues.previousYear) ? +newValues.previousYear : null
      },
      this.sectionReference,
      this.avgNumberOfEmployeesForm.invalid
    );
  }

  ngOnDestroy(): void {
    this.$subscriptions.forEach(sub => sub.unsubscribe());
    this.$subscriptions = [];
  }
}
