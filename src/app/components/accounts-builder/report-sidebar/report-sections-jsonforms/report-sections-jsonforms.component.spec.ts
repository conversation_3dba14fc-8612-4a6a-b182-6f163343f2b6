import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, fakeAsync, flush, TestBed, tick } from '@angular/core/testing';
import { BehaviorSubject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { features } from 'src/features';
import { ReportSectionsJsonformsComponent } from './report-sections-jsonforms.component';
import { ReportingStandards } from 'src/app/models/report.model';
import { ReportSectionsService } from 'src/app/services/report-sections.service';
import { Bookmark } from 'src/app/models/bookmark.model';

describe('ReportSectionsJsonformsComponent', () => {
  let component: ReportSectionsJsonformsComponent;
  let fixture: ComponentFixture<ReportSectionsJsonformsComponent>;
  let mockWindow: Window = <any>{ System: { import: () => Promise.resolve({}) } };

  let reportSectionsServiceMock: jasmine.SpyObj<ReportSectionsService>;

  const bookmarkList: Bookmark[] = [
    { Text: 'Section 1', PageIndex: 1, Indices: '0,0,0,0' },
    { Text: 'Section 2', PageIndex: 2, Indices: '0,0,0,0' },
    { Text: 'Section 3', PageIndex: 3, Indices: '0,0,0,0' }
  ];

  beforeEach(() => {
    reportSectionsServiceMock = jasmine.createSpyObj('ReportSectionsService', ['updateBookmarkNavigation']);
    reportSectionsServiceMock.bookmarkList = new BehaviorSubject<Bookmark[]>(bookmarkList);
  });

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ReportSectionsJsonformsComponent],
      imports: [HttpClientTestingModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        {
          provide: ReportSectionsService,
          useValue: reportSectionsServiceMock
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ReportSectionsJsonformsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call setParcelProps when loadParcel() is being called', fakeAsync(() => {
    component.windowAny = mockWindow;
    const setParcelPropsSpy = spyOn(component, 'setParcelProps');
    component.loadParcel();
    tick(500);
    expect(setParcelPropsSpy).toHaveBeenCalled();
    flush();
  }));

  it('should set the parcel properties', () => {
    component.clientUUID = 'clientGuid';
    component.periodUUID = 'periodGuid';
    component.previousPeriodUUID = 'previousPeriodGuid';
    component.reportingStandard = ReportingStandards.FRS102_1A;
    component.setParcelProps();
    expect(component.parcelProps.environment).toEqual(environment);
    expect(component.parcelProps.features).toEqual(features);
    expect(component.parcelProps.clientUUID).toEqual('clientGuid');
    expect(component.parcelProps.periodUUID).toEqual('periodGuid');
    expect(component.parcelProps.previousPeriodUUID).toEqual('previousPeriodGuid');
    expect(component.parcelProps.reportingStandard).toEqual(ReportingStandards.FRS102_1A);
  });

  it('should call setSelectedSection when selectedNode event is triggered', () => {
    const event = new CustomEvent('selectedNode');
    const setSelectedSectionSpy = spyOn(component, 'setSelectedSection');
    window.dispatchEvent(event);
    expect(setSelectedSectionSpy).toHaveBeenCalledWith(event);
  });

  it('should call updateBookmarkNavigation when setSelectedSection is called with a valid section', () => {
    const event = new CustomEvent('custom-event', { detail: { nodeTitle: 'Section 1' } });
    component.setSelectedSection(event);
    expect(reportSectionsServiceMock.updateBookmarkNavigation).toHaveBeenCalledWith(bookmarkList[0]);
  });

  it('should not call updateBookmarkNavigation when setSelectedSection is called with an invalid section', () => {
    const event = new CustomEvent('custom-event', { detail: { nodeTitle: 'Section 10' } });
    component.setSelectedSection(event);
    expect(reportSectionsServiceMock.updateBookmarkNavigation).not.toHaveBeenCalled();
  });

  it('should load the parcel and emit the isApplicationLoaded event', fakeAsync(() => {
    const mockMod = { default: {} };
    component.windowAny = { System: { import: () => Promise.resolve(mockMod) } };
    const setParcelPropsSpy = spyOn(component, 'setParcelProps');
    const emitSpy = spyOn(component.isApplicationLoaded, 'emit');
    component.loadParcel();
    tick();
    expect(setParcelPropsSpy).toHaveBeenCalled();
    expect(component.jsonformsApp).toEqual(mockMod.default);
    expect(emitSpy).toHaveBeenCalledWith(true);
  }));
});
