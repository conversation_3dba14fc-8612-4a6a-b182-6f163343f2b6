import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { features } from 'src/features';
import { mountRootParcel } from 'single-spa';
import { environment } from 'src/environments/environment';
import { ReportingStandards, ReportingStandardsJsonforms } from 'src/app/models/report.model';
import { ReportSectionsService } from 'src/app/services/report-sections.service';
import { Bookmark } from 'src/app/models/bookmark.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-report-sections-jsonforms',
  templateUrl: './report-sections-jsonforms.component.html',
  styleUrls: ['./report-sections-jsonforms.component.scss']
})
export class ReportSectionsJsonformsComponent implements OnInit {
  @Input() clientUUID: string;
  @Input() periodUUID: string;
  @Input() previousPeriodUUID: string;
  @Input() reportingStandard: ReportingStandards;
  @Output() isApplicationLoaded: EventEmitter<boolean> = new EventEmitter<boolean>(false);
  mountRootParcel = mountRootParcel;
  parcelProps;
  jsonformsApp;
  windowAny: any = window;
  reportingStandardTreeviewId;

  constructor(private reportSectionsService: ReportSectionsService) {
    window.addEventListener('selectedNode', event => this.setSelectedSection(event));
  }

  ngOnInit(): void {
    this.loadParcel();
  }
  loadParcel(): void {
    if (this.windowAny.System) {
      this.windowAny.System.import(
        '@iris/elements2-accountsproduction-jsonforms-component-v01-pkg'
      ).then(mod => {
        this.setParcelProps();
        this.jsonformsApp = mod.default;
      });
    }
    this.isApplicationLoaded.emit(true);
  }

  setParcelProps(): void {
    this.reportingStandardTreeviewId = ReportingStandardsJsonforms[this.reportingStandard];
    this.parcelProps = {
      environment: environment,
      features: features,
      reportingStandard: this.reportingStandardTreeviewId,
      clientUUID: this.clientUUID,
      periodUUID: this.periodUUID,
      previousPeriodUUID: this.previousPeriodUUID
    };
  }

  setSelectedSection(event: Event): void {
    const detail = (event as CustomEvent)?.detail;
    let navigationRef: Bookmark;

    this.reportSectionsService.bookmarkList.subscribe(bookmarks => {
      navigationRef = bookmarks.find(bookmark => bookmark.Text === detail?.nodeTitle);
    });

    if (navigationRef) {
      this.reportSectionsService.updateBookmarkNavigation(navigationRef);
    }
  }
}
