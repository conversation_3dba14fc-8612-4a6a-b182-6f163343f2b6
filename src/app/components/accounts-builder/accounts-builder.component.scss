@import '../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

// calculation for accounts-builder-container height
$header-height: 56px;
$breadcrums-height: 56px;
$banner-height: 43px;
$tabs-height: 48px;
$footer-height: 62px;
$heightToBeDeducted: calc(
  #{$header-height} + #{$breadcrums-height} + #{$banner-height} + #{$tabs-height} + #{$footer-height}
);

.accounts-builder-container {
  display: flex;
  height: calc(100vh - #{$heightToBeDeducted});
  elements-accounts-builder-component-v01-pkg-display-report {
    width: 100%;
  }
}

iris-snackbar {
  position: fixed;
  left: calc(50% - 15%);
  top: $p-4;
  width: 30%;
  z-index: 999999;
}
