@import '../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

.display-report-container {
  position: relative;
  overflow: hidden;
  justify-content: center;
  background-color: $grey-light-7;
  display: flex;
  flex-direction: column;
  align-items: center;

  border: 1px solid $grey-light-4;

  line-height: 1.5;
  height: 100%;

  iris-icon,
  iris-loading-spinner {
    pointer-events: none;
    margin-bottom: $p-2;
  }

  &.xbrl-editor-mode {
    flex-direction: row;
  }

  &.default-mode {
    flex-direction: column;
  }
}

.report-section {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
}

.display-report {
  flex: 1;
  height: 100%;
}

iris-snackbar {
  position: fixed;
  left: calc(50% - 15%);
  top: $p-4;
  width: 30%;
  z-index: 999999;
}

button {
  margin-top: $p-2;
  border: none;
  background-color: transparent;
  color: $iris-blue;
  outline: none;
  cursor: pointer;
  padding: 0;
  font-size: $p-2;
  text-decoration: underline;
}

.glantus-report-viewer {
  height: 100%;
  width: 100%;
  z-index: 0;
  position: relative;
}

.display-report-error-container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.display-report {
  z-index: 0;
  position: relative;
}

.xbrl-tag-editor {
  height: 100%;
}

#display-report-inner-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.refresh-report-container {
  max-height: 50px;
  background-color: $yellow-light-1;
  display: flex;
  align-items: center;

  > * {
    display: flex;
    align-items: center;
  }

  * {
    display: flex;
    align-items: center;
  }

  button {
    display: inline;
    vertical-align: baseline;
    margin: 0;
    padding: 0;
    line-height: inherit;
    height: auto;
    transform: none;
    align-self: baseline;
  }

  span,
  button {
    display: inline;
    align-items: baseline;
  }
}
