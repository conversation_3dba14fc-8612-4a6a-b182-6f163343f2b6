<div hideToggle disabled class="issue-details">
  <div class="mat-expansion-header-issue-details issue-details-main-row">
    <div class="issue-details-content-col">
      <div class="issue-detail-header-row">
        <iris-icon-only [type]="isIssueMandatory() ? 'alert-triangle' : 'alert-circle'"
          [ngClass]="{'advisory':!isIssueMandatory(), 'critical': isIssueMandatory()}"
          [size]="20">
        </iris-icon-only>
        <span class="header-desciption" [ngClass]="{'advisory':!isIssueMandatory(), 'critical': isIssueMandatory()}">
          {{issueDetails?.type}} {{issueDetails?.displayName}}
        </span>
      </div>
      <div class="issue-details-description-row">
        <span class="issue-description">
          {{ issueDetails?.description ? (issueDetailsOpenState ? issueDetails.description : (issueDetails.description | slice:0:40) + (issueDetails.description.length > 40 ? '...' : '')) : '' }}
        </span>
        <a class="more-info-link" (click)="toggleIssueDetails()" *ngIf="issueDetails?.description">
          {{ issueDetailsOpenState ? 'Less information' : 'More information' }}
        </a>
      </div>
      <div class="issue-details-breadcrumb-row">
        <div class="breadcrumb">{{issueDetails?.breadcrumb || ''}}</div>
      </div>
    </div>
    
    <div class="hidden">
      <div class="fix-divider"></div>
      <div class="fix-btn-col">
        <button class="fix-btn" (click)="onFixClick($event)">
          <iris-icon-only type="external-link" [size]="16" icon-colour="#1B69B9" background-colour="#fff" style="vertical-align: middle;"></iris-icon-only>
          <span>Fix</span>
        </button>
      </div>
    </div>
  </div>
</div>
