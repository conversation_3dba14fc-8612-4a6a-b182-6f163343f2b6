@import '../../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/variables';

.issue-details {
  background-color: $grey-light-1;
  font-weight: 400;
  padding: $p-1;
  margin: $p-0;
  cursor: pointer;

  .mat-expansion-header-issue-details.issue-details-main-row {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    width: 100%;
  }

  .issue-details-content-col {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
  }

  .issue-detail-header-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: $iris-red;
    gap: 12px;
    .header-desciption {
      font-weight: 600;
      display: flex;
      align-items: center;
      line-height: 1;
    }
    .advisory {
      margin-bottom: -5px;
      color: $orange;
    }
    .critical {
      margin-bottom: -5px;
      color: $iris-red;
    }
  }

  .issue-details-description-row {
    display: flex;
    align-items: flex-start;
    gap: 0;
    padding: 4px 0 0 32px;
    font-size: 14px;
    color: $iris-grey-dark;
    .issue-description {
      flex: 0 1 auto;
      color: $iris-grey-dark;
      font-weight: 400;
      margin-right: 4px;
      white-space: pre-line;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .more-info-link {
      color: $iris-blue;
      font-size: 13px;
      font-weight: 500;
      text-decoration: underline;
      cursor: pointer;
      margin-left: 4px;
      &:hover {
        color: $iris-blue-dark-30;
      }
    }
  }

  .issue-details-breadcrumb-row {
    padding-left: 32px;
    font-size: 12px;
    color: $iris-grey-dark;
    .breadcrumb {
      font-weight: 700;
    }
  }

  .fix-divider {
    width: 1px;
    background: $grey-light-4;
    margin: 0 16px;
    border-radius: 1px;
    align-self: stretch;
    min-height: 48px;
  }

  .fix-btn-col {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 70px;
    padding-left: 0;
    padding-right: 8px;
  }
  .fix-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: $iris-blue;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
    transition: color 0.2s;
    &:hover {
      color: $iris-blue-dark-30;
    }
    iris-icon-only {
      margin-right: 4px;
    }
  }

  .hidden {
    display: none;
  }
}
