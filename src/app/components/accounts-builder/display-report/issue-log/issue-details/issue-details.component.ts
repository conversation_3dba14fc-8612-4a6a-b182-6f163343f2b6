import { Component, Input } from '@angular/core';
import { ValidationIssue } from 'src/app/models/report.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-issue-details',
  templateUrl: './issue-details.component.html',
  styleUrls: ['./issue-details.component.scss']
})
export class IssueDetailsComponent {
  @Input() issueDetails: ValidationIssue = null;
  issueDetailsOpenState = false;

  constructor() {}
  ngOnInit(): void {
    this.issueDetailsOpenState = false;
  }

  toggleIssueDetails(): void {
    this.issueDetailsOpenState = !this.issueDetailsOpenState;
  }

  isIssueMandatory(): boolean {
    return this.issueDetails?.errorCategory === 'Mandatory';
  }

  onFixClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
  }
}
