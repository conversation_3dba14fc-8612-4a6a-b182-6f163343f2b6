import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import {
  ValidationIssue,
  ValidationIssueCategory,
  ValidationIssueType,
  ValidationIssueTarget
} from 'src/app/models/report.model';
import { By } from '@angular/platform-browser';

import { IssueDetailsComponent } from './issue-details.component';

describe('IssueDetailsComponent', () => {
  let component: IssueDetailsComponent;
  let fixture: ComponentFixture<IssueDetailsComponent>;

  const mockValidationIssue: ValidationIssue = {
    errorCategory: ValidationIssueCategory.Mandatory,
    type: ValidationIssueType.Missing,
    displayName: 'Test Issue',
    description: 'This is a test issue description that is longer than 40 characters',
    breadcrumb: 'Test > Path > Issue',
    name: 'test-issue',
    errorCode: 'TEST001',
    target: ValidationIssueTarget.IssueLog
  };

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [IssueDetailsComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IssueDetailsComponent);
    component = fixture.componentInstance;
    component.issueDetails = mockValidationIssue;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle issueDetailsOpenState', () => {
    expect(component.issueDetailsOpenState).toBeFalse();
    component.toggleIssueDetails();
    expect(component.issueDetailsOpenState).toBeTrue();
    component.toggleIssueDetails();
    expect(component.issueDetailsOpenState).toBeFalse();
  });

  it('should handle isIssueMandatory correctly', () => {
    // Test Mandatory case
    component.issueDetails = { ...mockValidationIssue, errorCategory: ValidationIssueCategory.Mandatory };
    expect(component.isIssueMandatory()).toBeTrue();

    // Test Advisory case
    component.issueDetails = { ...mockValidationIssue, errorCategory: ValidationIssueCategory.Advisory };
    expect(component.isIssueMandatory()).toBeFalse();

    // Test null case
    component.issueDetails = null;
    expect(component.isIssueMandatory()).toBeFalse();
  });

  it('should handle onFixClick correctly', () => {
    const mockEvent = {
      preventDefault: jasmine.createSpy('preventDefault'),
      stopPropagation: jasmine.createSpy('stopPropagation')
    } as unknown as Event;

    component.onFixClick(mockEvent);

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
  });

  it('should initialize with correct state', () => {
    expect(component.issueDetailsOpenState).toBeFalse();
    expect(component.issueDetails).toEqual(mockValidationIssue);
  });

  it('should handle description display correctly', () => {
    // Test truncated description
    component.issueDetailsOpenState = false;
    fixture.detectChanges();
    const descriptionElement = fixture.debugElement.query(By.css('.issue-description'));
    expect(descriptionElement.nativeElement.textContent.trim()).toContain('...');

    // Test full description
    component.issueDetailsOpenState = true;
    fixture.detectChanges();
    expect(descriptionElement.nativeElement.textContent.trim()).toBe(mockValidationIssue.description);

    // Test null description
    component.issueDetails = { ...mockValidationIssue, description: null };
    fixture.detectChanges();
    expect(descriptionElement.nativeElement.textContent.trim()).toBe('');
  });

  it('should display correct header information', () => {
    const headerElement = fixture.debugElement.query(By.css('.header-desciption'));
    expect(headerElement.nativeElement.textContent.trim()).toBe(
      `${mockValidationIssue.type} ${mockValidationIssue.displayName}`
    );

    // Test with null values
    component.issueDetails = { ...mockValidationIssue, type: null, displayName: null };
    fixture.detectChanges();
    expect(headerElement.nativeElement.textContent.trim()).toBe('');
  });

  it('should handle breadcrumb display', () => {
    const breadcrumbElement = fixture.debugElement.query(By.css('.breadcrumb'));
    expect(breadcrumbElement.nativeElement.textContent.trim()).toBe(mockValidationIssue.breadcrumb);

    // Test with null breadcrumb
    component.issueDetails = { ...mockValidationIssue, breadcrumb: null };
    fixture.detectChanges();
    expect(breadcrumbElement.nativeElement.textContent.trim()).toBe('');
  });
});
