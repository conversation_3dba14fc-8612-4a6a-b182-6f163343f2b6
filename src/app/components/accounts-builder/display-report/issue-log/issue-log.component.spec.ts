import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  ReportData,
  ReportStatus,
  ValidationData,
  ValidationIssue,
  ValidationIssueTarget,
  ValidationIssueCategory,
  ValidationIssueType
} from 'src/app/models/report.model';

import { IssueLogComponent } from './issue-log.component';
import { AccountsBuilderService } from '../../../../services/accounts-builder.service';
import { Subject } from 'rxjs';

describe('IssueLogComponent', () => {
  let component: IssueLogComponent;
  let fixture: ComponentFixture<IssueLogComponent>;
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
    'triggerGenerateReport',
    'getReportStatus',
    'getReportData',
    'reportStatusSubject'
  ]);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [IssueLogComponent],
      providers: [
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    accountsBuilderServiceMock.reportStatusSubject = new Subject<string>();
    fixture = TestBed.createComponent(IssueLogComponent);
    component = fixture.componentInstance;
    component.reportData = {
      validationData: {
        validationIssues: [{} as ValidationIssue],
        advisoryValidationIssuesCount: 0,
        mandatoryValidationIssuesLogCount: 0
      } as ValidationData
    } as ReportData;
    fixture.detectChanges();
  });

  describe('shouldInit', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should populate displayable items', () => {
      component.reportData = {
        validationData: {
          validationIssues: [
            { name: '', breadcrumb: '', displayName: '', description: '', target: ValidationIssueTarget.IssueLog },
            { name: '', breadcrumb: '', displayName: '', description: '', target: ValidationIssueTarget.SectionValidation
            }
          ]
        }
      } as ReportData;

      component.ngOnInit();
      expect(component.displayableValidationIssues.length).toBe(2);
    });
  });

  describe('showIssueLog', () => {
    it('should update issue log', () => {
      component.onClick();

      expect(component.showIssueLog).toBeTrue();
    });
  });

  describe('validation summary data', () => {
    it('should show chevron if report is successfully generated and has issuelog validation issues', () => {
      component.reportData = {
        validationData: {
          validationIssues: [
            { name: '', breadcrumb: '', displayName: '', description: '', target: ValidationIssueTarget.IssueLog }
          ]
        }
      } as ReportData;
      component.reportStatus = ReportStatus.SUCCESS;
      component.ngOnInit();

      expect(component.shouldShowChevron()).toBeTrue();
    });

    it('should not show chevron if report is not successfully', () => {
      component.reportData = {
        validationData: {
          validationIssues: [
            { name: '', breadcrumb: '', displayName: '', description: '', target: ValidationIssueTarget.IssueLog }
          ]
        }
      } as ReportData;
      component.reportStatus = ReportStatus.FAILED;
      component.ngOnInit();

      expect(component.shouldShowChevron()).toBeFalse();
    });

    it('should not show chevron if report does not have issuelog validation issues', () => {
      component.reportData = {
        validationData: {
          validationIssues: []
        }
      } as ReportData;
      component.reportStatus = ReportStatus.SUCCESS;
      component.ngOnInit();

      expect(component.shouldShowChevron()).toBeFalse();
    });
  });

  describe('getValidationIssues', () => {
    it('should return validation issues with matching issue type', () => {
      const issueType = 'accounts builder';
      const validationIssues: ValidationIssue[] = [
        { 
          name: '', 
          breadcrumb: 'Accounts Builder > Sections FRS102 - Section 1A > Members transactions', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Advisory, 
          type:ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        },
        { 
          name: '', 
          breadcrumb: 'Other Issue', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Mandatory, 
          type:ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        }
      ];

      component.displayableValidationIssues = validationIssues;
      const result = component.getValidationIssues(issueType);
      expect(result).toEqual([validationIssues[0]]);
    });

    it('should return validation issues with matching issue type after replacing "accounts production" with "profit share tab"', () => {
      const issueType = 'accounts production';
      const validationIssues: ValidationIssue[] = [
        { 
          name: '', 
          breadcrumb: 'Profit Share Tab', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Advisory, 
          type:ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        },
        { 
          name: '', 
          breadcrumb: 'Other Issue', 
          displayName: '', description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Mandatory, 
          type:ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        }
      ];

      component.displayableValidationIssues = validationIssues;
      const result = component.getValidationIssues(issueType);
      expect(result).toEqual([validationIssues[0]]);
    });

    it('should return validation issues with matching issue type after replacing "accounts production" with "source data tab"', () => {
      const issueType = 'accounts production';
      const validationIssues: ValidationIssue[] = [
        { 
          name: '', 
          breadcrumb: 'Source data Tab', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Advisory, 
          type:ValidationIssueType.Invalid, 
          target: ValidationIssueTarget.IssueLog 
        },
        { 
          name: '', 
          breadcrumb: 'Other Issue', 
          displayName: '', description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Mandatory, 
          type:ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        }
      ];

      component.displayableValidationIssues = validationIssues;
      const result = component.getValidationIssues(issueType);
      expect(result).toEqual([validationIssues[0]]);
    });

    it('should return an empty array if no validation issues match the issue type', () => {
      const issueType = 'accounts production';
      const validationIssues: ValidationIssue[] = [
        { 
          name: '', 
          breadcrumb: 'Other Issue', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Mandatory, 
          type:ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        }
      ];

      component.displayableValidationIssues = validationIssues;
      const result = component.getValidationIssues(issueType);
      expect(result).toEqual([]);
    });
  });

  describe('getTabTitleWithIssueCount', () => {
    it('should return the tab title with the correct issue count', () => {
      const title = 'Tab Title';
      const issueType = 'accounts builder';
      const validationIssues: ValidationIssue[] = [
        { 
          name: '', 
          breadcrumb: 'Accounts Builder > Sections FRS102 - Section 1A > Members transactions', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Advisory, 
          type: ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        },
        { 
          name: '', 
          breadcrumb: 'Other Issue', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Mandatory, 
          type: ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        }
      ];

      component.displayableValidationIssues = validationIssues;
      const result = component.getTabTitleWithIssueCount(title, issueType);
      expect(result).toEqual('Tab Title (1)');
    });

    it('should return the tab title with zero issue count if no validation issues match the issue type', () => {
      const title = 'Tab Title';
      const issueType = 'accounts production';
      const validationIssues: ValidationIssue[] = [
        { 
          name: '', 
          breadcrumb: 'Other Issue', 
          displayName: '', 
          description: '', 
          errorCode: '', 
          errorCategory: ValidationIssueCategory.Mandatory, 
          type: ValidationIssueType.Missing, 
          target: ValidationIssueTarget.IssueLog 
        }
      ];

      component.displayableValidationIssues = validationIssues;
      const result = component.getTabTitleWithIssueCount(title, issueType);
      expect(result).toEqual('Tab Title (0)');
    });
  });
});
