import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ReportData, ReportStatus, ValidationIssue, ValidationIssueTarget } from 'src/app/models/report.model';
import { IssueLogTab, ISSUELOG_TABS } from 'src/app/models/issue-log-header-details.model';
import { AccountsBuilderService } from '../../../../services/accounts-builder.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-issue-log',
  templateUrl: './issue-log.component.html',
  styleUrls: ['./issue-log.component.scss']
})
export class IssueLogComponent implements OnInit, OnDestroy {
  showIssueLog: boolean;
  displayableValidationIssues: ValidationIssue[];
  statuses = ReportStatus;
  @Input() reportData: ReportData;
  @Input() reportStatus: ReportStatus;
  @Input() hasBanner: boolean = false;
  @Output() showIssuesEvent: EventEmitter<boolean> = new EventEmitter<boolean>();
  private reportProcessIdSubject: Subscription;
  tabs: IssueLogTab[] = ISSUELOG_TABS;
  activeTab: string = 'Data screens';

  constructor(private readonly accountsBuilderService: AccountsBuilderService) {}
  ngOnInit(): void {
    this.showIssueLog = false;
    this.displayableValidationIssues =
      this.reportData?.validationData?.validationIssues?.filter(
        v => v.target == ValidationIssueTarget.IssueLog || v.target == ValidationIssueTarget.SectionValidation
      ) ?? [];
    this.reportProcessIdSubject = this.accountsBuilderService.reportStatusSubject.subscribe(status => {
      if (status === ReportStatus.IN_PROGRESS) {
        this.showIssueLog = false;
      }
    });
  }

  onClick(): void {
    this.showIssueLog = !this.showIssueLog;
    this.showIssuesEvent.emit(this.showIssueLog);
  }

  shouldShowChevron() {
    return this.displayableValidationIssues.length > 0 && this.reportStatus === ReportStatus.SUCCESS;
  }

  shouldShowExpandedIssueLog() {
    return this.showIssueLog && this.reportStatus !== ReportStatus.IN_PROGRESS;
  }

  ngOnDestroy(): void {
    this.reportProcessIdSubject.unsubscribe();
  }

  getValidationIssues(issueType: string): ValidationIssue[] {
    const issueTypeTerms = issueType == 'accounts production' ? ['source data tab', 'profit share tab'] : [issueType];
    return this.displayableValidationIssues.filter(v =>
      issueTypeTerms.some(term => v.breadcrumb.toLocaleLowerCase().includes(term))
    );
  }

  getTabTitleWithIssueCount(title: string, issueType: string): string {
    const issueCount = this.getValidationIssues(issueType).length;
    return `${title} (${issueCount})`;
  }
}
