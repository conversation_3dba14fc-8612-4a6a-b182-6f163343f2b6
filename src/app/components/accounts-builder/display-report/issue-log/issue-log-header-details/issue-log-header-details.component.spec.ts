import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import {
  ReportStatus,
  ValidationData,
  ValidationIssue,
  ValidationIssueTarget,
  ValidationIssueCategory,
  ValidationIssueType
} from 'src/app/models/report.model';

import { IssueLogHeaderDetailsComponent } from './issue-log-header-details.component';

describe('IssueDetailsComponent', () => {
  let component: IssueLogHeaderDetailsComponent;
  let fixture: ComponentFixture<IssueLogHeaderDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [IssueLogHeaderDetailsComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IssueLogHeaderDetailsComponent);
    component = fixture.componentInstance;
    component.reportStatus = ReportStatus.SUCCESS;
    component.validationData = {
      validationIssues: [],
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 0
    } as any as ValidationData;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return no issues message', () => {
    component.ngOnInit();

    expect(component.messageDetails.type).toEqual('check');
    expect(component.messageDetails.message).toEqual('No issues detected');
    expect(component.messageDetails.iconColour).toEqual('#0c6a00');
    expect(component.messageDetails.backgroundColor).toEqual('#ffffff');
  });

  it('should return no data available', () => {
    component.validationData = undefined;
    component.ngOnInit();

    expect(component.messageDetails.type).toEqual('alert-triangle');
    expect(component.messageDetails.message).toEqual('No data available, please generate a Report');
    expect(component.messageDetails.iconColour).toEqual('#ff9504');
    expect(component.messageDetails.backgroundColor).toEqual('#ffffff');
  });

  it('should return no data available', () => {
    component.reportStatus = ReportStatus.FAILED;
    component.ngOnInit();

    expect(component.messageDetails.type).toEqual('alert-triangle');
    expect(component.messageDetails.message).toEqual('No data available, please generate a Report');
    expect(component.messageDetails.iconColour).toEqual('#ff9504');
    expect(component.messageDetails.backgroundColor).toEqual('#ffffff');
  });

  it('should return issues summary', () => {
    component.validationData = {
      validationIssues: [
        {
          breadcrumb: 'test',
          description: 'test',
          displayName: 'test',
          name: 'test',
          type: ValidationIssueType.Invalid,
          errorCategory: ValidationIssueCategory.Mandatory,
          target: ValidationIssueTarget.IssueLog,
          errorCode: 'test'
        } as ValidationIssue
      ],
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 1
    } as ValidationData;
    component.ngOnInit();

    expect(component.messageDetails.type).toEqual('alert-triangle');
    expect(component.messageDetails.message).toEqual('(1)');
    expect(component.messageDetails.iconColour).toEqual('#c8102e');
    expect(component.messageDetails.backgroundColor).toEqual('#f7f8fA');
  });

  it('should return mandatory and advisory issues', () => {
    component.validationData = {
      advisoryValidationIssuesLogCount: 1,
      mandatoryValidationIssuesLogCount: 1
    } as ValidationData;
    const result = component.hasMandatoryAndAdvisoryIssues();

    expect(result).toBeTrue();
  });

  it('should return mandatory false when no validation data', () => {
    component.validationData = null;
    expect(component.hasMandatoryAndAdvisoryIssues()).toBeFalse();
  });

  it('should display no issue when no validation data', () => {
    component.validationData = null;
    expect(component.shouldDisplayNoIssueMessage()).toBeTrue();
  });

  it('should display no issue when only non issue log validation issues', () => {
    component.validationData = {
      validationIssues: [
        {
          breadcrumb: 'test',
          description: 'test',
          displayName: 'test',
          name: 'test',
          errorCategory: ValidationIssueCategory.Advisory,
          type: ValidationIssueType.Invalid,
          errorCode: 'test',
          target: ValidationIssueTarget.SectionValidation
        } as ValidationIssue
      ],
      advisoryValidationIssuesLogCount: 1
    } as ValidationData;
    expect(component.shouldDisplayNoIssueMessage()).toBeTrue();
  });

  it('should not display no issue when issue log validation issues exists', () => {
    component.validationData = {
      validationIssues: [
        {
          breadcrumb: 'test',
          description: 'test',
          displayName: 'test',
          name: 'test',
          errorCategory: ValidationIssueCategory.Mandatory,
          type: ValidationIssueType.Invalid,
          errorCode: 'test',
          target: ValidationIssueTarget.IssueLog
        } as ValidationIssue
      ],
      mandatoryValidationIssuesLogCount: 1
    } as ValidationData;
    expect(component.shouldDisplayNoIssueMessage()).toBeFalse();
  });

  it('should not display validation issue when no validation data', () => {
    component.validationData = null;
    expect(component.shouldDisplayValidationIssueSummary()).toBeFalse();
  });
});
