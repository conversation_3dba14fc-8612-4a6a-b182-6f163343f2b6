<div class="issue-log-header-details">
  <ng-container>
    <div class="issue-collapsed-info">
      <iris-icon  [type]="messageDetails.type"
          shape="circle"
          [iconColour]="messageDetails.iconColour"
          [backgroundColour]="messageDetails.backgroundColor"
          size="15"
         ></iris-icon>
      <p [class]="messageDetails.styleClass">{{messageDetails.message}}</p>
    </div>
  </ng-container>
  <ng-container *ngIf="hasMandatoryAndAdvisoryIssues()">
    <div class="issue-collapsed-info">
      <iris-icon  
          type="alert-circle"
          shape="circle"
          [iconColour]="'#ff9800'"
          [backgroundColour]="messageDetails.backgroundColor"
          size="15"
         ></iris-icon>
      <p [class]="messageDetails.styleClass">({{validationData.advisoryValidationIssuesLogCount}})</p>
    </div>
  </ng-container>
</div>
