import { Component, Input, OnInit } from '@angular/core';
import { IssueLogHeaderDetailsModel } from 'src/app/models/issue-log-header-details.model';
import { ReportStatus, ValidationData } from 'src/app/models/report.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-issue-log-header-details',
  templateUrl: './issue-log-header-details.component.html',
  styleUrls: ['./issue-log-header-details.component.scss']
})
export class IssueLogHeaderDetailsComponent implements OnInit {
  statuses = ReportStatus;
  messageDetails: IssueLogHeaderDetailsModel;
  @Input() validationData: ValidationData;
  @Input() reportStatus: ReportStatus;

  ngOnInit(): void {
    if (this.shouldDisplayNoDataAvailableMessage()) {
      this.messageDetails = {
        type: 'alert-triangle',
        iconColour: '#ff9504',
        backgroundColor: '#ffffff',
        message: 'No data available, please generate a Report',
        styleClass: 'no-validation-data'
      };
      return;
    }
    if (this.shouldDisplayNoIssueMessage()) {
      this.messageDetails = {
        type: 'check',
        iconColour: '#0c6a00',
        backgroundColor: '#ffffff',
        message: 'No issues detected',
        styleClass: 'empty-validation-list'
      };
      return;
    }
    this.messageDetails = {
      type: 'alert-triangle',
      iconColour: '#c8102e',
      backgroundColor: '#f7f8fA',
      message: `(${this.validationData?.mandatoryValidationIssuesLogCount})`,
      styleClass: ''
    };
  }

  shouldDisplayValidationIssueSummary() {
    return (
      this.reportStatus === this.statuses.SUCCESS &&
      (this.validationData?.mandatoryValidationIssuesLogCount > 0 ||
        this.validationData?.advisoryValidationIssuesLogCount > 0)
    );
  }

  hasMandatoryAndAdvisoryIssues() {
    return (
      this.validationData?.mandatoryValidationIssuesLogCount > 0 && this.validationData?.advisoryValidationIssuesLogCount > 0
    );
  }

  shouldDisplayNoIssueMessage() {
    return (this.validationData?.validationIssues?.filter(v => v.target == 'IssueLog') ?? []).length === 0;
  }

  shouldDisplayNoDataAvailableMessage() {
    return !this.validationData || this.reportStatus !== this.statuses.SUCCESS;
  }
}
