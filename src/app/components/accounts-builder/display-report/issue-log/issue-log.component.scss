@import '../../../../../../node_modules/@iris/platform-ui-kit/dist/collection/styles/variables';
@import '../../../../../assets/mixins';

$header-height: 56px;
$breadcrums-height: 56px;
$banner-height: 43px;
$tabs-height: 48px;
$footer-height: 62px;
$refresh-banner-height: 50px;
$issue-log-header-height: 40px;
$heightToBeDeducted: calc(
  #{$header-height} + #{$breadcrums-height} + #{$banner-height} + #{$tabs-height} + #{$footer-height}
);
$heightToBeDeductedWithRefreshBanner: calc(
  #{$header-height} + #{$breadcrums-height} + #{$banner-height} + #{$tabs-height} + #{$footer-height} + #{$refresh-banner-height}
);

.issue-log-container {
  width: 100%;
  background-color: $white;
  z-index: 0;
}
.issue-log-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  padding-right: 50px;
  gap: 10px;
  background-color: $white;
  height: 40px;
  border-top: 1px solid $grey-light-4;
  box-shadow: 0px 1px 0px #e9eaeb;
  .issue-log-title {
    font-family: $font;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    display: flex;
    align-items: right;
    text-align: right;
    user-select: none;
    color: $grey-light-4;
    p {
      margin: 0px;
    }
  }
  .issue-log-title--with-issues {
    color: $black;
  }

  .issue-log-show-chevron {
    margin-left: 15px;
    margin-top: 8px;
  }

  .issue-log-hide-chevron {
    margin-left: 35px;
  }

  elements-accounts-builder-component-v01-pkg-issue-log-header-details {
    margin-left: auto;
  }
}

.issue-log-expanded-info {
  z-index: 200;
  position: relative;
  height: calc(100vh - #{$heightToBeDeductedWithRefreshBanner} - #{$issue-log-header-height});
  width: 100%;
  background-color: $white;
  overflow: hidden;
  overflow-y: auto;

  @include custom-scrollbar;
}

h1 {
  margin: 0px;
}

.issue-log-container--expanded {
  position: absolute;
  top: 75px;
  z-index: 1;
}

.issue-log-container--expanded-with-banner {
  position: absolute;
  top: 50px;
  z-index: 1;
}

.issue-log-container--expanded-no-banner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  .issue-log-expanded-info {
    height: calc(100vh - $heightToBeDeducted - #{$issue-log-header-height});
  }
}

:host {
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 40px;
}

.issue-log-count {
  padding-left: 5px;
}
