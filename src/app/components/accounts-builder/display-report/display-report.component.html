<div 
  class="display-report-container"
  [ngClass]="{'xbrl-editor-mode': isXbrlEditorModeOn, 'default-mode': !isXbrlEditorModeOn }"
  >
  <iris-snackbar [type]="issueLogAlert.type" *ngIf="issueLogAlert" (close)="issueLogAlert = null">
    {{ issueLogAlert.message }}
  </iris-snackbar>

  <iris-loading-spinner *ngIf="isLoading" size="80"></iris-loading-spinner>

  <ng-container *ngIf="!isLoading">
    <div id="display-report-inner-wrapper">
      <ng-container *ngIf="isXbrlEditorModeOn; else reportViewer">
        <div class="display-report" id="display-report-core">
          <elements-accounts-builder-component-v01-pkg-xbrl-section-html-report-viewer
            [reportId]="reportParams.reportId"
            [clientUUID]="reportParams.clientId"
            [periodUUID]="reportParams.accountPeriodId"
            [previousPeriodUUID]="reportParams.previousAccountPeriodId"
          ></elements-accounts-builder-component-v01-pkg-xbrl-section-html-report-viewer>
        </div>
      </ng-container>
      <ng-template #reportViewer>
        <ng-container *ngIf="status !== statuses.SUCCESS; else glantusReportViewer">
          <div class="display-report-error-container">
            <iris-loading-spinner *ngIf="status === statuses.IN_PROGRESS" size="80"></iris-loading-spinner>
            <iris-icon *ngIf="status !== statuses.IN_PROGRESS" type="alert-triangle" shape="circle"
              [backgroundColour]="status === statuses.FAILED ? '#FAE7EA' : '#FFF4E6'"
              [iconColour]="status === statuses.FAILED ? '#C8102E' : '#FF9504'" size="36"></iris-icon>
            <span *ngIf="status === statuses.NO_DATA">
              <strong> {{ getNoTrialBalanceDataMessage() }} </strong>
            </span>
            <span *ngFor="let message of messageMapping[status]">
              {{ message }}<br />
            </span>
            <span *ngIf="status === statuses.FAILED">
              Click “<button (click)="triggerGenerateReport()">Generate report</button>” to try again.
            </span>
          </div>
        </ng-container>
        <ng-template #glantusReportViewer>
          <div class="refresh-report-container">
            <iris-alert *ngIf="displayFailedBanner || displayWarningBanner"
              [type]="displayFailedBanner ? 'error' : 'warning'">
              <span *ngIf="displayFailedBanner; else warningBanner">
                {{ bannerErrorMessage }} Please
                <button (click)="triggerGenerateReport()">try again</button>.
              </span>
              <ng-template #warningBanner>
                <span *ngIf="hasSourceData; else noTrialBalanceData">
                  Changes have been made. Press
                  <button (click)="triggerGenerateReport()">Refresh report</button> to
                  see the updates.
                </span>
                <ng-template #noTrialBalanceData>
                  <span> {{ getNoTrialBalanceDataMessage(' a new ') }} </span>
                </ng-template>
              </ng-template>
            </iris-alert>
          </div>
          <accountsproduction-builder-report-viewer #displayReport class="glantus-report-viewer"
            [queryParams]="reportParams" (navigationReady)="onNavigationReady($event)">
          </accountsproduction-builder-report-viewer>
        </ng-template>
      </ng-template>
      <elements-accounts-builder-component-v01-pkg-dynamic-issuelog
        *ngIf="shouldDisplayIssueLogComponent() && isAccountProductionDynamicIssueLogEnabled; else currentIssueLog"
        [periodId]="periodId"
        [reportData]="reportData"
        [reportStatus]="status"
        [hasBanner]="displayFailedBanner || displayWarningBanner"
        (showIssuesEvent)="updateShowIssuesLog($event)">
      </elements-accounts-builder-component-v01-pkg-dynamic-issuelog>
      <ng-template #currentIssueLog>
        <elements-accounts-builder-component-v01-pkg-issue-log *ngIf="shouldDisplayIssueLogComponent()"
          [reportData]="reportData"
          [reportStatus]="status"
          [hasBanner]="displayFailedBanner || displayWarningBanner"
          (showIssuesEvent)="updateShowIssuesLog($event)">
        </elements-accounts-builder-component-v01-pkg-issue-log>
      </ng-template>
    </div>
    <elements-accounts-builder-component-v01-pkg-xbrl-section-tag-editor *ngIf="isXbrlEditorModeOn" class="xbrl-tag-editor"
      [reportId]="reportParams.reportId"
      [clientUUID]="reportParams.clientId"
      [periodUUID]="reportParams.accountPeriodId"
      [previousPeriodUUID]="reportParams.accountPeriodId"
      [taxonomyId]="taxonomyId"
    ></elements-accounts-builder-component-v01-pkg-xbrl-section-tag-editor>
  </ng-container>
</div>
