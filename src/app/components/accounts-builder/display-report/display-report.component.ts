import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { ReportViewer2Component } from '@iris/accountsproduction-builder-report-viewer';
import { EMPTY, Subject, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Bookmark } from 'src/app/models/bookmark.model';
import { FailedReportErrorCodes, FailedReportErrorMessages, ReportData, ReportStatus } from 'src/app/models/report.model';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { AlertService } from 'src/app/services/alert.service';
import { ReportSectionsService } from 'src/app/services/report-sections.service';
import { Alert } from '../../../models/alert.model';
import { ReportNotesService } from '../../../services/report-notes.service';
import { XbrlToggleService } from 'src/app/services/xbrl-toggle.service';
import * as moment from 'moment';
import { AppSettings } from 'src/app/utils/appSettings';
import { features } from 'src/features';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-display-report',
  templateUrl: './display-report.component.html',
  styleUrls: ['./display-report.component.scss']
})
export class DisplayReportComponent implements OnInit, OnDestroy {
  @ViewChild('displayReport') public displayReport: ReportViewer2Component;
  @ViewChild('reportIframe') iframeRef: ElementRef;

  @Input() hasSourceData: boolean = false;
  @Input() isLoading: boolean = true;
  @Input() clientUUID: string = null;
  @Input() periodUUID: string = null;
  @Input() periodId: string = null;
  @Input() navigateToBookmark: Subject<Bookmark>;
  @Output() bookmarkListChange: EventEmitter<Bookmark[]> = new EventEmitter<Bookmark[]>();
  @Output() isLoadingUpdate: EventEmitter<boolean> = new EventEmitter<boolean>();

  POLLING_INTERVAL: number = 3000;
  displayFailedBanner: boolean = false;
  displayWarningBanner: boolean = false;
  hasLatestSuccessfulReport: boolean = false;
  useLastSuccessfulPreviousId: boolean = false;
  bannerErrorMessage: FailedReportErrorMessages = FailedReportErrorMessages.BANNER_TECHNICAL;
  reportData: ReportData;
  statuses = ReportStatus;
  status: string = ReportStatus.NO_DATA;
  showIssuesLog: boolean = false;
  $subscriptions: Subscription[] = [];
  issueLogAlert: Alert = null;
  isXbrlEditorModeOn: boolean = false;
  isXbrlButtonEnabled: boolean = false;
  taxonomyId: number = null;
  isAccountProductionDynamicIssueLogEnabled: boolean = false;

  reportParams = {
    reportId: '',
    clientId: '********-0000-0000-0001-************',
    accountPeriodId: '********-0000-0000-0000-************',
    previousAccountPeriodId: '********-0000-0000-0000-************'
  };

  messageMapping = {
    [ReportStatus.NO_DATA]: ['Please add source data and complete any trial balance', 'adjustments to proceed.'],
    [ReportStatus.NOT_STARTED]: [
      'Select the reporting standards on the left and click',
      '“Generate report” to view and prepare final report.'
    ],
    [ReportStatus.IN_PROGRESS]: ['Generating report...'],
    [ReportStatus.FAILED]: [FailedReportErrorMessages.TECHNICAL]
  };

  constructor(
    private readonly accountsBuilderService: AccountsBuilderService,
    private readonly alertService: AlertService,
    private readonly reportSectionsService: ReportSectionsService,
    private readonly reportNotesService: ReportNotesService,
    private readonly accountingPoliciesService: AccountingPoliciesService,
    private readonly xbrlToggleService: XbrlToggleService
  ) {
    window.addEventListener('onReportDataChangeJsonforms', event => this.setRegreshReportWarningBanner(event));
  }

  ngOnInit(): void {
    this.getReportData();
    this.reportParams.clientId = this.clientUUID;

    features.getState(AppSettings.FeatureFlag.AccountsProduction.DynamicIssueLogEnabled).subscribe(isToggledOn => {
      this.isAccountProductionDynamicIssueLogEnabled = isToggledOn;
    });

    this.$subscriptions.push(
      this.accountsBuilderService.reportStatusSubject.subscribe((status: ReportStatus) => {
        this.getReportStatus(status);
      })
    );

    if (this.hasSourceData) {
      this.accountsBuilderService.reportStatusSubject.next(ReportStatus.NOT_STARTED);
    }

    this.$subscriptions.push(
      this.reportSectionsService.navigateToBookmark.subscribe(newBookmark => {
        this.displayReport.navigate(newBookmark.PageIndex, null);
      })
    );

    this.$subscriptions.push(
      this.reportNotesService.triggerRefreshStatusBanner$.subscribe(trigger => {
        this.displayWarningBanner = trigger;
      })
    );

    this.$subscriptions.push(
      this.accountingPoliciesService.triggerRefreshStatusBanner$.subscribe(trigger => {
        this.displayWarningBanner = trigger;
      })
    );

    this.$subscriptions.push(
      this.xbrlToggleService.xbrlEditorModeToggle$.subscribe(([xbrlModeOn, taxonomyId]) => {
        this.isXbrlEditorModeOn = xbrlModeOn;
        this.taxonomyId = taxonomyId;
      })
    );
  }

  onXbrlEditorModeChanged([xblrMode, taxonomyId]: [boolean, number]): void {
    console.log(`display report component --> xbrl mode on: ${xblrMode}, taxonomy id: ${taxonomyId}`);
    this.isXbrlEditorModeOn = xblrMode;
    this.taxonomyId = taxonomyId;
  }

  updateShowIssuesLog(showIssuesLog: boolean) {
    this.showIssuesLog = showIssuesLog;
  }

  shouldDisplayIssueLogComponent(): boolean {
    return (
      this.status !== this.statuses.IN_PROGRESS &&
      this.status !== this.statuses.FAILED &&
      this.useLastSuccessfulPreviousId === false
    );
  }

  getNoTrialBalanceDataMessage(additionalMessage: string = ' '): string {
    return `No trial balance data available to generate${additionalMessage}report.`;
  }

  updateStatus(status: ReportStatus): void {
    this.status = status;
    if (this.isLoading) this.updateIsLoading(false);
  }

  setPeriodsIds(periodId: string, previousPeriodId: string): void {
    if (this.reportParams.accountPeriodId !== periodId) this.reportParams.accountPeriodId = periodId;
    if (previousPeriodId && this.reportParams.previousAccountPeriodId !== previousPeriodId)
      this.reportParams.previousAccountPeriodId = previousPeriodId;
  }

  setReportId(reportId: string) {
    this.reportParams.reportId = reportId;
  }

  handleFailedStatus(errorCode: string = null) {
    if (errorCode === FailedReportErrorCodes.CALCULATION) {
      this.messageMapping = {
        ...this.messageMapping,
        [ReportStatus.FAILED]: [FailedReportErrorMessages.CALCULATION]
      };
      this.bannerErrorMessage = FailedReportErrorMessages.BANNER_CALCULATION;
    }
    if (!this.hasLatestSuccessfulReport) {
      this.updateStatus(ReportStatus.FAILED);
      return;
    }

    this.updateStatus(ReportStatus.SUCCESS);
    if (errorCode) {
      this.displayFailedBanner = true;
    }
    this.useLastSuccessfulPreviousId = true;
  }

  getReportData(status: ReportStatus = null, wasReportJustGenerated: boolean = false): void {
    this.accountsBuilderService
      .getReportData(this.clientUUID, this.periodUUID)
      .pipe(
        catchError(() => {
          this.updateIsLoading(false);
          return EMPTY;
        })
      )
      .subscribe((reportData: ReportData) => {
        if (!moment.utc(reportData?.lastSuccessfullTimeUtc).isAfter(moment.utc('0001-01-01'))) {
          this.updateStatus(
            this.hasSourceData
              ? reportData?.errorCode
                ? ReportStatus.FAILED
                : ReportStatus.NOT_STARTED
              : ReportStatus.NO_DATA
          );
          this.updateIsLoading(false);
          return;
        }
        this.reportData = reportData;
        this.displayWarningBanner = this.shouldDisplayWarningBanner(reportData);
        this.displayIssueLogSnackBar(wasReportJustGenerated, reportData);
        if (reportData.lastSuccessfullTimeUtc) this.hasLatestSuccessfulReport = true;
        if (reportData?.errorCode) {
          this.getReportStatus(ReportStatus.FAILED, reportData.errorCode);
        } else if (!status) {
          this.getReportStatus(ReportStatus.SUCCESS);
        } else {
          this.updateStatus(status);
        }
        this.setPeriodsIds(reportData.periodId, reportData.previousPeriodId);
        this.setReportId(this.reportData.reportingStandard?.id);
      });
  }

  getReportStatus(status: ReportStatus, errorCode: string = null): void {
    switch (status) {
      case ReportStatus.FAILED:
        this.handleFailedStatus(errorCode);
        break;
      case ReportStatus.SUCCESS:
        if (this.status === ReportStatus.IN_PROGRESS) {
          this.alertService.alertsSubject.next({
            type: 'success',
            message: 'Report generated successfully'
          });
        }
        this.hasLatestSuccessfulReport = true;
        this.displayFailedBanner = false;
        this.getReportData(ReportStatus.SUCCESS, this.status === ReportStatus.IN_PROGRESS);
        break;
      case ReportStatus.IN_PROGRESS:
        this.updateStatus(ReportStatus.IN_PROGRESS);
        break;
      default:
        return;
    }
  }

  triggerGenerateReport(): void {
    this.accountsBuilderService.triggerGenerateReportSubject.next();
  }

  updateIsLoading(isLoading: boolean): void {
    this.isLoading = isLoading;
    this.isLoadingUpdate.emit(isLoading);
  }

  onNavigationReady(bookmarks: Bookmark[]): void {
    this.reportSectionsService.updateBookmarkList(bookmarks);
    this.navigationReadyEvent();
  }

  displayIssueLogSnackBar(wasReportJustGenerated: boolean, reportData: ReportData) {
    const numberOfValidationIssues = reportData?.validationData?.mandatoryValidationIssuesLogCount ?? 0;
    if (wasReportJustGenerated && numberOfValidationIssues > 0 && this.status !== this.statuses.FAILED) {
      this.issueLogAlert = {
        type: 'error',
        message: `${numberOfValidationIssues} major ${
          numberOfValidationIssues > 1 ? 'issues' : 'issue'
        } require your attention, review them in Issue Log`
      };
    }
  }

  removeSubscriptions() {
    this.$subscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.$subscriptions = [];
  }

  private shouldDisplayWarningBanner(reportData: ReportData): boolean {
    return reportData.isDirty;
  }

  setRegreshReportWarningBanner(event: Event): void {
    const detail = (event as CustomEvent)?.detail;
    this.displayWarningBanner = detail?.isDirty;
  }

  navigationReadyEvent(): void {
    let customEvent = new CustomEvent("onReportReadyInViewer", {
      detail: { navigationStateChange: true }
    });
    window.dispatchEvent(customEvent);
  }

  ngOnDestroy() {
    this.removeSubscriptions();
  }
}
