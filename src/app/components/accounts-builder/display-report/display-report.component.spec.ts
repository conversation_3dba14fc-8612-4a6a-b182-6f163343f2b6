import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { of, Subject, Subscription, throwError } from 'rxjs';
import {
  FailedReportErrorCodes,
  FailedReportErrorMessages,
  ReportData,
  ReportStatus,
  ReportStatusResponse
} from 'src/app/models/report.model';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { DisplayReportComponent } from './display-report.component';
import { MOCK_REPORT_DATA } from 'src/app/models/report.mock';
import { ReportNotesService } from '../../../services/report-notes.service';
import { AccountingPoliciesService } from 'src/app/services/accounting-policies.service';
import { ReportSectionsService } from 'src/app/services/report-sections.service';
import { Bookmark } from 'src/app/models/bookmark.model'; 

describe('DisplayReportComponent', () => {
  let component: DisplayReportComponent;
  let fixture: ComponentFixture<DisplayReportComponent>;
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
    'triggerGenerateReport',
    'getReportStatus',
    'getReportData'
  ]);
  const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', ['']);
  const accountingPoliciesServiceMock = jasmine.createSpyObj('AccountingPoliciesService', ['']);
  const reportSectionsServiceMock = jasmine.createSpyObj('ReportSectionsService', ['updateBookmarkList']);
  reportSectionsServiceMock.bookmarkList = new Subject();
  reportSectionsServiceMock.navigateToBookmark = new Subject();
  const getReportStatusResponse: ReportStatusResponse = {
    status: ReportStatus.SUCCESS,
    errorCode: null
  };

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [DisplayReportComponent],
      providers: [
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        },
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesServiceMock
        },
        {
          provide: ReportSectionsService,
          useValue: reportSectionsServiceMock
        }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    accountsBuilderServiceMock.triggerGenerateReport.and.returnValue(of(MOCK_REPORT_DATA.lastSuccessfullProcessId));
    accountsBuilderServiceMock.getReportStatus.and.returnValue(of(getReportStatusResponse));
    accountsBuilderServiceMock.getReportData.and.returnValue(of(MOCK_REPORT_DATA));
    accountsBuilderServiceMock.triggerGenerateReportSubject = new Subject<any>();
    accountsBuilderServiceMock.reportProcessIdSubject = new Subject<string>();
    accountsBuilderServiceMock.reportStatusSubject = new Subject<string>();
    reportNotesServiceMock.triggerRefreshStatusBanner$ = new Subject<boolean>();
    accountingPoliciesServiceMock.triggerRefreshStatusBanner$ = new Subject<boolean>();

    fixture = TestBed.createComponent(DisplayReportComponent);
    component = fixture.componentInstance;
    component.navigateToBookmark = new Subject<any>();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('hasSourceData', () => {
    let reportStatusSubjectSpy: any;
    beforeEach(() => {
      reportStatusSubjectSpy = spyOn(accountsBuilderServiceMock.reportStatusSubject, 'next');
    });

    it('should call reportStatusSubject with NOT_STARTED if it has source data', () => {
      component.hasSourceData = true;
      component.ngOnInit();

      expect(reportStatusSubjectSpy).toHaveBeenCalledWith(ReportStatus.NOT_STARTED);
    });

    it('should not call reportStatusSubject if does not have source data', () => {
      component.hasSourceData = false;
      component.ngOnInit();

      expect(reportStatusSubjectSpy).not.toHaveBeenCalled();
    });
  });

  describe('updateIsLoading', () => {
    beforeEach(() => {
      spyOn(component.isLoadingUpdate, 'emit');
    });

    it('should update loading state to false', () => {
      component.isLoading = true;
      component.updateIsLoading(false);

      expect(component.isLoading).toBeFalse();
      expect(component.isLoadingUpdate.emit).toHaveBeenCalledWith(false);
    });

    it('should update loading state to true', () => {
      component.isLoading = false;
      component.updateIsLoading(true);

      expect(component.isLoading).toBeTrue();
      expect(component.isLoadingUpdate.emit).toHaveBeenCalledWith(true);
    });
  });

  describe('updateShowIssuesLog', () => {
    it('should update loading state to false', () => {
      component.showIssuesLog = true;
      component.updateShowIssuesLog(false);

      expect(component.showIssuesLog).toBeFalse();
    });

    it('should update loading state to true', () => {
      component.showIssuesLog = false;
      component.updateShowIssuesLog(true);

      expect(component.showIssuesLog).toBeTrue();
    });
  });

  describe('shouldDisplayIssueLogComponent', () => {
    it('should not display issue log component when status is failed', () => {
      component.status = ReportStatus.FAILED;
      component.useLastSuccessfulPreviousId = false;
      const result = component.shouldDisplayIssueLogComponent();

      expect(result).toBeFalse();
    });

    it('should display issue log component', () => {
      component.status = ReportStatus.SUCCESS;
      component.useLastSuccessfulPreviousId = false;
      const result = component.shouldDisplayIssueLogComponent();

      expect(result).toBeTrue();
    });

    it('should not display issue log component is previous report is displayed', () => {
      component.status = ReportStatus.SUCCESS;
      component.useLastSuccessfulPreviousId = true;
      const result = component.shouldDisplayIssueLogComponent();

      expect(result).toBeFalse();
    });
  });

  describe('unsubscribe', () => {
    beforeEach(() => {});

    it('should unsubscribe all the subscriptions found after ngOnDestroy', () => {
      const MOCK_SUBSCRIPTIONS_ARRAY = [new Subscription(), new Subscription()];
      component.$subscriptions = [...MOCK_SUBSCRIPTIONS_ARRAY];

      spyOn(component.$subscriptions[0], 'unsubscribe');
      spyOn(component.$subscriptions[1], 'unsubscribe');
      component.ngOnDestroy();

      expect(MOCK_SUBSCRIPTIONS_ARRAY[0].unsubscribe).toHaveBeenCalled();
      expect(MOCK_SUBSCRIPTIONS_ARRAY[1].unsubscribe).toHaveBeenCalled();
      expect(component.$subscriptions.length).toEqual(0);
    });
  });

  describe('updateStatus', () => {
    let updateIsLoadingSpy: any;
    let reportStatusSubjectSpy: any;

    beforeEach(() => {
      updateIsLoadingSpy = spyOn(component, 'updateIsLoading');
      reportStatusSubjectSpy = spyOn(accountsBuilderServiceMock.reportStatusSubject, 'next');
      component.isLoading = true;
    });

    it('should not update loading state and call report status subject with IN_PROGRESS', () => {
      component.isLoading = false;
      component.updateStatus(ReportStatus.IN_PROGRESS);

      expect(updateIsLoadingSpy).not.toHaveBeenCalled();
    });
  });

  describe('setPeriodsIds', () => {
    const periodId = 'currentPeriodId';
    const previousPeriodId = 'previousPeriodId';
    const reportParamsCurrentPeriodId = 'reportParamsCurrentPeriodId';
    const reportParamsPreviousPeriodId = 'reportParamsPreviousPeriodId';

    beforeEach(() => {
      component.reportParams.accountPeriodId = reportParamsCurrentPeriodId;
      component.reportParams.previousAccountPeriodId = reportParamsPreviousPeriodId;
    });

    it('should update both periods if valid periods values provided', () => {
      component.setPeriodsIds(periodId, previousPeriodId);
      expect(component.reportParams.accountPeriodId).toEqual(periodId);
      expect(component.reportParams.previousAccountPeriodId).toEqual(previousPeriodId);
    });

    it('should not update previous account period if value is not provided', () => {
      component.setPeriodsIds(periodId, null);
      expect(component.reportParams.accountPeriodId).toEqual(periodId);
      expect(component.reportParams.previousAccountPeriodId).toEqual(reportParamsPreviousPeriodId);
    });
  });

  describe('setReportID', () => {
    beforeEach(() => {
      component.reportParams.reportId = '6184fa5afd07d03dc0839d24';
    });

    it('should set the report id ', () => {
      component.setReportId('6184fa5afd07d03dc0839d34');
      expect(component.reportParams.reportId).toEqual('6184fa5afd07d03dc0839d34');
    });
  });

  describe('handleFailedStatus', () => {
    beforeEach(() => {
      component.hasLatestSuccessfulReport = true;
      component.displayFailedBanner = false;
      component.useLastSuccessfulPreviousId = false;
    });

    it('should set message displayed on failed status to calculation error when errorCode from BE is CALCULATION_ERROR', () => {
      component.handleFailedStatus(FailedReportErrorCodes.CALCULATION);

      expect(component.messageMapping[ReportStatus.FAILED]).toEqual([FailedReportErrorMessages.CALCULATION]);
    });

    it('should set error banner message to BANNER_CALCULATION when errorCode from BE is CALCULATION_ERROR', () => {
      component.handleFailedStatus(FailedReportErrorCodes.CALCULATION);

      expect(component.bannerErrorMessage).toEqual(FailedReportErrorMessages.BANNER_CALCULATION);
    });

    it('should set message displayed on failed status to technical error when errorCode BE is TECHNICAL_ERROR', () => {
      component.handleFailedStatus(FailedReportErrorCodes.TECHNICAL);

      expect(component.messageMapping[ReportStatus.FAILED]).toEqual([FailedReportErrorMessages.TECHNICAL]);
    });

    it('should set error banner message to BANNER_TECHNICAL when errorCode from BE is TECHNICAL_ERROR', () => {
      component.handleFailedStatus(FailedReportErrorCodes.TECHNICAL);

      expect(component.bannerErrorMessage).toEqual(FailedReportErrorMessages.BANNER_TECHNICAL);
    });

    it('should update status with FAILED if it does not have a previous successful report', () => {
      const spy = spyOn(component, 'updateStatus');
      component.hasLatestSuccessfulReport = false;
      component.handleFailedStatus(null);

      expect(spy).toHaveBeenCalledWith(ReportStatus.FAILED);
    });

    it('should use last successful report previous ID if a previous successful exists and current status is FAILED', () => {
      component.status = ReportStatus.FAILED;
      component.handleFailedStatus(null);

      expect(component.useLastSuccessfulPreviousId).toBeTrue();
    });
  });

  describe('getReportData', () => {
    let updateStatusSpy: any;
    let reportStatusSubjectSpy: any;
    beforeEach(() => {
      updateStatusSpy = spyOn(component, 'updateStatus');
      component.useLastSuccessfulPreviousId = false;
      reportStatusSubjectSpy = spyOn(accountsBuilderServiceMock.reportStatusSubject, 'next');
    });

    it('should get latest report data on ngOnInit ', () => {
      const getReportDataSpy = spyOn(component, 'getReportData');
      component.ngOnInit();

      expect(getReportDataSpy).toHaveBeenCalled();
    });

    it('should set periods IDs with correct period Ids', () => {
      const setPeriodsIdsSpy = spyOn(component, 'setPeriodsIds');
      component.getReportData();

      expect(setPeriodsIdsSpy).toHaveBeenCalledWith(MOCK_REPORT_DATA.periodId, MOCK_REPORT_DATA.previousPeriodId);
    });

    it('should set previous period ID to latest report one if it uses last successful report periodId', () => {
      const setPeriodsIdsSpy = spyOn(component, 'setPeriodsIds');
      component.useLastSuccessfulPreviousId = true;
      component.getReportData();

      expect(setPeriodsIdsSpy).toHaveBeenCalledWith(MOCK_REPORT_DATA.periodId, MOCK_REPORT_DATA.previousPeriodId);
    });

    it('should update status with provided status', () => {
      component.getReportData(ReportStatus.IN_PROGRESS);

      expect(updateStatusSpy).toHaveBeenCalledWith(ReportStatus.IN_PROGRESS);
    });

    it('should not call reportProcessIdSubject when status is provided', () => {
      component.getReportData(ReportStatus.IN_PROGRESS);

      expect(reportStatusSubjectSpy).not.toHaveBeenCalled();
    });

    it('should update loading state if get latest report data throws error', () => {
      const spy = spyOn(component, 'updateIsLoading');
      accountsBuilderServiceMock.getReportData.and.returnValue(throwError(Error));
      component.getReportData();

      expect(spy).toHaveBeenCalled();
    });

    it('should display warning banner if it is dirty', () => {
      component.displayWarningBanner = false;
      const mockData = { ...MOCK_REPORT_DATA, isDirty: true };
      accountsBuilderServiceMock.getReportData.and.returnValue(of(mockData));
      component.getReportData();

      expect(component.displayWarningBanner).toBeTrue();
    });

    it('should update status to failed if technical error', () => {
      component.hasSourceData = true;
      const mockData = { ...MOCK_REPORT_DATA, errorCode: 'Technical Error', lastSuccessfullTimeUtc: '0001-01-01' };
      accountsBuilderServiceMock.getReportData.and.returnValue(of(mockData));
      component.getReportData();

      expect(updateStatusSpy).toHaveBeenCalledWith(ReportStatus.FAILED);
    });

    it('should display issue log snack bar if validations issues are found', () => {
      const mockData = {
        ...MOCK_REPORT_DATA,
        validationData: {
          validationIssues: [{ name: 'test' }],
          mandatoryValidationIssuesLogCount: 1
        }
      } as ReportData;

      accountsBuilderServiceMock.getReportData.and.returnValue(of(mockData));
      component.displayIssueLogSnackBar(true, mockData);
      expect(component.issueLogAlert.type).toBe('error');
    });

    it('should not display issue log snack bar if no validation data is found', () => {
      const mockData = {
        ...MOCK_REPORT_DATA,
        validationData: undefined
      } as ReportData;
      component.displayIssueLogSnackBar(true, mockData);
      expect(component.issueLogAlert).toBeNull();
    });

    it('should display issue log snack bar with message adapted depending on number of issues found', () => {
      let mockData = {
        ...MOCK_REPORT_DATA,
        validationData: {
          validationIssues: [{ name: 'test' }],
          mandatoryValidationIssuesLogCount: 1
        }
      } as ReportData;

      component.displayIssueLogSnackBar(true, mockData);
      expect(component.issueLogAlert.message).toContain('issue ');
      mockData = {
        ...MOCK_REPORT_DATA,
        validationData: {
          validationIssues: [{ name: 'test' }, { name: 'test2' }],
          mandatoryValidationIssuesLogCount: 2
        }
      } as ReportData;

      component.displayIssueLogSnackBar(true, mockData);
      expect(component.issueLogAlert.message).toContain('issues ');
    });

    it('should not display issue log snack bar if no validations are found', () => {
      component.displayIssueLogSnackBar(true, MOCK_REPORT_DATA);
      expect(component.issueLogAlert).toBeNull();
    });

    it('should display issue log snack bar if validations issues are found', () => {
      const mockData = {
        ...MOCK_REPORT_DATA,
        validationData: {
          validationIssues: [{ name: 'test' }],
          mandatoryValidationIssuesLogCount: 1
        }
      } as ReportData;

      accountsBuilderServiceMock.getReportData.and.returnValue(of(mockData));
      component.displayIssueLogSnackBar(true, mockData);
      expect(component.issueLogAlert.type).toBe('error');
    });
  });

  describe('getReportStatus', () => {
    const processId = '1234';
    let reportStatusResponse: ReportStatusResponse;
    beforeEach(() => {
      reportStatusResponse = {
        status: ReportStatus.SUCCESS,
        errorCode: null
      };
    });
    it('should handle failed status with null if it throws error', () => {
      const spy = spyOn(component, 'handleFailedStatus');
      accountsBuilderServiceMock.getReportStatus.and.returnValue(throwError(Error));
      component.getReportStatus(ReportStatus.FAILED);

      expect(spy).toHaveBeenCalled();
    });

    it('should handle failed status with errorCode if status is FAILED', () => {
      const spy = spyOn(component, 'handleFailedStatus');
      reportStatusResponse.status = ReportStatus.FAILED;
      reportStatusResponse.errorCode = FailedReportErrorCodes.TECHNICAL;
      accountsBuilderServiceMock.getReportStatus.and.returnValue(of(reportStatusResponse));
      component.getReportStatus(ReportStatus.FAILED);

      expect(spy).toHaveBeenCalled();
    });

    it('should get report data if status is SUCCESS', () => {
      const spy = spyOn(component, 'getReportData');
      component.getReportStatus(ReportStatus.SUCCESS);

      expect(spy).toHaveBeenCalled();
    });

    it('should have latest successful report if status is SUCCESS', () => {
      component.hasLatestSuccessfulReport = false;
      component.getReportStatus(ReportStatus.SUCCESS);

      expect(component.hasLatestSuccessfulReport).toBeTrue();
    });

    it('should update status with IN_PROGRESS if status is IN_PROGRESS', () => {
      const spy = spyOn(component, 'updateStatus');
      reportStatusResponse.status = ReportStatus.IN_PROGRESS;
      accountsBuilderServiceMock.getReportStatus.and.returnValue(of(reportStatusResponse));
      component.getReportStatus(ReportStatus.IN_PROGRESS);

      expect(spy).toHaveBeenCalledWith(ReportStatus.IN_PROGRESS);
    });

    it('should not display banner if report is SUCCESS', () => {
      component.displayFailedBanner = true;
      component.getReportStatus(ReportStatus.SUCCESS);

      expect(component.displayFailedBanner).toBeFalse();
    });

    it('should not call any methods if report status is undefined', () => {
      const spyUpdateStatus = spyOn(component, 'updateStatus');
      const spyHandleFailedStatus = spyOn(component, 'handleFailedStatus');
      const spyGetReportData = spyOn(component, 'getReportData');

      reportStatusResponse.status = undefined;
      accountsBuilderServiceMock.getReportStatus.and.returnValue(of(reportStatusResponse));

      expect(spyUpdateStatus).not.toHaveBeenCalled();
      expect(spyHandleFailedStatus).not.toHaveBeenCalled();
      expect(spyGetReportData).not.toHaveBeenCalled();
    });
  });

  it('should call next on triggerGenerateReportSubject when trigger generate report', () => {
    const triggerGenerateReportSubjectSpy = spyOn(accountsBuilderServiceMock.triggerGenerateReportSubject, 'next');
    component.triggerGenerateReport();

    expect(triggerGenerateReportSubjectSpy).toHaveBeenCalled();
  });

  it('should call setRegreshReportWarningBanner when onReportDataChangeJsonforms event is triggered', () => {
    const event = new CustomEvent('onReportDataChangeJsonforms');
    const setRegreshReportWarningBannerSpy = spyOn(component, 'setRegreshReportWarningBanner');
    window.dispatchEvent(event);
    expect(setRegreshReportWarningBannerSpy).toHaveBeenCalledWith(event);
  });

  describe('onNavigationReady', () => {
    it('should update bookmark list and trigger navigation ready event', () => {
      const bookmarks: Bookmark[] = [{ Text: '1', PageIndex: 1, Indices: 'section1' }];
      const navigationReadyEventSpy = spyOn(component, 'navigationReadyEvent');
      component.onNavigationReady(bookmarks);
  
      expect(reportSectionsServiceMock.updateBookmarkList).toHaveBeenCalledWith(bookmarks);
      expect(navigationReadyEventSpy).toHaveBeenCalled();
    });
  });

  describe('navigationReadyEvent', () => {
    it('should dispatch a custom event "onReportReadyInViewer" with correct detail', () => {
      const customEventSpy = spyOn(window, 'dispatchEvent');
      const expectedEvent = new CustomEvent("onReportReadyInViewer", {
        detail: { navigationStateChange: true }
      });

      component.navigationReadyEvent();

      expect(customEventSpy).toHaveBeenCalledWith(expectedEvent);
    });
  });
  
});
