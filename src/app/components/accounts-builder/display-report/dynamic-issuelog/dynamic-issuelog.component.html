<div class="issue-log-container"  [ngClass]="{
  'issue-log-container--expanded': shouldShowExpandedIssueLog(),
  'issue-log-container--expanded-with-banner': shouldShowExpandedIssueLog() && hasBanner,
  'issue-log-container--expanded-no-banner': shouldShowExpandedIssueLog() && !hasBanner
}">
    <section class="issue-log-header">
      <div [ngClass]="!shouldShowChevron() ? 'issue-log-hide-chevron': 'issue-log-show-chevron'">
        <iris-icon
          *ngIf="shouldShowChevron()"
          size="28"
          [type]="shouldShowExpandedIssueLog() ? 'chevron-down' : 'chevron-up'"
          background-colour="#ffffff"
          icon-colour="black"
          padding="0"
          (click)="onClick()"
        ></iris-icon>
      </div>
      <div class="issue-log-title" [ngClass]="{'issue-log-title--with-issues': shouldShowChevron()}">
        <p>Issue Log</p>
        <p class="issue-log-count" *ngIf="issueLogData?.validationIssues.length > 0 ">({{ issueLogData.validationIssues.length }})</p>
      </div>
      <elements-accounts-builder-component-v01-pkg-dynamic-issuelog-header-details
        *ngIf="!shouldShowExpandedIssueLog()"
        [issueLogData] = "issueLogData ? issueLogData : undefined"
        [reportStatus] = "reportStatus">
      </elements-accounts-builder-component-v01-pkg-dynamic-issuelog-header-details>
    </section>
    <div class="issue-log-expanded-info" *ngIf="shouldShowExpandedIssueLog()">
      <iris-tabs-mat (activechanged)="$event.stopPropagation();" class="main-tabs">
        <iris-tab-mat
          *ngFor="let tab of tabs"
          [label]="getTabTitleWithIssueCount(tab.title, tab.issueType)"
          [active]="tab.title === activeTab"
          [hidden]="tab.isHidden"
          [disabled]="tab.isDisabled">
        <mat-accordion [multi]="true">
          <elements-accounts-builder-component-v01-pkg-dynamic-issue-details
            *ngFor="let validationIssue of getValidationIssues(tab.issueType)"
            [issueDetails]="validationIssue"
            [isFixDisabled]="isFixDisabled">
          </elements-accounts-builder-component-v01-pkg-dynamic-issue-details>
        </mat-accordion>
        </iris-tab-mat>
      </iris-tabs-mat>
    </div>
  </div>
  