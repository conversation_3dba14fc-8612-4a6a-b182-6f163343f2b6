import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import {
  ReportStatus,
  ValidationIssueCategory
} from 'src/app/models/report.model';
import { IssueLog, IssueLogData } from 'src/app/models/dynamic-issuelog.model';

import { DynamicIssueLogHeaderDetailsComponent } from './dynamic-issuelog-header-details.component';

describe('IssueDetailsComponent', () => {
  let component: DynamicIssueLogHeaderDetailsComponent;
  let fixture: ComponentFixture<DynamicIssueLogHeaderDetailsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DynamicIssueLogHeaderDetailsComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicIssueLogHeaderDetailsComponent);
    component = fixture.componentInstance;
    component.reportStatus = ReportStatus.SUCCESS;
    component.issueLogData = {
      validationIssues: [],
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 0
    } as any as IssueLogData;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update messageDetails when issueLogData changes', () => {
    component.issueLogData = {
      validationIssues: [
        {
          category: ValidationIssueCategory.Mandatory,
          group: "DataScreen",
          issueTitle: "Issue title",
          issueDescription: "Issue description",
          issueLongDescription: "Issue long description",
          issuePath: "",
          screenId: "Test Screen"
        }
      ],
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 1
    } as IssueLogData;
    component.reportStatus = ReportStatus.SUCCESS;
    component.ngOnChanges({
      issueLogData: {
        currentValue: component.issueLogData,
        previousValue: undefined,
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(component.messageDetails.type).toEqual('alert-triangle');
    expect(component.messageDetails.message).toEqual('(1)');
  });

  it('should set messageDetails for no issues detected', () => {
    component.issueLogData = {
      validationIssues: [],
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 0
    } as IssueLogData;
    component.reportStatus = ReportStatus.SUCCESS;
    component.ngOnInit();

    expect(component.messageDetails.type).toEqual('check');
    expect(component.messageDetails.message).toEqual('No issues detected');
    expect(component.messageDetails.iconColour).toEqual('#0c6a00');
    expect(component.messageDetails.backgroundColor).toEqual('#ffffff');
  });

  it('should set messageDetails for issues summary', () => {
    component.issueLogData = {
      validationIssues: [
        {
          category: ValidationIssueCategory.Mandatory,
          group: "DataScreen",
          issueTitle: "Issue title",
          issueDescription: "Issue description",
          issueLongDescription: "Issue long description",
          issuePath: "",
          screenId: "Test Screen"
        } as IssueLog
      ],
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 1
    } as IssueLogData;
    component.reportStatus = ReportStatus.SUCCESS;
    component.ngOnInit();

    expect(component.messageDetails.type).toEqual('alert-triangle');
    expect(component.messageDetails.message).toEqual('(1)');
    expect(component.messageDetails.iconColour).toEqual('#c8102e');
    expect(component.messageDetails.backgroundColor).toEqual('#f7f8fA');
  });

  it('should return true for hasMandatoryAndAdvisoryIssues when both counts are greater than 0', () => {
    component.issueLogData = {
      advisoryValidationIssuesLogCount: 1,
      mandatoryValidationIssuesLogCount: 1
    } as IssueLogData;
    const result = component.hasMandatoryAndAdvisoryIssues();

    expect(result).toBeTrue();
  });

  it('should return false for hasMandatoryAndAdvisoryIssues when counts are 0', () => {
    component.issueLogData = {
      advisoryValidationIssuesLogCount: 0,
      mandatoryValidationIssuesLogCount: 0
    } as IssueLogData;
    const result = component.hasMandatoryAndAdvisoryIssues();

    expect(result).toBeFalse();
  });

  it('should return true for shouldDisplayNoIssueMessage when validationIssues is empty', () => {
    component.issueLogData = {
      validationIssues: []
    } as IssueLogData;
    expect(component.shouldDisplayNoIssueMessage()).toBeTrue();
  });

  it('should return false for shouldDisplayNoIssueMessage when validationIssues exist', () => {
    component.issueLogData = {
      validationIssues: [
        {
          category: ValidationIssueCategory.Mandatory,
          group: "DataScreen",
          issueTitle: "Issue title",
          issueDescription: "Issue description",
          issueLongDescription: "Issue long description",
          issuePath: "",
          screenId: "Test Screen"
        } as IssueLog
      ]
    } as IssueLogData;
    expect(component.shouldDisplayNoIssueMessage()).toBeFalse();
  });

  it('should return true for shouldDisplayNoDataAvailableMessage when issueLogData is undefined', () => {
    component.issueLogData = undefined;
    component.reportStatus = ReportStatus.FAILED;
    expect(component.shouldDisplayValidationDetail()).toBeFalse();
  });

  it('should return true for shouldDisplayNoDataAvailableMessage when reportStatus is not SUCCESS', () => {
    component.issueLogData = {
      validationIssues: []
    } as IssueLogData;
    component.reportStatus = ReportStatus.FAILED;
    expect(component.shouldDisplayValidationDetail()).toBeFalse();
  });

  it('should return false for shouldDisplayNoDataAvailableMessage when reportStatus is SUCCESS', () => {
    component.issueLogData = {
      validationIssues: []
    } as IssueLogData;
    component.reportStatus = ReportStatus.SUCCESS;
    expect(component.shouldDisplayValidationDetail()).toBeTrue();
  });

});
