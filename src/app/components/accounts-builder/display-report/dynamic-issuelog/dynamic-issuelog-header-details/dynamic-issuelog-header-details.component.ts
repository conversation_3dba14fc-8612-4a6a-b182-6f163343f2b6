import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { IssueLogHeaderDetailsModel } from 'src/app/models/issue-log-header-details.model';
import { ReportStatus } from 'src/app/models/report.model';
import { IssueLogData } from 'src/app/models/dynamic-issuelog.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-dynamic-issuelog-header-details',
  templateUrl: './dynamic-issuelog-header-details.component.html',
  styleUrls: ['./dynamic-issuelog-header-details.component.scss']
})
export class DynamicIssueLogHeaderDetailsComponent implements OnInit {
  @Input() issueLogData: IssueLogData;
  @Input() reportStatus: ReportStatus;
  statuses = ReportStatus;
  messageDetails: IssueLogHeaderDetailsModel;
  
  ngOnInit(): void {
    this.updateMessageDetails();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.issueLogData) {
      this.updateMessageDetails();
    }
  }

  updateMessageDetails() {
    if (this.shouldDisplayNoIssueMessage()) {
      this.messageDetails = {
        type: 'check',
        iconColour: '#0c6a00',
        backgroundColor: '#ffffff',
        message: 'No issues detected',
        styleClass: 'empty-validation-list'
      };
      return;
    }
    this.messageDetails = {
      type: 'alert-triangle',
      iconColour: '#c8102e',
      backgroundColor: '#f7f8fA',
      message: `(${this.issueLogData?.mandatoryValidationIssuesLogCount})`,
      styleClass: ''
    };
  }

  hasMandatoryAndAdvisoryIssues() {
    return ( this.issueLogData?.mandatoryValidationIssuesLogCount > 0 && 
              this.issueLogData?.advisoryValidationIssuesLogCount > 0 );
  }

  shouldDisplayNoIssueMessage() {
    return this.issueLogData?.validationIssues?.length === 0;
  }

  shouldDisplayValidationDetail() {
    return !(!this.issueLogData || this.reportStatus !== this.statuses.SUCCESS);
  }
}
