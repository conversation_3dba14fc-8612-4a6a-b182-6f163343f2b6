<div hideToggle disabled class="issue-details">
    <div class="mat-expansion-header-issue-details issue-details-main-row">
      <div class="issue-details-content-col">
        <div class="issue-detail-header-row">
          <iris-icon-only [type]="isIssueMandatory() ? 'alert-triangle' : 'alert-circle'"
            [ngClass]="{'advisory':!isIssueMandatory(), 'critical': isIssueMandatory()}"
            [size]="20">
          </iris-icon-only>
          <span class="header-desciption" [ngClass]="{'advisory':!isIssueMandatory(), 'critical': isIssueMandatory()}">
            {{ issueDetails?.issueTitle }}
          </span>
        </div>
        <div class="issue-details-description-row">
          <!-- <span class="issue-description">
            {{ issueDetails?.issueDescription ? (issueDetailsOpenState ? issueDetails.issueDescription : (issueDetails.issueDescription | slice:0:40) + (issueDetails.issueDescription.length > 40 ? '...' : '')) : '' }}
          </span> -->
          <span class="issue-description" *ngIf="!issueDetailsOpenState">
            {{ issueDetails?.issueDescription ? issueDetails.issueDescription : '' }}
          </span>
          <span class="issue-description" *ngIf="issueDetailsOpenState">
            {{ issueDetails?.issueLongDescription ? issueDetails.issueLongDescription : '' }}
          </span>
          <a class="more-info-link" (click)="toggleIssueDetails()" *ngIf="issueDetails?.issueDescription && issueDetails?.issueLongDescription ">
            {{ issueDetailsOpenState ? 'Less information' : 'More information' }}
          </a>
        </div>
      </div>
      <div class="issue-fix-content">
        <div class="fix-btn-col">
          <button 
            [disabled]="setFixDisabledState()" 
            [ngClass]="{'fix-btn-disabled': setFixDisabledState() }" 
            class="fix-btn" (click)="onFixClick(issueDetails)">
              <iris-icon-only 
                [type]="issueDetails?.group === 'DataScreen' ? 'edit' : 'external-link'"
                [size]="16" icon-colour="#1B69B9" 
                background-colour="#fff" 
                style="vertical-align: middle;">
              </iris-icon-only>
            <span>Fix</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  