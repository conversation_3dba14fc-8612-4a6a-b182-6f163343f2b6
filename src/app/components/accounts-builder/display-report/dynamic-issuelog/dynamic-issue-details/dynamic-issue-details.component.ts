import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { IssueLog } from 'src/app/models/dynamic-issuelog.model';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-dynamic-issue-details',
  templateUrl: './dynamic-issue-details.component.html',
  styleUrls: ['./dynamic-issue-details.component.scss']
})
export class DynamicIssueDetailsComponent {
  @Input() issueDetails: IssueLog = null;
  @Input() isFixDisabled: boolean = true;
  issueDetailsOpenState = false;

  constructor(private readonly router: Router) {}

  ngOnInit(): void {
    this.issueDetailsOpenState = false;
  }

  toggleIssueDetails(): void {
    this.issueDetailsOpenState = !this.issueDetailsOpenState;
  }

  isIssueMandatory(): boolean {
    return this.issueDetails?.category === 'Mandatory';
  }

  onFixClick(issueDetails: IssueLog): void {
    if (issueDetails.group !== 'DataScreen') {
      this.router.navigateByUrl(issueDetails.issuePath);
    } else {
      console.log('Raise event with Screen ID:', issueDetails.screenId);
      this.setSelectedSection(issueDetails.screenId);
    }
  }

  setSelectedSection(screenId: string): void {
    let customEvent = new CustomEvent('issueFixScreen', {
      detail: { screenId: screenId }
    });
    window.dispatchEvent(customEvent);
  }

  setFixDisabledState(): boolean {
    if (this.issueDetails?.group === 'DataScreen') {
      return (
        this.isFixDisabled ||
        this.issueDetails.screenId === undefined ||
        this.issueDetails.screenId === null ||
        this.issueDetails.screenId === ''
      );
    } else {
      return this.issueDetails.issuePath === undefined || this.issueDetails.screenId === null;
    }
  }
}
