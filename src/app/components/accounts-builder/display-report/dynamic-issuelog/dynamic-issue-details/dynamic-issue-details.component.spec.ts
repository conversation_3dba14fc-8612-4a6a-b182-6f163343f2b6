import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ValidationIssueCategory } from 'src/app/models/report.model';
import { IssueLog } from 'src/app/models/dynamic-issuelog.model';
import { DynamicIssueDetailsComponent } from './dynamic-issue-details.component';

describe('IssueDetailsComponent', () => {
  let component: DynamicIssueDetailsComponent;
  let fixture: ComponentFixture<DynamicIssueDetailsComponent>;

  const mockIssueLog: IssueLog = {
    category: ValidationIssueCategory.Mandatory,
    group: "DataScreen",
    issueTitle: "Issue title",
    issueDescription: "Issue description",
    issueLongDescription: "Issue long description",
    issuePath: "/client-management/client/ClientId/information",
    screenId: "Test Screen"
  }

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DynamicIssueDetailsComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicIssueDetailsComponent);
    component = fixture.componentInstance;
    component.issueDetails = mockIssueLog;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize issueDetailsOpenState to false on ngOnInit', () => {
    component.ngOnInit();
    expect(component.issueDetailsOpenState).toBeFalse();
  });

  it('should toggle issueDetailsOpenState correctly', () => {
    component.issueDetailsOpenState = false;

    component.toggleIssueDetails();
    expect(component.issueDetailsOpenState).toBeTrue();

    component.toggleIssueDetails();
    expect(component.issueDetailsOpenState).toBeFalse();
  });

  it('should handle isIssueMandatory correctly', () => {
    component.issueDetails = { ...mockIssueLog, category: ValidationIssueCategory.Mandatory };
    expect(component.isIssueMandatory()).toBeTrue();

    component.issueDetails = { ...mockIssueLog, category: ValidationIssueCategory.Advisory };
    expect(component.isIssueMandatory()).toBeFalse();

    component.issueDetails = null;
    expect(component.isIssueMandatory()).toBeFalse();
  });

  it('should navigate to client-management if group is not DataScreen on onFixClick', () => {
    spyOn(component['router'], 'navigateByUrl');
    component.onFixClick({ ...mockIssueLog, group: 'Client' });

    expect(component['router'].navigateByUrl).toHaveBeenCalledWith('/client-management/client/ClientId/information');
  });

  it('should raise event with screenId if group is DataScreen on onFixClick', () => {
    spyOn(window, 'dispatchEvent');
    const screenId = 'NOTES-AveNoEmploy';
    component.onFixClick({ ...mockIssueLog, group: 'DataScreen', screenId });

    expect(window.dispatchEvent).toHaveBeenCalled();
  });

  it('should return true for setFixDisabledState when group is DataScreen and isFixDisabled is true', () => {
    component.issueDetails = { ...mockIssueLog, group: 'DataScreen' };
    component.isFixDisabled = true;
    expect(component.setFixDisabledState()).toBeTrue();
  });

  it('should return true for setFixDisabledState when group is DataScreen and screenId is undefined', () => {
    component.issueDetails = { ...mockIssueLog, group: 'DataScreen', screenId: undefined };
    expect(component.setFixDisabledState()).toBeTrue();
  });

  it('should return true for setFixDisabledState when group is DataScreen and screenId is null', () => {
    component.issueDetails = { ...mockIssueLog, group: 'DataScreen', screenId: null };
    expect(component.setFixDisabledState()).toBeTrue();
  });

  it('should return true for setFixDisabledState when group is not DataScreen and issuePath is undefined', () => {
    component.issueDetails = { ...mockIssueLog, group: 'Client', issuePath: undefined };
    expect(component.setFixDisabledState()).toBeTrue();
  });

  it('should return true for setFixDisabledState when group is not DataScreen and screenId is null', () => {
    component.issueDetails = { ...mockIssueLog, group: 'Client', screenId: null };
    expect(component.setFixDisabledState()).toBeTrue();
  });

  it('should return false for setFixDisabledState when group is DataScreen and isFixDisabled is false and screenId is defined', () => {
    component.issueDetails = { ...mockIssueLog, group: 'DataScreen', screenId: '123' };
    component.isFixDisabled = false;
    expect(component.setFixDisabledState()).toBeFalse();
  });

  it('should return false for setFixDisabledState when group is not DataScreen and issuePath and screenId are defined', () => {
    component.issueDetails = { ...mockIssueLog, group: 'Client', issuePath: 'some/path', screenId: '123' };
    expect(component.setFixDisabledState()).toBeFalse();
  });

});
