import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReportData, ReportStatus } from 'src/app/models/report.model';
import { IssueLog, IssueLogData } from 'src/app/models/dynamic-issuelog.model';
import { DynamicIssueLogComponent } from './dynamic-issuelog.component';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { Subject } from 'rxjs';

const mockIssueLogData: IssueLogData = {
  validationIssues: [
    {
      category: "Mandatory",
      group: "DataScreen",
      issueTitle: "issueTitle",
      issueDescription: "issueDescription",
      issueLongDescription: "issueLongDescription",
      issuePath: "",
      screenId: "NOTES-AveNoEmploy"
    },
    {
      category: "Advisory",
      group: "DataScreen",
      issueTitle: "issueTitle",
      issueDescription: "issueDescription",
      issueLongDescription: "issueLongDescription",
      issuePath: "",
      screenId: "NOTES-AveNoEmploy"
    }
  ],
  advisoryValidationIssuesLogCount: 1,
  mandatoryValidationIssuesLogCount: 1
};

describe('IssueLogComponent', () => {
  let component: DynamicIssueLogComponent;
  let fixture: ComponentFixture<DynamicIssueLogComponent>;
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
    'triggerGenerateReport',
    'getReportStatus',
    'getReportData',
    'reportStatusSubject'
  ]);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DynamicIssueLogComponent],
      providers: [
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    accountsBuilderServiceMock.reportStatusSubject = new Subject<string>();
    fixture = TestBed.createComponent(DynamicIssueLogComponent);
    component = fixture.componentInstance;
    component.reportData = {
      clientId: 'client-id',
      periodId: 'period-id',
      previousPeriodId: 'previous-period-id',
      entitySetup: {
        reportingStandard: 'FRS102'
      },
      validationData: {
        validationIssues: [{} as IssueLog],
        advisoryValidationIssuesCount: 0,
        mandatoryValidationIssuesLogCount: 0
      } as IssueLogData
    } as ReportData;
    fixture.detectChanges();
  });

  describe('shouldInit', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    describe('ngOnInit', () => {
      it('should subscribe to reportStatusSubject and set showIssueLog to false when status is IN_PROGRESS', () => {
        component.ngOnInit();
        accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.IN_PROGRESS);
  
        expect(component.showIssueLog).toBeFalse();
      });
    });

    describe('onClick', () => {
      it('should toggle showIssueLog and emit showIssuesEvent', () => {
        spyOn(component.showIssuesEvent, 'emit');
        
        component.onClick();
        expect(component.showIssueLog).toBeTrue();
        expect(component.showIssuesEvent.emit).toHaveBeenCalledWith(true);
  
        component.onClick();
        expect(component.showIssueLog).toBeFalse();
        expect(component.showIssuesEvent.emit).toHaveBeenCalledWith(false);
      });
    });

    describe('shouldShowChevron', () => {
      it('should return true if issueLogData has validation issues and reportStatus is SUCCESS', () => {
        component.issueLogData = {
          validationIssues: [{} as IssueLog]
        } as IssueLogData;
        component.reportStatus = ReportStatus.SUCCESS;
  
        expect(component.shouldShowChevron()).toBeTrue();
      });
  
      it('should return false if issueLogData has no validation issues', () => {
        component.issueLogData = {
          validationIssues: []
        } as IssueLogData;
        component.reportStatus = ReportStatus.SUCCESS;
  
        expect(component.shouldShowChevron()).toBeFalse();
      });
  
      it('should return false if reportStatus is not SUCCESS', () => {
        component.issueLogData = {
          validationIssues: [{} as IssueLog]
        } as IssueLogData;
        component.reportStatus = ReportStatus.FAILED;
  
        expect(component.shouldShowChevron()).toBeFalse();
      });
    });

    describe('shouldShowExpandedIssueLog', () => {
      it('should return true if showIssueLog is true and reportStatus is not IN_PROGRESS', () => {
        component.showIssueLog = true;
        component.reportStatus = ReportStatus.SUCCESS;
  
        expect(component.shouldShowExpandedIssueLog()).toBeTrue();
      });
  
      it('should return false if showIssueLog is false', () => {
        component.showIssueLog = false;
        component.reportStatus = ReportStatus.SUCCESS;
  
        expect(component.shouldShowExpandedIssueLog()).toBeFalse();
      });
  
      it('should return false if reportStatus is IN_PROGRESS', () => {
        component.showIssueLog = true;
        component.reportStatus = ReportStatus.IN_PROGRESS;
  
        expect(component.shouldShowExpandedIssueLog()).toBeFalse();
      });
    });

    describe('getValidationIssues', () => {
      it('should return filtered validation issues based on issueType', () => {
        const issueType = 'testType';
        const validationIssues: IssueLog[] = [
          { group: 'testType', issuePath: '' },
          { group: 'otherType', issuePath: '' }
        ];
  
        component.issueLogData = { validationIssues } as IssueLogData;
        const result = component.getValidationIssues(issueType);
  
        expect(result).toEqual([validationIssues[0]]);
      });
  
      it('should return an empty array if no issues match the issueType', () => {
        const issueType = 'nonMatchingType';
        const validationIssues: IssueLog[] = [
          { group: 'testType', issuePath: '' }
        ];
  
        component.issueLogData = { validationIssues } as IssueLogData;
        const result = component.getValidationIssues(issueType);
  
        expect(result).toEqual([]);
      });
    });

    describe('getTabTitleWithIssueCount', () => {
      it('should return title with issue count for the given issue type', () => {
        const title = 'Test Title';
        const issueType = 'Mandatory';
        const validationIssues: IssueLog[] = [
          { group: 'Mandatory', issuePath: '' },
          { group: 'Advisory', issuePath: '' }
        ];

        component.issueLogData = { validationIssues } as IssueLogData;
        const result = component.getTabTitleWithIssueCount(title, issueType);

        expect(result).toEqual('Test Title (1)');
      });

      it('should return title with zero issue count if no issues match the issue type', () => {
        const title = 'Test Title';
        const issueType = 'NonMatchingType';
        const validationIssues: IssueLog[] = [
          { group: 'Mandatory', issuePath: '' }
        ];

        component.issueLogData = { validationIssues } as IssueLogData;
        const result = component.getTabTitleWithIssueCount(title, issueType);

        expect(result).toEqual('Test Title (0)');
      });

      it('should return title with zero issue count if there are no validation issues', () => {
        const title = 'Test Title';
        const issueType = 'Mandatory';
        component.issueLogData = { validationIssues: [] } as IssueLogData;

        const result = component.getTabTitleWithIssueCount(title, issueType);

        expect(result).toEqual('Test Title (0)');
      });
    });

    describe('ngOnDestroy', () => {
      it('should unsubscribe from reportProcessIdSubject', () => {
        spyOn(component['reportProcessIdSubject'], 'unsubscribe');
        component.ngOnDestroy();
        expect(component['reportProcessIdSubject'].unsubscribe).toHaveBeenCalled();
      });
    });

    describe('onTreeViewEvents', () => {
      it('should set isFixDisabled to false and call setValidationData when event type is load', () => {
        spyOn(component, 'setValidationData');
        const event = new CustomEvent('treeviewevent', {
          detail: { type: 'load', issueLogs: mockIssueLogData.validationIssues }
        });
        component.onTreeViewEvents(event);
        
        expect(component.isFixDisabled).toBeFalse();
        expect(component.setValidationData).toHaveBeenCalledWith(event.detail.issueLogs);
      });

      it('should set isFixDisabled to true and call setValidationData when event type is not load', () => {
        spyOn(component, 'setValidationData');
        const event = new CustomEvent('treeviewevent', {
          detail: { type: 'other', issueLogs: mockIssueLogData.validationIssues }
        });
        component.onTreeViewEvents(event);
        
        expect(component.isFixDisabled).toBeTrue();
        expect(component.setValidationData).toHaveBeenCalledWith(event.detail.issueLogs);
      });

      it('should call setValidationData with an empty array if issueLogs is not provided', () => {
        spyOn(component, 'setValidationData');
        const event = new CustomEvent('treeviewevent', {
          detail: { type: 'load' }
        });
        component.onTreeViewEvents(event);
        
        expect(component.setValidationData).toHaveBeenCalledWith([]);
      });
    });

    describe('setValidationData', () => {
      it('should initialize issueLogData if it is not defined', () => {
        component.issueLogData = undefined;
        const issues: IssueLog[] = [];
        
        component.setValidationData(issues);
        
        expect(component.issueLogData).toBeDefined();
        expect(component.issueLogData.validationIssues).toEqual([]);
        expect(component.issueLogData.advisoryValidationIssuesLogCount).toBe(0);
        expect(component.issueLogData.mandatoryValidationIssuesLogCount).toBe(0);
      });
    
      it('should set validationIssues and counts correctly', () => {
        component.issueLogData = { validationIssues: [], advisoryValidationIssuesLogCount: 0, mandatoryValidationIssuesLogCount: 0 };
        const issues: IssueLog[] = [
          { category: 'Advisory', group: 'DataScreen', issueTitle: 'Advisory Issue', issuePath: '' },
          { category: 'Mandatory', group: 'DataScreen', issueTitle: 'Mandatory Issue', issuePath: '' },
          { category: 'Advisory', group: 'DataScreen', issueTitle: 'Another Advisory Issue', issuePath: '' }
        ];
    
        component.setValidationData(issues);
    
        expect(component.issueLogData.validationIssues.length).toBe(3);
        expect(component.issueLogData.advisoryValidationIssuesLogCount).toBe(2);
        expect(component.issueLogData.mandatoryValidationIssuesLogCount).toBe(1);
      });
    
      it('should call setIssuePath with the provided issues', () => {
        const issues: IssueLog[] = [{ category: 'Advisory', group: 'DataScreen', issueTitle: 'Advisory Issue', issuePath: '' }];
        spyOn(component, 'setIssuePath').and.callThrough();
    
        component.setValidationData(issues);
    
        expect(component.setIssuePath).toHaveBeenCalledWith(issues);
      });
    });

    describe('setIssuePath', () => {
      it('should replace placeholders in issuePath with actual values', () => {
        const issues: IssueLog[] = [
          { issuePath: 'https://{Environment}/accounts-production/periods/{ClientId}/{PeriodId}/source-data' } as IssueLog,
          { issuePath: 'https://{Environment}/client-management/client/{ClientId}/information' } as IssueLog
        ];
        
        component.reportData.clientId = 'test-client-id';
        component.periodId = 'test-period-id';
    
        const result = component.setIssuePath(issues);
    
        expect(result[0].issuePath).toBe('/accounts-production/periods/test-client-id/test-period-id/source-data');
        expect(result[1].issuePath).toBe('/client-management/client/test-client-id/information');
      });
    
      it('should return the same issuePath if no placeholders are present', () => {
        const issues: IssueLog[] = [
          { issuePath: '/path/to/resource' } as IssueLog,
          { issuePath: '/another/path' } as IssueLog
        ];
    
        const result = component.setIssuePath(issues);
    
        expect(result[0].issuePath).toBe('/path/to/resource');
        expect(result[1].issuePath).toBe('/another/path');
      });
    
      it('should handle empty issuePath', () => {
        const issues: IssueLog[] = [{ issuePath: '' } as IssueLog ];
        const result = component.setIssuePath(issues);
    
        expect(result[0].issuePath).toBe('');
      });
    });

  });
});
