import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';
import { ReportData, ReportStatus, ValidationIssue } from 'src/app/models/report.model';
import { IssueLog, IssueLogData } from 'src/app/models/dynamic-issuelog.model';
import { DYNAMIC_ISSUELOG_TABS, IssueLogTab } from 'src/app/models/issue-log-header-details.model';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-dynamic-issuelog',
  templateUrl: './dynamic-issuelog.component.html',
  styleUrls: ['./dynamic-issuelog.component.scss']
})
export class DynamicIssueLogComponent implements OnInit, OnDestroy {
  @Input() reportData: ReportData;
  @Input() reportStatus: ReportStatus;
  @Input() periodId: string;
  @Input() hasBanner: boolean = false;
  @Output() showIssuesEvent: EventEmitter<boolean> = new EventEmitter<boolean>();
  showIssueLog: boolean;
  displayableValidationIssues: ValidationIssue[];
  statuses = ReportStatus;
  private reportProcessIdSubject: Subscription;
  tabs: IssueLogTab[] = DYNAMIC_ISSUELOG_TABS;
  activeTab: string = 'Data screens';
  issueLogData: IssueLogData;
  isFixDisabled = true;

  constructor( private readonly accountsBuilderService: AccountsBuilderService ) {
    window.addEventListener('treeviewevent', event => this.onTreeViewEvents(event));
  }

  ngOnInit(): void {
    this.showIssueLog = false;
    this.reportProcessIdSubject = this.accountsBuilderService.reportStatusSubject.subscribe(status => {
      if (status === ReportStatus.IN_PROGRESS) {
        this.showIssueLog = false;
      }
    });
  }

  onClick(): void {
    this.showIssueLog = !this.showIssueLog;
    this.showIssuesEvent.emit(this.showIssueLog);
  }

  shouldShowChevron() {
    return this.issueLogData?.validationIssues.length > 0 && this.reportStatus === ReportStatus.SUCCESS;
  }

  shouldShowExpandedIssueLog() {
    return this.showIssueLog && this.reportStatus !== ReportStatus.IN_PROGRESS;
  }

  ngOnDestroy(): void {
    this.reportProcessIdSubject.unsubscribe();
  }

  getValidationIssues(issueType: string): IssueLog[] {
    return this.issueLogData?.validationIssues.filter(issueLog => issueType.toLocaleLowerCase() === (issueLog.group.toLocaleLowerCase())) || [];
  }

  getTabTitleWithIssueCount(title:string, issueType: string): string {
    const issueCount = this.getValidationIssues(issueType).length;
    return `${title} (${issueCount})`;
  }

  onTreeViewEvents(event: Event): void {
    const detail = (event as CustomEvent)?.detail;
    this.isFixDisabled = detail?.type !== 'load';
    this.setValidationData(detail?.issueLogs || []);
  }

  setValidationData(issues: IssueLog[]): void {
    if (!this.issueLogData) {
      this.issueLogData = { validationIssues: [], advisoryValidationIssuesLogCount: 0, mandatoryValidationIssuesLogCount: 0 };
    }

    this.issueLogData.validationIssues = this.setIssuePath(issues);
    this.issueLogData.advisoryValidationIssuesLogCount = issues.filter(issue => issue.category === 'Advisory').length;
    this.issueLogData.mandatoryValidationIssuesLogCount = issues.filter(issue => issue.category === 'Mandatory').length;
  }

  setIssuePath(issues: IssueLog[]): IssueLog[] {
    issues = issues.map(issue => {
      issue.issuePath = issue.issuePath.replace(/https:\/\/{Environment}/i, '');
      issue.issuePath = issue.issuePath.replace(/{ClientId}/i, this.reportData.clientId);
      issue.issuePath = issue.issuePath.replace(/{PeriodId}/i, this.periodId);
      return issue;
    });
    return issues;
  }
}
