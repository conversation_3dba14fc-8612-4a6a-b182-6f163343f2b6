import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ActivatedRoute, convertToParamMap } from '@angular/router';
import { BehaviorSubject, of } from 'rxjs';
import {
  BusinessSubTypes,
  BusinessTypes,
  ClientDetails,
  ClientDetailsReport,
  ClientInvolvement,
  ProcessedClientInvolvement
} from 'src/app/models/client.model';
import { ClientService } from 'src/app/services/client.service';
import { PeriodsService } from 'src/app/services/periods.service';
import { ReportNotesService } from 'src/app/services/report-notes.service';

import { AccountsBuilderComponent } from './accounts-builder.component';
import { MOCK_BOOKMARK_DATA } from '../../models/bookmark.mock';
import { AccountsBuilderService } from 'src/app/services/accounts-builder.service';
import { MOCK_REPORT_DATA } from 'src/app/models/report.mock';
import { ReportNotesData } from 'src/app/models/report-sections/report-notes.model';
import { EntitySetup, ReportStatus } from 'src/app/models/report.model';

describe('AccountsBuilderComponent', () => {
  let component: AccountsBuilderComponent;
  let fixture: ComponentFixture<AccountsBuilderComponent>;
  const periodData = {
    periodId: '8a2817c0-19d7-11ec-9621-0242ac130002',
    previousPeriodId: '********-1111-1111-1111-************'
  };
  const periodsServiceMock = jasmine.createSpyObj('PeriodsService', [
    'getSourceData',
    'getPeriodData',
    'getEntitySetup'
  ]);
  const clientServiceMock = jasmine.createSpyObj('ClientService', [
    'getClientDetails',
    'getClientInvolvements',
    'getProfitShare'
  ]);
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', ['getReportData']);

  const MOCK_CLIENT_DETAILS: ClientDetails = {
    id: 'clientUUID',
    businessType: BusinessTypes.Limited,
    businessSubType: BusinessSubTypes.None,
    countryRegIn: 'countryRegIn',
    limitedCompanyType: 'limitedCompanyType'
  };

  const MOCK_ENTITY_SETUP: EntitySetup = {
    reportingStandard: 'string',
    entitySize: 'string',
    independentReviewType: 'string',
    tradingStatus: 'string',
    dormantStatus: 'string',
    terminology: 'string',
    choiceOfStatement: 'string'
  };

  const MOCK_CLIENT_DETAILS_REPORT: ClientDetailsReport = {
    businessTypeName: 'Limited Company',
    businessType: BusinessTypes.Limited,
    businessSubType: BusinessSubTypes.None,
    countryRegIn: 'countryRegIn',
    clientUUID: 'clientUUID',
    limitedCompanyType: 'limitedCompanyType'
  };

  const reportNotesMock: ReportNotesData = {
    previousPeriodId: '3',
    averageNumberOfEmployees: {
      currentPeriod: 3,
      previousPeriod: 5
    },
    offBalanceSheetArrangements: 'off-balance mock',
    advancesCreditAndGuaranteesGrantedToDirectors: 'advances mock',
    advancesCreditAndGuaranteesGrantedToDirectorsExtended: {
      guarantees: 'guarantees',
      items: [
        {
          index: 1,
          involvementClientGuid: 'abcd-abcd',
          directorName: 'John',
          balanceOutstandingAtStartOfYear: 232,
          amountsAdvanced: 22,
          amountsRepaid: 22,
          amountsWrittenOff: 22,
          amountsWaived: 22,
          balanceOutstandingAtEndOfYear: 22,
          advanceCreditConditions: 'advanceCreditConditions'
        }
      ]
    },
    guaranteesAndOtherFinancialCommitments: 'guarantees mock',
    membersLiabilityText: 'members liability mock',
    additionalNote1: {
      noteTitle: 'title 1 mock',
      noteText: 'paragraph 1 mock'
    },
    additionalNote2: {
      noteTitle: 'title 2 mock',
      noteText: 'paragraph 2 mock'
    },
    controllingPartyNote: 'ctrl party mock',
    relatedPartyTransactions: 'related party transaction mock',
    operatingProfitLoss: {
      isEnabled: true,
      items: [
        {
          description: 'operating profit',
          value: 123,
          index: 1
        }
      ]
    },
    intangibleAssetsRevaluation: 'intangible assets revaluation mock',
    tangibleFixedAssetsNotes: {
      valuationInCurrentReportingPeriod: {
        valuationDetails: 'valuationdetails',
        independentValuerInvolved: false,
        revaluationBasis: 'revaluationbasis',
        dateOfRevaluation: '2024-02-12'
      },
      historicalCostBreakdown: {
        revaluedAssetClass: 'historicDetails',
        revaluedClassPronoun: 'revaluedClassPronoun',
        currentReportingPeriodCost: 1,
        currentReportingPeriodAccumulatedDepreciation: 2
      },
      analysisOfCostOrValuation: {
        costLandAndBuildings: 1,
        costPlantAndMachineryEtc: 2,
        totalLandAndBuildings: 4,
        totalPlantAndMachineryEtc: 5,
        analysisOfCostOrValuationItems: [
          {
            index: 1,
            year: 2004,
            landAndBuildings: 1,
            plantAndMachineryEtc: 1
          },
          {
            index: 2,
            year: 2005,
            landAndBuildings: 2,
            plantAndMachineryEtc: 2
          }
        ]
      }
    }
  };

  const MOCK_PROFIT_SHARE = {
    totalProfitLossShare: 0
  };

  const MOCK_DIRECTORS: ClientInvolvement[] = [
    {
      id: 123,
      involvedClientGuid: '118be4e3-71cf-461a-81e4-8105171a2846',
      clientName: 'JOHN',
      involvementType: 'DIRECTOR',
      from: null,
      to: null
    },
    {
      id: 123,
      involvedClientGuid: 'f4c4c0b6-2ad9-4c5c-ab58-1f497ef78200',
      clientName: 'JOHN',
      involvementType: 'DIRECTOR',
      from: null,
      to: null
    }
  ];

  const MOCK_PROCESSED_CLIENT_INVOLVEMENT: ProcessedClientInvolvement = {
    directors: MOCK_DIRECTORS,
    secretaries: [],
    partners: [],
    proprietors: []
  };

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [AccountsBuilderComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: convertToParamMap({ periodId: 123, clientId: 456 })
            }
          }
        },
        {
          provide: PeriodsService,
          useValue: periodsServiceMock
        },
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        },
        {
          provide: ClientService,
          useValue: clientServiceMock
        },
        {
          provide: ReportNotesService,
          useValue: reportNotesMock
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    periodsServiceMock.getSourceData.and.returnValue(of(1));
    periodsServiceMock.getPeriodData.and.returnValue(of(periodData));
    periodsServiceMock.getEntitySetup.and.returnValue(of(MOCK_ENTITY_SETUP));
    accountsBuilderServiceMock.getReportData.and.returnValue(of(MOCK_REPORT_DATA));
    clientServiceMock.getClientDetails.and.returnValue(of(MOCK_CLIENT_DETAILS));
    clientServiceMock.getClientInvolvements.and.returnValue(of(MOCK_PROCESSED_CLIENT_INVOLVEMENT));
    clientServiceMock.getProfitShare.and.returnValue(of(MOCK_PROFIT_SHARE));
    accountsBuilderServiceMock.reportData = new BehaviorSubject(null);
    accountsBuilderServiceMock.reportStatusSubject = new BehaviorSubject(null);
    fixture = TestBed.createComponent(AccountsBuilderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('updateIsLoading', () => {
    it('should set isLoading to false', () => {
      component.isLoading = true;
      component.updateIsLoading(false);

      expect(component.isLoading).toBeFalse();
    });

    it('should set isLoading to true', () => {
      component.isLoading = false;
      component.updateIsLoading(true);

      expect(component.isLoading).toBeTrue();
    });
  });

  describe('getSourceData', () => {
    it('should set hasSourceData to true', () => {
      component.hasSourceData = false;
      component.ngOnInit();

      expect(component.hasSourceData).toBeTrue();
    });

    it('should set hasSourceData to false', () => {
      periodsServiceMock.getSourceData.and.returnValue(of(0));
      component.hasSourceData = true;
      component.ngOnInit();

      expect(component.hasSourceData).toBeFalse();
    });
  });

  it('getPeriodData should set periodUUID value', () => {
    component.periodUUID = null;
    component.ngOnInit();
    expect(component.periodUUID).toEqual(periodData.periodId);
  });

  it('getPeriodData should set previouPeriodUUID value', () => {
    component.previousPeriodUUID = null;
    component.ngOnInit();
    expect(component.previousPeriodUUID).toEqual(periodData.previousPeriodId);
  });

  describe('getClientDetails', () => {
    it('should set clientDetails', () => {
      const MOCK_CLIENT_DETAILS_MAPPED: ClientDetailsReport = {
        ...MOCK_CLIENT_DETAILS_REPORT
      };
      component.ngOnInit();
      expect(component.clientDetailsReport).toEqual(MOCK_CLIENT_DETAILS_MAPPED);
    });

    it('should set businessType to unknown', () => {
      clientServiceMock.getClientDetails.and.returnValue(
        of({ ...MOCK_CLIENT_DETAILS, businessType: BusinessTypes.Limited })
      );
      component.ngOnInit();
      expect(component.clientDetailsReport.businessType).toEqual(BusinessTypes.Limited);
    });

    it('should set countryRegIn to unknown', () => {
      clientServiceMock.getClientDetails.and.returnValue(of({ ...MOCK_CLIENT_DETAILS, countryRegIn: null }));
      component.ngOnInit();
      expect(component.clientDetailsReport.countryRegIn).toEqual(component['unknownDetail']);
    });
  });

  describe('updateBookmarkSections', () => {
    it('should update the value for bookmarkList', () => {
      component.updateBookmarkSections(MOCK_BOOKMARK_DATA);
      expect(component.bookmarkList).toEqual(MOCK_BOOKMARK_DATA);
    });
  });

  describe('updateReportStatus', () => {
    it('should call event bus with report state generated when report generated successfully and no current report data', () => {
      component.isReportGenerated = false;
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.SUCCESS);
      expect(component.isReportGenerated).toBeTrue();
    });

    it('should not call event bus with report state generated when report generation failed', () => {
      component.isReportGenerated = false;
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.FAILED);
      expect(component.isReportGenerated).toBeFalse();
    });

    it('should not call event bus with report state generated when report generated successfully but current report data found', () => {
      component.isReportGenerated = true;
      accountsBuilderServiceMock.reportStatusSubject.next(ReportStatus.SUCCESS);
      expect(component.isReportGenerated).toBeTrue();
    });

    it('should not call event bus with report state generated when report is not generated', () => {
      component.isReportGenerated = false;
      accountsBuilderServiceMock.getReportData.and.returnValue(of({ lastSuccessfullTimeUtc: '0001-01-01T00:00:00Z' }));
      component.ngOnInit();
      expect(component.isReportGenerated).toBeFalse();
    });

    it('should call event bus with report state generated when report is generated', () => {
      component.isReportGenerated = false;
      accountsBuilderServiceMock.getReportData.and.returnValue(of({ lastSuccessfullTimeUtc: '2024-01-01T00:00:00Z' }));
      component.ngOnInit();
      expect(component.isReportGenerated).toBeTrue();
    });
  });
});
