import Eev from 'eev';
import { setInternalEventBus } from './index';

describe('setInternalEventBus', () => {
  let internalEventBus: any;

  beforeEach(() => {
    internalEventBus = null;
  });

  it('should set the internal event bus', () => {
    const mockEventBus = {} as Eev;
    setInternalEventBus(mockEventBus);
    expect(internalEventBus).toEqual(null);
  });
  it('should not set the internal event bus if value is null or undefined', () => {
    internalEventBus = {};
    setInternalEventBus(null);
    expect(internalEventBus).not.toEqual(null);
    setInternalEventBus(undefined);
    expect(internalEventBus).not.toEqual(undefined);
  });
});
