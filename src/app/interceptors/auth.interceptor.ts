import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';
import { userUtil } from '../utils/userUtil';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHand<PERSON>) {
    const user = userUtil.get();
    const authRequest = req.clone({
      setHeaders: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + user.accessToken,
        PlatformTenantId: user?.userProfile?.platformTenantId
      }
    });

    return next.handle(authRequest);
  }
}
