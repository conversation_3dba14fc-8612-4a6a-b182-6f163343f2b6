import { CommaDelimiterPipe } from './comma-delimiter.pipe';

describe('CommaDelimiterPipe', () => {
  it('create an instance', () => {
    const pipe = new CommaDelimiterPipe();
    expect(pipe).toBeTruthy();
  });

  it('should add a comma delimiter to any number have more than 3 digits', () => {
    const MOCK_VALUE = 1234;
    const pipe = new CommaDelimiterPipe();
    expect(pipe.transform(MOCK_VALUE)).toBe('1,234');
  });

  it('should NOT add a comma delimiter to any number have less than 3 digits', () => {
    const MOCK_VALUE = 456;
    const pipe = new CommaDelimiterPipe();
    expect(pipe.transform(MOCK_VALUE)).toBe('456');
  });
});
