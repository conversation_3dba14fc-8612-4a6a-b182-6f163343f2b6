import { Component, OnInit, OnD<PERSON>roy, Inject } from '@angular/core';
import { userUtil } from './utils/userUtil';
import { singleSpaPropsSubject, SingleSpaProps } from 'src/single-spa/single-spa-props';
import { Subscription } from 'rxjs';
import { setEnvironment } from '../environments/environment';
import { setFeatures } from 'src/features';
import { ReportViewer2Service } from '@iris/accountsproduction-builder-report-viewer';
import { setInternalEventBus } from './event-bus';

@Component({
  selector: 'elements-accounts-builder-component-v01-pkg-root',
  templateUrl: './app.component.html',
  styleUrls: []
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'elements-accounts-builder-component-v01-pkg';
  singleSpaProps: SingleSpaProps = null;
  subscription: Subscription = null;

  constructor(private reportViewerService: ReportViewer2Service) {}

  ngOnInit() {
    // set userProfile in store from localStorage
    const data = userUtil.get();
    if (data) {
      data.selectedTenant = {};
      data.selectedTenant.id = data.userProfile.platformTenantId;
    }

    this.subscription = singleSpaPropsSubject.subscribe((props: any) => {
      setEnvironment(props.environment);
      if (props.features) {
        setFeatures(props.features);
      }
      this.reportViewerService.setEnvironment(props.environment);
      if (props.eventBus) {
        setInternalEventBus(props.eventBus);
      }
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
