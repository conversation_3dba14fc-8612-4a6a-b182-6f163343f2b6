import { TestBed } from '@angular/core/testing';
import { directorsReportSection } from '../models/report-sections/report-sections.model';

import { DirectorsReportService } from './directors-report.service';

describe('DirectorsReportService', () => {
  let service: DirectorsReportService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(DirectorsReportService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('generateSubsectionStructure', () => {
    it('should return the directors report section as an observable', () => {
      const EXPECTED_LABEL = directorsReportSection.label;
      const subsectionStructure = service.generateSubsectionsStructure();
      subsectionStructure.subscribe(section => {
        expect(section.label).toBe(EXPECTED_LABEL);
      });
    });
  });
});
