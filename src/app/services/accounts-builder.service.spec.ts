import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import {
  MOCK_GENERATE_REPORT_PAYLOAD,
  MOCK_GET_REPORT_STATUS_RESPONSE,
  MOCK_REPORT_DATA,
  VALIDATION_DATA
} from '../models/report.mock';
import { AccountsBuilderService } from './accounts-builder.service';
import {
  ReportStatus,
  GenerateReportPayload,
  ReportingStandards,
  ReportingStandardVersion
} from '../models/report.model';
import { BusinessTypes } from '../models/client.model';
describe('AccountsBuilderService', () => {
  let service: AccountsBuilderService;
  let httpClient: HttpClientTestingModule;
  const CLIENT_UUID = '8a2817c0-19d7-11ec-9621-0242ac130002';
  const PERIOD_UUID = '8a2817c0-19d7-11ec-9621-0242ac130002';

  const GET_REPORT_DATA = { data: { ...MOCK_REPORT_DATA, validationData: { ...VALIDATION_DATA } } };
  const RESPONSE_MAPPINGS = {
    triggerGenerateReport: of(null),
    getReportStatus: of(MOCK_GET_REPORT_STATUS_RESPONSE),
    getReportData: of(GET_REPORT_DATA)
  };

  const createHttpClient = (type: string) => {
    return {
      get: () => RESPONSE_MAPPINGS[type] ?? throwError('error'),
      post: () => RESPONSE_MAPPINGS[type] ?? throwError('error')
    };
  };

  const httpClientMock = jasmine.createSpyObj('HttpClient', ['post', 'put', 'get']);

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule]
    });
    httpClientMock.post.and.returnValue(of([]));
    httpClientMock.put.and.returnValue(of([]));
    httpClientMock.get.and.returnValue(of([]));
    service = new AccountsBuilderService(httpClientMock);
  });

  it('should be created', () => {
    const generateReportSpy = spyOn(service, 'triggerGenerateReport').and.returnValue(of());
    expect(service).toBeTruthy();
    service.generateReportPayload = null;
    service.triggerGenerateReportSubject.next();
    expect(generateReportSpy).not.toHaveBeenCalled();
  });

  it('should emit in progress first when generate the report', () => {
    const generateReportSpy = spyOn(service, 'triggerGenerateReport').and.returnValue(of());
    const reportStatusSpy = spyOn(service.reportStatusSubject, 'next');
    service.setGenerateReportPayload(MOCK_GENERATE_REPORT_PAYLOAD);
    service.triggerGenerateReportSubject.next();
    expect(generateReportSpy).toHaveBeenCalledWith(MOCK_GENERATE_REPORT_PAYLOAD.clientId, MOCK_GENERATE_REPORT_PAYLOAD);
    expect(reportStatusSpy).toHaveBeenCalledWith(ReportStatus.IN_PROGRESS);
  });

  it('should emit null as processId when the generation fails', () => {
    const generateReportSpy = spyOn(service, 'triggerGenerateReport').and.returnValue(throwError({}));
    const reportStatusSpy = spyOn(service.reportStatusSubject, 'next');
    service.setGenerateReportPayload(MOCK_GENERATE_REPORT_PAYLOAD);
    service.triggerGenerateReportSubject.next();
    expect(generateReportSpy).toHaveBeenCalledWith(MOCK_GENERATE_REPORT_PAYLOAD.clientId, MOCK_GENERATE_REPORT_PAYLOAD);
    expect(reportStatusSpy).toHaveBeenCalledWith(ReportStatus.FAILED);
  });

  describe('triggerGenerateReport', () => {
    it('should return the correct response', (done: DoneFn) => {
      httpClient = createHttpClient('triggerGenerateReport');
      service = new AccountsBuilderService(httpClient as HttpClient);

      service.triggerGenerateReport(CLIENT_UUID, MOCK_GENERATE_REPORT_PAYLOAD).subscribe(res => {
        expect(res).toBeNull();
        done();
      });
    });

    it('should generate correct payload with IFRS type for IFRS reporting standard', (done: DoneFn) => {
      const ifrsReportingStandard = {
        id: '6846bc1dce3611951c712788',
        name: 'IFRS - Additional Note 1',
        description: 'IFRS - Additional Note 1',
        display: false,
        reportingStandard: ReportingStandards.IFRS,
        reportType: ReportingStandardVersion.Full,
        compatibleCompanyTypes: [BusinessTypes.Limited],
        compatibleCompanySubTypes: null,
        correlationId: null,
        equivalentCorrelationId: null
      };

      const ifrsGenerateReportPayload: GenerateReportPayload = {
        clientId: 'f080e369-1098-4e6d-b6d3-8fd5bbfe89bf',
        businessType: 'Limited',
        periodId: 'd2fce502-f8c3-46ed-b17f-6ccb429cab48',
        previousPeriodId: '********-0cb3-450d-93d2-2dcbee004cfb',
        reportingStandard: ifrsReportingStandard
      };

      const postSpy = jasmine.createSpy('post').and.returnValue(of(null));
      const httpClientSpy = { post: postSpy };
      service = new AccountsBuilderService(httpClientSpy as unknown as HttpClient);

      service.triggerGenerateReport(CLIENT_UUID, ifrsGenerateReportPayload).subscribe(() => {
        expect(postSpy).toHaveBeenCalled();
        const capturedPayload = postSpy.calls.mostRecent().args[1];
        expect(capturedPayload.clientId).toBe('f080e369-1098-4e6d-b6d3-8fd5bbfe89bf');
        expect(capturedPayload.periodId).toBe('d2fce502-f8c3-46ed-b17f-6ccb429cab48');
        expect(capturedPayload.previousPeriodId).toBe('********-0cb3-450d-93d2-2dcbee004cfb');

        expect(capturedPayload.reportingStandard.id).toBe('6846bc1dce3611951c712788');
        expect(capturedPayload.reportingStandard.name).toBe('IFRS - Additional Note 1');
        expect(capturedPayload.reportingStandard.type).toBe('IFRS');
        expect(capturedPayload.reportingStandard.version).toBe(ReportingStandardVersion.Full);

        done();
      });
    });
  });

  describe('getReportData', () => {
    it('should return the correct response', (done: DoneFn) => {
      httpClient = createHttpClient('getReportData');
      service = new AccountsBuilderService(httpClient as HttpClient);
      const expectedResponse = { ...GET_REPORT_DATA.data };

      service.getReportData(CLIENT_UUID, PERIOD_UUID).subscribe(res => {
        expect(res).toEqual(expectedResponse);
        expect(res.validationData.advisoryValidationIssuesLogCount).toEqual(2);
        expect(res.validationData.mandatoryValidationIssuesLogCount).toEqual(1);
        done();
      });
    });
  });

  describe('getPreviousReportData', () => {
    it('should return the correct response', (done: DoneFn) => {
      httpClient = createHttpClient('getReportData');
      service = new AccountsBuilderService(httpClient as HttpClient);
      const expectedResponse = { ...GET_REPORT_DATA.data };

      service.getPreviousReportData(CLIENT_UUID, PERIOD_UUID).subscribe(res => {
        expect(res).toEqual(expectedResponse);
        expect(res.validationData.advisoryValidationIssuesLogCount).toEqual(2);
        expect(res.validationData.mandatoryValidationIssuesLogCount).toEqual(1);
        done();
      });
    });
  });

  describe('setGenerateReportPayload', () => {
    it('should update generateReportPayload with the new values', () => {
      service.setGenerateReportPayload(MOCK_GENERATE_REPORT_PAYLOAD);
      expect(service.generateReportPayload).toBe(MOCK_GENERATE_REPORT_PAYLOAD);
    });
  });
});
