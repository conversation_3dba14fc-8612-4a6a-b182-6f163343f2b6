import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { directorsReportSection, ReportSection } from '../models/report-sections/report-sections.model';

@Injectable({
  providedIn: 'root'
})
export class DirectorsReportService {
  generateSubsectionsStructure(): Observable<ReportSection> {
    const directorsReport = { ...directorsReportSection };
    return of(directorsReport);
  }
}
