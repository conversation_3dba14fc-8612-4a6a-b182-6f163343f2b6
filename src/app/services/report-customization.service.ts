import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { BehaviorSubject, forkJoin, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { Bookmark } from '../models/bookmark.model';
import { NotesFormTypeEnum } from '../models/report-sections/report-notes.model';
import {
  ReportSection,
  customizableReportSection,
  directorsReportSection
} from '../models/report-sections/report-sections.model';
import { ReportData, ReportingStandards, ReportNoteSection, ReportType } from '../models/report.model';
import { reportStandardHelper } from '../utils/report-standard-helper';
import { AccountingPoliciesService } from './accounting-policies.service';
import { AccountsBuilderService } from './accounts-builder.service';
import { DirectorsReportService } from './directors-report.service';
import { ReportNotesService } from './report-notes.service';
import { ReportSectionsService } from './report-sections.service';
import { ReportBuilderService } from './report-builder.service';
import { BusinessTypes } from '../models/client.model';
import { accountingPoliciesSection, balanceSheetSection } from '../models/report-sections/accounting-policies.model';

@Injectable({
  providedIn: 'root'
})
export class ReportCustomizationService implements OnDestroy {
  currentSectionNode: ReportSection = null;
  reportData: ReportData = null;

  reportSectionsTree = new BehaviorSubject<ReportSection>(null);
  $subscriptions: Subscription[] = [];

  constructor(
    private reportSectionsService: ReportSectionsService,
    private reportNotesService: ReportNotesService,
    private accountsBuilderService: AccountsBuilderService,
    private accountingPoliciesService: AccountingPoliciesService,
    private directorsReportService: DirectorsReportService,
    private reportBuilderService: ReportBuilderService
  ) {
    this.reportSectionsTree?.next(this.generateReportSectionsRoot());
    this.$subscriptions.push(
      this.accountsBuilderService.reportData.subscribe(newReportData => {
        this.reportData = newReportData;
        if (this.reportData?.reportingStandard) {
          this.reportBuilderService.getReportingStandards().subscribe(s => {
            this.reportData.reportingStandard = s.find(x => x.id === this.reportData.reportingStandard.id);
          });
        }
      })
    );

    this.$subscriptions.push(
      this.reportSectionsService.bookmarkList.subscribe(bookmarks => {
        if (bookmarks?.length && this.reportData !== null) {
          // When the bookmarks are being generated, the initialization for the sections tree is triggered, updating the sidebar with the new bookmarks
          this.initSectionsTree(bookmarks, this.reportData);
        } else {
          this.reportSectionsTree?.next(this.generateReportSectionsRoot());
        }
      })
    );
  }

  public generateReportSectionsRoot(): ReportSection {
    // The report sections root is the default state of the sidebar, where de report is not generated or there are no sections
    // This can be extended with the sections (and the configuration for each one) existent in the selected report type
    const reportTypeRoot = {
      parent: null,
      label: 'Generator',
      isMandatory: true,
      formConfigKey: null,
      children: [],
      errorCount: 0,
      warningCount: 0
    };

    reportTypeRoot.children.push({
      parent: reportTypeRoot,
      label: 'Sections',
      isMandatory: true,
      formConfigKey: null,
      children: null,
      errorCount: 0,
      warningCount: 0
    });

    return reportTypeRoot;
  }

  initSectionsTree(bookmarks: Bookmark[], reportData: ReportData): void {
    const sectionsRoot = this.generateReportSectionsRoot();
    const reportType = reportData?.reportingStandard?.reportingStandard;
    if (!reportType) return;
    this.currentSectionNode = sectionsRoot.children[0];

    let sectionsMapping: ReportSection[] = bookmarks.map(bookmark => {
      return {
        label: bookmark.Text,
        navigationRef: bookmark,
        parent: this.currentSectionNode,
        children: null,
        isMandatory: true,
        errorCount: 0,
        warningCount: 0
      };
    });

    this.addReportSpecificSections(sectionsMapping, reportType, bookmarks, sectionsRoot, reportData);
  }

  public addReportSpecificSections(
    sectionsMapping: ReportSection[],
    reportType: ReportingStandards,
    bookmarks: Bookmark[],
    sectionsRoot: ReportSection,
    reportData: ReportData
  ) {
    switch (reportType) {
      case ReportingStandards.FRS105:
        if (this.bookmarksIncludesSection(bookmarks, customizableReportSection)) {
          this.reportNotesService
            .generateSubsectionsStructure()
            .pipe(
              map(reportNotesSection => {
                const reportTypeNoteSection = this.getReportTypeNoteSection();
                this.filterReportNoteSubSectionByFormType(
                  reportNotesSection,
                  reportTypeNoteSection.notesToFinancialStatements
                );
                return reportNotesSection;
              })
            )
            .subscribe(customSection => {
              sectionsMapping = this.replaceOrAddSection(customSection, sectionsMapping);

              this.currentSectionNode.children = sectionsMapping;

              this.reportNotesService.initSubsectionsValidity(reportData.validationData);
              this.reportSectionsTree?.next(sectionsRoot);
            });
        } else {
          this.reportNotesService.generateSubsectionsStructure().subscribe(customSection => {
            sectionsMapping = this.replaceOrAddSection(customSection, sectionsMapping);

            this.currentSectionNode.children = sectionsMapping;

            this.reportNotesService.initSubsectionsValidity(reportData.validationData);
            this.reportSectionsTree?.next(sectionsRoot);
          });
        }
        break;
      case ReportingStandards.FRS102_1A:
        forkJoin([
          this.directorsReportService.generateSubsectionsStructure(),
          this.accountingPoliciesService.generateSubsectionsStructure(),
          this.accountingPoliciesService.generateBalanceSheetStructure(),
          this.reportNotesService.generateSubsectionsStructure()
        ])
          .pipe(
            map(
              ([directorsNotesSection, accountingNotesSection, accountingBalanceNotesSection, reportNotesSection]) => {
                return this.pushSections(
                  bookmarks,
                  directorsNotesSection,
                  accountingNotesSection,
                  accountingBalanceNotesSection,
                  reportNotesSection
                );
              }
            )
          )
          .subscribe(reportSections => {
            reportSections.forEach(section => {
              sectionsMapping = this.replaceOrAddSection(section, sectionsMapping);
            });
            this.currentSectionNode.children = sectionsMapping;
            this.reportNotesService.initSubsectionsValidity(reportData.validationData);
            this.accountingPoliciesService.initSubsectionsValidity(reportData.validationData);
            this.reportSectionsTree?.next(sectionsRoot);
          });
        break;
      case ReportingStandards.FRS102:
        this.currentSectionNode.children = sectionsMapping;
        this.reportSectionsTree?.next(sectionsRoot);
        break;

      case ReportingStandards.Unincorporated:
        this.reportNotesService.generateSubsectionsStructure().subscribe(customSection => {
          this.currentSectionNode.children = sectionsMapping;
          this.reportSectionsTree?.next(sectionsRoot);
        });
        break;
      case ReportingStandards.Charity:
        this.currentSectionNode.children = sectionsMapping;
        this.reportSectionsTree?.next(sectionsRoot);
        break;
      case ReportingStandards.IFRS:
        this.currentSectionNode.children = sectionsMapping;
        this.reportSectionsTree?.next(sectionsRoot);
        break;
    }
  }

  private pushSections(
    bookmarks: Bookmark[],
    directorsNotesSection: ReportSection,
    accountingNotesSection: ReportSection,
    accountingBalanceNotesSection: ReportSection,
    reportNotesSection: ReportSection
  ): any[] {
    let sectionArray = [];
    const reportTypeNoteSection = this.getReportTypeNoteSection();

    if (this.bookmarksIncludesSection(bookmarks, directorsReportSection)) {
      this.filterReportNoteSubSectionByFormType(directorsNotesSection, reportTypeNoteSection.directorsReports);
      sectionArray.push(directorsNotesSection);
    }

    if (this.bookmarksIncludesSection(bookmarks, accountingPoliciesSection)) {
      this.filterReportNoteSubSectionByFormType(accountingNotesSection, reportTypeNoteSection.accountingPolicies);
      sectionArray.push(accountingNotesSection);
    }

    if (this.bookmarksIncludesSection(bookmarks, balanceSheetSection)) {
      sectionArray.push(accountingBalanceNotesSection);
    }

    if (this.bookmarksIncludesSection(bookmarks, customizableReportSection)) {
      this.filterReportNoteSubSectionByFormType(reportNotesSection, reportTypeNoteSection.notesToFinancialStatements);
      sectionArray.push(reportNotesSection);
    }

    return sectionArray;
  }

  private bookmarksIncludesSection(bookmarks: Bookmark[], section: ReportSection): boolean {
    return bookmarks?.some(o => o.Text === section.label || section.label.includes(o.Text));
  }

  filterReportNoteSubSectionByFormType(reportSection: ReportSection, notesSection: NotesFormTypeEnum[]) {
    if (reportSection?.children?.length > 0) {
      reportSection.children = this.getReportSectionsByFromType(reportSection.children, notesSection);
    }
  }

  getReportTypeNoteSection(): ReportNoteSection {
    return reportStandardHelper.getReportNoteSection(this.reportData?.reportingStandard?.reportingStandard);
  }

  private getReportSectionsByFromType(
    reportSections: ReportSection[],
    notesTypes: NotesFormTypeEnum[]
  ): ReportSection[] {
    if (!reportSections) return reportSections;

    return reportSections.reduce((sections: ReportSection[], subSection: ReportSection) => {
      subSection.children = this.getReportSectionsByFromType(subSection.children, notesTypes);
      if (this.isReportSectionValid(subSection, notesTypes)) sections.push(subSection);

      return sections;
    }, []);
  }

  private isReportSectionValid(reportSection: ReportSection, notesTypes: NotesFormTypeEnum[]): boolean {
    return (
      (!reportSection.formConfigKey && reportSection.children?.length > 0) ||
      (reportSection.formConfigKey && notesTypes?.some(s => s === reportSection.formConfigKey)) ||
      reportSection.visibile
    );
  }

  hasMatchingLabels(firstLabel: string, secondLabel: string): boolean {
    return firstLabel.trim().toLowerCase() === secondLabel.trim().toLowerCase();
  }

  removeSubscriptions() {
    this.$subscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.$subscriptions = [];
  }

  ngOnDestroy(): void {
    this.removeSubscriptions();
  }

  public replaceOrAddSection(section: ReportSection, sectionsMapping: ReportSection[]): ReportSection[] {
    if (section === null) return sectionsMapping;
    section.parent = this.currentSectionNode;
    const bookmarkedSection = sectionsMapping.find(existingSection => {
      const aliasesList = section.alias ? [section.label, ...section.alias] : [section.label];
      return aliasesList.find(alias => this.hasMatchingLabels(alias, existingSection.label));
    });
    if (bookmarkedSection) {
      bookmarkedSection.children = section.children;
      bookmarkedSection.warningCount = section.warningCount;
      bookmarkedSection.errorCount = section.errorCount;
      bookmarkedSection.formConfigKey = section.formConfigKey;
      section.children?.forEach(newChild => {
        newChild.parent = bookmarkedSection;
      });
    } else {
      sectionsMapping.push(section);
    }
    return sectionsMapping;
  }
}
