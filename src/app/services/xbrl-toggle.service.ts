import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class XbrlToggleService {
  private xbrlEditorModeToggleSubject = new Subject<[boolean, number?]>();
  xbrlEditorModeToggle$ = this.xbrlEditorModeToggleSubject.asObservable();

  emitXbrlModeChanged(xbrlModeOn: boolean = false, taxonomyId: number = null): void {
    this.xbrlEditorModeToggleSubject.next([xbrlModeOn, taxonomyId]);
    this.dispatchXbrlButtonEvent(xbrlModeOn);
    console.log(`xbrl toggle service mode on: ${xbrlModeOn}, taxonomy id: ${taxonomyId}`);
  }

  private dispatchXbrlButtonEvent(selected: boolean): void {
    let customEvent = new CustomEvent('selectedXbrlButton', {
      detail: { selectedXbrlButton: selected }
    });
    window.dispatchEvent(customEvent);
  }
}
