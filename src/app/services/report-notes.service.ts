import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EMPTY, Observable, Subject, Subscription } from 'rxjs';
import { catchError, debounceTime, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { DefaultErrorAlert } from '../models/alert.model';
import {
  ClientInvolvement,
  ProfitShare,
  ClientDetails,
  BusinessTypes,
  LimitedCompanyTypes
} from '../models/client.model';
import { AccountProductionPeriod } from '../models/period.model';
import {
  NotesFormTypeEnum,
  ReportNotesBody,
  ReportNotesData,
  ReportNotesResponse,
  StatusTypesEnum,
  ValidationIssueEnum,
  ValidationTypeEnum
} from '../models/report-sections/report-notes.model';
import {
  customizableReportSection,
  customizableReportSubsectionsFrs1021a,
  customizableReportSubsectionsFrs105,
  ReportSection,
  tangibleFixedAssetsSection
} from '../models/report-sections/report-sections.model';
import { ReportData, ReportingStandards, ReportingStandardVersion, ValidationData } from '../models/report.model';
import { AccountsBuilderService } from './accounts-builder.service';
import { AlertService } from './alert.service';
import { ClientService } from './client.service';
import { PeriodsService } from './periods.service';

@Injectable({
  providedIn: 'root'
})
export class ReportNotesService {
  private readonly AP_PERIODS_API_ENDPOINT = `${environment.accounts_production_periods_api_uri}/clients`;
  private readonly AP_PERIODS_API_KEY = { 'x-api-key': environment.accounts_production_periods_api_key };
  private triggerRefreshStatusBanner: Subject<boolean> = new Subject<boolean>();
  private reportNotesData: ReportNotesData;
  private triggerChanges: Subject<ReportNotesData> = new Subject<ReportNotesData>();

  customNode: ReportSection;
  reportData: ReportData;
  validationData: NotesFormTypeEnum[] = [];
  isCreated = false;
  directors: ClientInvolvement[] = [];
  clientDetails: ClientDetails;
  periodData: AccountProductionPeriod = null;
  profitShare: ProfitShare;
  $subscriptions: Subscription[] = [];
  limitedCompanyType: string;
  businessType: string;
  reportingStandard: string;

  public triggerRefreshStatusBanner$ = this.triggerRefreshStatusBanner.asObservable();
  public triggerChanges$: Observable<ReportNotesData> = this.triggerChanges;

  constructor(
    private alertService: AlertService,
    private accountsBuilderService: AccountsBuilderService,
    private clientService: ClientService,
    private periodsService: PeriodsService,
    private http: HttpClient
  ) {
    this.initReportNotesData();

    this.periodsService.periodData.subscribe(newPeriodData => {
      this.periodData = newPeriodData;
    });

    this.accountsBuilderService.reportData.subscribe(newReportData => {
      this.reportNotesData.previousPeriodId = newReportData?.previousPeriodId;
      this.reportData = newReportData;
    });

    this.clientService.directors.subscribe(directors => {
      this.directors = directors;
    });

    this.triggerChanges.pipe(debounceTime(1000)).subscribe(reportNotesData => {
      const url = `${this.AP_PERIODS_API_ENDPOINT}/${this.reportData.clientId}/accountperiods/${this.reportData.periodId}/notes`;
      const payload: ReportNotesData = {
        previousPeriodId: this.reportNotesData.previousPeriodId,
        ...reportNotesData
      };

      if (!this.isCreated) {
        this.saveValues(payload, url);
      } else {
        this.updateValues(payload, url);
      }
    });
  }

  tangibleFixedAssetsSection(validationData: ValidationData) {
    const isTangibleFixedAssetsVisible =
      validationData.validationIssues.findIndex(
        validationItem => validationItem.errorCode === ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_NOTES_WARN
      ) === -1;

    if (!isTangibleFixedAssetsVisible) {
      this.customNode.children = this.customNode.children.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_NOTES_WARN
      );
      return;
    }

    const nodeIndex = this.customNode.children.findIndex(
      c => c.validationIssueCode === ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_NOTES_WARN
    );
    this.customNode.children[nodeIndex].children = [];
    tangibleFixedAssetsSection.forEach((section, index) => {
      this.customNode.children[nodeIndex].children.push({
        ...section,
        parent: this.customNode.children[nodeIndex]
      });
    });

    const childNodeIndexHistoric = this.customNode.children[nodeIndex].children.findIndex(
      c => c.formConfigKey === NotesFormTypeEnum.HISTORICAL_COST_BREAKDOWN
    );
    this.customNode.children[nodeIndex].children[childNodeIndexHistoric].errorCount = this.notesSectionMissing(
      this.reportData.validationData,
      ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_HISTORICAL_COST_BREAKDOWN_ERROR
    )
      ? 1
      : 0;

    const childNodeIndexAnalysis = this.customNode.children[nodeIndex].children.findIndex(
      c => c.formConfigKey === NotesFormTypeEnum.ANALYSIS_OF_COST_VALUATION
    );
    this.customNode.children[nodeIndex].children[childNodeIndexAnalysis].errorCount = this.notesSectionMissing(
      this.reportData.validationData,
      ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_ANALYSIS_OF_COST_ERROR
    )
      ? 1
      : 0;

    if (
      this.customNode.children[nodeIndex].children[childNodeIndexHistoric].errorCount === 1 ||
      this.customNode.children[nodeIndex].children[childNodeIndexAnalysis].errorCount === 1
    ) {
      this.customNode.children[nodeIndex].errorCount = 1;
      this.customNode.errorCount = 1;
    }
  }

  generateSubsectionsStructure(): Observable<ReportSection> {
    return this.getReportNotes(this.reportData.clientId, this.reportData.periodId).pipe(
      map((response: ReportNotesResponse) => {
        this.customNode = { ...customizableReportSection };
        this.customNode.children = [];

        let customizableReportSubsections =
          this.reportData.reportingStandard?.reportingStandard === ReportingStandards.FRS102_1A
            ? customizableReportSubsectionsFrs1021a
            : customizableReportSubsectionsFrs105;

        customizableReportSubsections.forEach((section, index) => {
          this.customNode.children.push({
            ...section,
            parent: this.customNode
          });
        });

        this.filterReportChildren();

        if (this.reportData.reportingStandard?.reportingStandard === ReportingStandards.FRS102_1A) {
          this.checkOperatingNode();
          this.checkAdvancesNode();
        }
        this.tangibleFixedAssetsSection(this.reportData.validationData);
        this.updateIntangibleAssetsSection(this.reportData.validationData);

        if (response.status === StatusTypesEnum.CREATED) {
          this.isCreated = false;
          if (this.reportData.previousPeriodId) {
            this.getReportNotes(this.reportData.clientId, this.reportData.previousPeriodId).subscribe(
              (previousResponse: ReportNotesResponse) => {
                this.updateValuesFromPreviousPeriod(previousResponse);
                this.updateReportNotesDataFlags();
              }
            );
          }
        } else {
          this.isCreated = true;
          this.reportNotesData = response.body;
          this.updateReportNotesDataFlags();
        }

        return this.customNode;
      })
    );
  }

  updateIntangibleAssetsSection(validationData: ValidationData): void {
    const isIntangibleAssetsVisible =
      validationData.validationIssues.findIndex(
        validationItem => validationItem.errorCode === ValidationIssueEnum.INTANGIBLE_ASSETS_REVALUATION_WARN
      ) === -1;

    if (!isIntangibleAssetsVisible) {
      this.customNode.children = this.customNode.children.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.INTANGIBLE_ASSETS_REVALUATION_WARN
      );
    } else if (this.businessType === BusinessTypes.LLP) {
      const nodeIndex = this.customNode.children.findIndex(
        c => c.formConfigKey === NotesFormTypeEnum.INTANGIBLE_ASSETS_REVALUATION
      );
      this.customNode.children[nodeIndex].label = 'Intangible assets';
    }
  }

  initSubsectionsValidity(validationData: ValidationData): void {
    this.validationData = [];
    const advancesValidation = validationData.validationIssues.findIndex(
      validation => validation.name === ValidationTypeEnum.ADVANCES
    );
    this.customNode.children.forEach(child => {
      const keyValue = this.reportNotesData[child.formConfigKey];
      if (
        child.formConfigKey === NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES &&
        (keyValue?.currentPeriod === null || keyValue?.previousPeriod === null)
      ) {
        this.updateErrorCounts(child, true);
      }
      if (
        (child.formConfigKey === NotesFormTypeEnum.ADDITIONAL_NOTE_1 ||
          child.formConfigKey === NotesFormTypeEnum.ADDITIONAL_NOTE_2) &&
        keyValue?.noteTitle &&
        !keyValue?.noteText
      ) {
        this.updateWarningCounts(child, true);
      }
      if (child.formConfigKey === NotesFormTypeEnum.ADVANCES && advancesValidation > -1) {
        this.validationData.push(NotesFormTypeEnum.ADVANCES);
        if (!keyValue?.length) this.updateErrorCounts(child, true);
      }
    });
  }

  updateValuesFromPreviousPeriod(previousResponse: ReportNotesResponse) {
    for (const key in previousResponse.body) {
      if (previousResponse.body[key]) {
        switch (key) {
          case NotesFormTypeEnum.ADVANCES:
            this.reportNotesData[key] = null;
            break;
          case NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES:
            this.reportNotesData.averageNumberOfEmployees.previousPeriod =
              previousResponse.body.averageNumberOfEmployees.currentPeriod;
            break;
          case NotesFormTypeEnum.ADDITIONAL_NOTE_1:
          case NotesFormTypeEnum.ADDITIONAL_NOTE_2:
            this.reportNotesData[key].noteText = previousResponse.body[key].noteText;
            this.reportNotesData[key].noteTitle = previousResponse.body[key].noteTitle;
            break;
          case NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES:
            this.reportNotesData[key] = { ...previousResponse.body[key] };
            this.reportNotesData[key].valuationInCurrentReportingPeriod.dateOfRevaluation = this.periodData
              ? this.periodData.endDate.split('T')[0]
              : '';
            break;
          case NotesFormTypeEnum.ADVANCES_CREDITS:
            this.reportNotesData[key] = { ...previousResponse.body[key] };
            this.reportNotesData[key].items = [];
            previousResponse.body[key].items.forEach(e => {
              this.reportNotesData[key].items.push({
                index: e.index,
                involvementClientGuid: e.involvementClientGuid,
                directorName: e.directorName,
                balanceOutstandingAtStartOfYear: e.balanceOutstandingAtEndOfYear,
                amountsAdvanced: null,
                amountsRepaid: null,
                amountsWrittenOff: null,
                amountsWaived: null,
                balanceOutstandingAtEndOfYear: null,
                advanceCreditConditions: null
              });
            });
            break;
          case NotesFormTypeEnum.MEMBERS_LIABILITIES:
          case NotesFormTypeEnum.LOANS_DEBTS:
          default:
            this.reportNotesData[key] = previousResponse.body[key];
            break;
        }
      }
    }
  }

  updateReportNotesDataValues(
    newValues: any,
    sectionReference: ReportSection,
    hasError: boolean = false,
    hasWarning: boolean = false,
    formConfigKeyForNewValues: string = null
  ): void {
    sectionReference.hasData = this.checkValues(newValues);
    this.reportNotesData[formConfigKeyForNewValues ?? sectionReference.formConfigKey] = newValues;
    this.triggerChanges.next(this.reportNotesData);
    if ((hasError && !sectionReference.errorCount) || (!hasError && sectionReference.errorCount)) {
      this.updateErrorCounts(sectionReference, hasError);
    }
    if ((hasWarning && !sectionReference.warningCount) || (!hasWarning && sectionReference.warningCount)) {
      this.updateWarningCounts(sectionReference, hasWarning);
    }
  }

  updateErrorCounts(sectionReference: ReportSection, hasError: boolean): void {
    while (sectionReference !== null) {
      hasError ? sectionReference.errorCount++ : sectionReference.errorCount--;
      sectionReference = sectionReference.parent;
    }
  }

  updateWarningCounts(sectionReference: ReportSection, hasWarning: boolean): void {
    while (sectionReference !== null) {
      hasWarning ? sectionReference.warningCount++ : sectionReference.warningCount--;
      sectionReference = sectionReference.parent;
    }
  }

  getReportNotesData(property: string) {
    return this.reportNotesData[property];
  }

  initReportNotesData(): void {
    this.reportNotesData = {
      previousPeriodId: null,
      averageNumberOfEmployees: {
        currentPeriod: null,
        previousPeriod: null
      },
      offBalanceSheetArrangements: null,
      advancesCreditAndGuaranteesGrantedToDirectors: null,
      advancesCreditAndGuaranteesGrantedToDirectorsExtended: null,
      guaranteesAndOtherFinancialCommitments: null,
      membersLiabilityText: null,
      additionalNote1: {
        noteTitle: null,
        noteText: null
      },
      additionalNote2: {
        noteTitle: null,
        noteText: null
      },
      controllingPartyNote: null,
      relatedPartyTransactions: null,
      operatingProfitLoss: null,
      intangibleAssetsRevaluation: null,
      tangibleFixedAssetsNotes: null
    };
  }

  refreshStatusBanner(trigger: boolean) {
    this.triggerRefreshStatusBanner.next(trigger);
  }

  private notesSectionMissing(validationData: ValidationData, validationIssueEnum: ValidationIssueEnum) {
    const isNotesSectionVisible =
      validationData.validationIssues.findIndex(validationItem => validationItem.errorCode === validationIssueEnum) > 0;
    return isNotesSectionVisible;
  }

  private saveValues(payload: ReportNotesData, url: string): void {
    this.http
      .post(url, payload, { headers: this.AP_PERIODS_API_KEY })
      .pipe(
        catchError(() => {
          this.alertService.showAlert(DefaultErrorAlert);
          return EMPTY;
        })
      )
      .subscribe(() => {
        this.isCreated = true;
        this.refreshStatusBanner(true);
      });
  }

  private updateValues(payload: ReportNotesData, url: string) {
    delete payload.previousPeriodId;

    this.http
      .put(url, payload, { headers: this.AP_PERIODS_API_KEY })
      .pipe(
        catchError(() => {
          this.alertService.showAlert(DefaultErrorAlert);
          return EMPTY;
        })
      )
      .subscribe(() => this.refreshStatusBanner(true));
  }

  private getReportNotes(clientId: string, periodId: string): Observable<ReportNotesResponse> {
    return this.http.get<ReportNotesBody>(
      `${this.AP_PERIODS_API_ENDPOINT}/${clientId}/accountperiods/${periodId}/notes`,
      {
        headers: this.AP_PERIODS_API_KEY,
        observe: 'response'
      }
    );
  }

  private updateReportNotesDataFlags(): void {
    this.customNode.children.forEach(child => {
      const keyValue = this.reportNotesData[child.formConfigKey];
      child.hasData = this.checkValues(keyValue);
    });
  }

  private checkValues(keyValue: any): boolean {
    if (keyValue) {
      if (typeof keyValue !== typeof {}) {
        return keyValue !== null;
      }
      return Object.keys(keyValue).some(value => {
        return keyValue[value] !== null;
      });
    }

    return false;
  }

  hasSectionValidation(formConfigKey: string): boolean {
    return this.validationData.findIndex(validation => validation === formConfigKey) > -1;
  }

  private filterReportChildren() {
    if (
      this.businessType !== BusinessTypes.LLP ||
      this.reportData.reportingStandard.reportingStandard !== ReportingStandards.FRS102_1A ||
      this.profitShare === null ||
      this.profitShare?.totalProfitLossShare === 0
    ) {
      this.customNode.children = this.customNode.children.filter(
        section => section.formConfigKey !== NotesFormTypeEnum.LOANS_DEBTS
      );
    }

    if (
      this.businessType !== BusinessTypes.LLP &&
      this.limitedCompanyType !== LimitedCompanyTypes.LimitedByGuarantee &&
      this.limitedCompanyType !== LimitedCompanyTypes.LimitedByGuaranteeS30Exempt
    ) {
      this.customNode.children = this.customNode.children.filter(
        section => section.formConfigKey !== NotesFormTypeEnum.MEMBERS_LIABILITIES
      );
    }

    if (this.businessType === BusinessTypes.LLP) {
      this.customNode.children = this.customNode.children.filter(
        section =>
          section.formConfigKey !== NotesFormTypeEnum.ADVANCES &&
          section.formConfigKey !== NotesFormTypeEnum.MEMBERS_LIABILITIES
      );
    }
  }

  private checkOperatingNode() {
    if (this.reportData.reportingStandard.reportType === ReportingStandardVersion.Filleted) {
      this.customNode.children = this.customNode.children.filter(
        section => section.formConfigKey !== NotesFormTypeEnum.OPERATING_PROFIT_LOSS
      );
    }
  }

  private checkAdvancesNode() {
    if (this.businessType !== BusinessTypes.LLP) {
      const nodeIndex = this.customNode.children.findIndex(c => c.formConfigKey === NotesFormTypeEnum.ADVANCES);
      this.customNode.children[nodeIndex].children = [];
      this.customNode.children[nodeIndex].formConfigKey = null;
      this.customNode.children[nodeIndex].visibile = true;
      this.customNode.children[nodeIndex].errorCount = this.notesSectionMissing(
        this.reportData.validationData,
        ValidationIssueEnum.ADVANCES_CREDIT_AND_GUARANTEES_TO_DIRECTORS_ERROR
      )
        ? 1
        : 0;
      if (this.customNode.children[nodeIndex].errorCount === 1) {
        this.customNode.errorCount = this.customNode.children[nodeIndex].errorCount;
      }
      this.directors?.forEach((section, index) => {
        this.customNode.children[nodeIndex].children.push({
          ...this.customNode.children[nodeIndex],
          children: null,
          hasData: false,
          formConfigKey: NotesFormTypeEnum.ADVANCES_CREDITS,
          label: section.clientName,
          errorCount: 0,
          director: {
            index: index + 1,
            involvementClientGuid: section.involvedClientGuid,
            directorName: section.clientName
          },
          parent: this.customNode.children[nodeIndex]
        });
      });

      if (this.customNode.children[nodeIndex]?.children?.length === 1) {
        this.customNode.children[nodeIndex].label = 'Advances, credits and guarantees granted the Director';
      }
    }
  }
}
