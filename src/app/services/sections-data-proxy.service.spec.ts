import { TestBed } from '@angular/core/testing';
import { DataSourceServiceEnum, NotesFormTypeEnum } from '../models/report-sections/report-notes.model';
import { ReportSection } from '../models/report-sections/report-sections.model';

import { SectionsDataProxyService } from './sections-data-proxy.service';

describe('SectionsDataProxyService', () => {
  let service: SectionsDataProxyService;

  const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
    'getReportNotesData',
    'hasSectionValidation',
    'updateReportNotesDataValues'
  ]);

  const accountingPoliciesServiceMock = jasmine.createSpyObj('accountingPoliciesService', [
    'getCustomizationData',
    'updateCustomizationDataValues'
  ]);

  const MOCK_SECTION_REF: ReportSection = {
    parent: null,
    children: null,
    label: 'mock label',
    isMandatory: false,
    errorCount: 0,
    warningCount: 0,
    formConfigKey: NotesFormTypeEnum.ADVANCES,
    dataSourceService: DataSourceServiceEnum.NOTES
  };
  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = new SectionsDataProxyService(reportNotesServiceMock, accountingPoliciesServiceMock);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getFormData', () => {
    it('should get the data from the notes service', () => {
      service.getFormData(MOCK_SECTION_REF.dataSourceService, MOCK_SECTION_REF.formConfigKey);
      expect(reportNotesServiceMock.getReportNotesData).toHaveBeenCalledWith(MOCK_SECTION_REF.formConfigKey);
    });

    it('should get the data from the accounting policies service', () => {
      const MOCK_ACC_POL = {
        ...MOCK_SECTION_REF,
        dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES
      };
      service.getFormData(MOCK_ACC_POL.dataSourceService, MOCK_ACC_POL.formConfigKey);
      expect(accountingPoliciesServiceMock.getCustomizationData).toHaveBeenCalledWith(MOCK_ACC_POL.formConfigKey);
    });
  });

  describe('getFormValidation', () => {
    it('should get the validation from the notes service', () => {
      service.getFormValidation(MOCK_SECTION_REF.dataSourceService, MOCK_SECTION_REF.formConfigKey);
      expect(reportNotesServiceMock.hasSectionValidation).toHaveBeenCalledWith(MOCK_SECTION_REF.formConfigKey);
    });

    it('should return false for validation coming from accounting policies service', () => {
      const MOCK_ACC_POL = {
        ...MOCK_SECTION_REF,
        dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES
      };
      const result = service.getFormValidation(MOCK_ACC_POL.dataSourceService, MOCK_ACC_POL.formConfigKey);
      expect(result).toBe(false);
    });
  });

  describe('updateServiceData', () => {
    it('should update the values in the notes service', () => {
      const MOCK_NEW_VALUES = 'mock paragraph';
      service.updateServiceData(MOCK_NEW_VALUES, MOCK_SECTION_REF, false, false);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        MOCK_NEW_VALUES,
        MOCK_SECTION_REF,
        false,
        false
      );
    });

    it('should update the values for the members liability data screen', () => {
      const MOCK_NEW_VALUES = 'mock paragraph';
      const MOCK_LIABILITY_REF = {
        parent: null,
        children: null,
        label: 'mock label',
        isMandatory: false,
        errorCount: 0,
        warningCount: 0,
        formConfigKey: 'membersLiabilityText',
        dataSourceService: DataSourceServiceEnum.NOTES
      };

      service.updateServiceData(MOCK_NEW_VALUES, MOCK_LIABILITY_REF, false, false);
      expect(reportNotesServiceMock.updateReportNotesDataValues).toHaveBeenCalledWith(
        { noteTitle: 'MembersLiabilityTitle', noteText: MOCK_NEW_VALUES },
        MOCK_LIABILITY_REF,
        false,
        false
      );
    });

    it('should update the values in the policies service', () => {
      const MOCK_NEW_VALUES = 'mock paragraph 2';
      const MOCK_ACC_POL = {
        ...MOCK_SECTION_REF,
        dataSourceService: DataSourceServiceEnum.ACCOUNTING_POLICIES
      };
      service.updateServiceData(MOCK_NEW_VALUES, MOCK_ACC_POL, false, false);
      expect(accountingPoliciesServiceMock.updateCustomizationDataValues).toHaveBeenCalledWith(
        MOCK_NEW_VALUES,
        MOCK_ACC_POL,
        false,
        false
      );
    });
  });
});
