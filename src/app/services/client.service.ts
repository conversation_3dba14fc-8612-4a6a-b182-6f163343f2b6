import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { catchError, map, tap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import {
  ClientDetails,
  ClientInvolvement,
  ClientInvolvementResponse,
  InvolvementTypeEnum,
  ProcessedClientInvolvement,
  ProfitShare
} from '../models/client.model';
import { CacheService } from './cache.service';

@Injectable({
  providedIn: 'root'
})
export class ClientService {
  private readonly AP_CLIENTS_API = `${environment.accountsproduction_api_url}/clients`;
  private readonly AP_CLIENTS_API_KEY = { 'x-api-key': environment.accountsproduction_api_key };

  private readonly AP_PERIODS_API = `${environment.accounts_production_periods_api_uri}`;
  private readonly AP_PERIODS_API_KEY = { 'x-api-key': environment.accounts_production_periods_api_key };

  private readonly AP_PROFITSHARE_API = `${environment.accountsproduction_accountperiod_partnerprofitshare_api_url}`;
  private readonly AP_PROFITSHARE_API_KEY = {
    'x-api-key': environment.accountsproduction_accountperiod_partnerprofitshare_api_key
  };

  directors = new BehaviorSubject<ClientInvolvement[]>(null);
  profitShare = new BehaviorSubject<ProfitShare>(null);
  constructor(private http: HttpClient, private cacheService: CacheService) {}

  getClientDetails(clientId: string): Observable<ClientDetails> {
    const clientDetailsRoute = `${this.AP_PERIODS_API}/clients/${clientId}`;
    return this.http.get<ClientDetails>(clientDetailsRoute, { headers: this.AP_PERIODS_API_KEY });
  }

  getProfitShare(clientId: string, periodId: number): Observable<ProfitShare> {
    const profitShareRoute = `${this.AP_PROFITSHARE_API}/clients/${clientId}/accountperiods/${periodId}/profitShare`;
    return this.http.get<ProfitShare>(profitShareRoute, { headers: this.AP_PROFITSHARE_API_KEY });
  }

  getClientInvolvements(clientUUID: string): Observable<ProcessedClientInvolvement> {
    const existingClientInvolvements = this.getClientInvolvementsFromCache(clientUUID);
    const getClientInvolvementsEP = `${this.AP_CLIENTS_API}/${clientUUID}/involvements`;
    if (existingClientInvolvements) return of(existingClientInvolvements);

    return this.http
      .get<ClientInvolvementResponse[]>(getClientInvolvementsEP, { headers: this.AP_CLIENTS_API_KEY })
      .pipe(
        map(response => this.processInvolvementResponse(response)),
        tap(data => {
          this.setClientInvolvementsToCache(clientUUID, data);
        }),
        catchError(err => {
          if (err.status === 404) {
            const emptyResponse: ProcessedClientInvolvement = {
              directors: [],
              secretaries: [],
              partners: [],
              proprietors: []
            };
            return of(emptyResponse);
          } else {
            throwError(err);
          }
        })
      );
  }

  private processInvolvementResponse(response: ClientInvolvementResponse[]): ProcessedClientInvolvement {
    const processedResponse: ProcessedClientInvolvement = {
      directors: [],
      secretaries: [],
      partners: [],
      proprietors: []
    };

    response.forEach(item => {
      const involvementType = item.involvementType?.trim().toLowerCase();
      if (!involvementType) {
        return;
      }
      const row: ClientInvolvement = {
        id: item.id,
        involvedClientGuid: item.involvedClientGuid,
        clientName: item.involvedClientName,
        involvementType: involvementType,
        from: item.startDate || null,
        to: item.endDate || null
      };
      if (involvementType === InvolvementTypeEnum.Director) processedResponse.directors.push(row);
    });
    this.directors.next(processedResponse.directors.filter(d => !d?.to));
    return processedResponse;
  }

  private getClientInvolvementsFromCache(clientUUID: string): ProcessedClientInvolvement {
    return this.cacheService.get<ProcessedClientInvolvement>(this.getClientInvolvementsCacheKey(clientUUID));
  }

  private setClientInvolvementsToCache(clientUUID: string, accountProductionPeriod: ProcessedClientInvolvement): void {
    return this.cacheService.set(this.getClientInvolvementsCacheKey(clientUUID), accountProductionPeriod);
  }

  private getClientInvolvementsCacheKey(clientUUID: string) {
    return `client_involvements_${clientUUID}`;
  }
}
