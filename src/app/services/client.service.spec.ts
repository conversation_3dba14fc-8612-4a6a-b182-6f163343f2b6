import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import {
  BusinessSubTypes,
  BusinessTypes,
  ClientInvolvement,
  ClientInvolvementResponse,
  ProcessedClientInvolvement
} from '../models/client.model';
import { CacheService } from './cache.service';

import { ClientService } from './client.service';

describe('ClientService', () => {
  let service: ClientService;
  let cacheService: CacheService;
  let httpClient: HttpClientTestingModule;

  const CLIENT = {
    id: '8a2817c0-19d7-11ec-9621-0242ac130002',
    accountsChartId: 2,
    accountsChartIdentifier: 'ELTD',
    groupStructureId: 1,
    groupStructureCode: 100,
    clientId: 'ClientID',
    name: 'Name',
    tradingName: 'XYZ',
    businessType: BusinessTypes.Limited,
    businessSubType: BusinessSubTypes.None,
    businessTypeDisplayName: 'Limited',
    limitedCompanyType: 'LimitedCompanyType',
    incorporated: '2018-03-01T00:00:00Z',
    tradingCommenced: '2018-03-01T00:00:00Z',
    tradingCeased: '2018-03-05T00:00:00Z',
    registeredNo: 'RegisteredNo',
    countryRegIn: 'CountryRegIn',
    companyAuthenticationCode: 'ABC123'
  };

  const MOCK_CLIENT_INVOLVEMENT_RESPONSE: ClientInvolvementResponse[] = [
    {
      id: 123,
      involvedClientGuid: '***********',
      involvedClientType: 'secretary',
      involvedClientName: 'testname',
      involvementType: 'secretary',
      startDate: '12/12/12',
      endDate: '12/12/13'
    },
    {
      id: 1234,
      involvedClientGuid: '***********',
      involvedClientType: 'director',
      involvedClientName: 'testname',
      involvementType: 'director',
      startDate: '12/12/12',
      endDate: null
    }
  ];

  const RESPONSE_MAPPINGS = {
    getClientDetails: of(CLIENT),
    getClientInvolvements: of(MOCK_CLIENT_INVOLVEMENT_RESPONSE)
  };

  const createHttpClient = (type: string) => {
    return {
      get: () => RESPONSE_MAPPINGS[type] ?? throwError('error')
    };
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule]
    });
    service = TestBed.inject(ClientService);
    cacheService = TestBed.inject(CacheService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getClientDetails', () => {
    it('should return the correct response', () => {
      httpClient = createHttpClient('getClientDetails');
      service = new ClientService(httpClient as HttpClient, cacheService);
      const expectedResponse = {
        id: CLIENT.id,
        accountsChartId: CLIENT.accountsChartId,
        accountsChartIdentifier: CLIENT.accountsChartIdentifier,
        groupStructureId: CLIENT.groupStructureId,
        groupStructureCode: CLIENT.groupStructureCode,
        clientId: CLIENT.clientId,
        name: CLIENT.name,
        tradingName: CLIENT.tradingName,
        businessType: CLIENT.businessType,
        businessSubType: CLIENT.businessSubType,
        businessTypeDisplayName: CLIENT.businessTypeDisplayName,
        limitedCompanyType: CLIENT.limitedCompanyType,
        registeredNo: CLIENT.registeredNo,
        incorporated: CLIENT.incorporated,
        tradingCommenced: CLIENT.tradingCommenced,
        tradingCeased: CLIENT.tradingCeased,
        countryRegIn: CLIENT.countryRegIn,
        companyAuthenticationCode: CLIENT.companyAuthenticationCode
      };

      service.getClientDetails('123').subscribe(res => {
        expect(res).toEqual(expectedResponse);
      });
    });
  });

  describe('getClientInvolvements', () => {
    it('should return the correct response', () => {
      httpClient = createHttpClient('getClientInvolvements');
      service = new ClientService(httpClient as HttpClient, cacheService);

      const directorInvolvement = MOCK_CLIENT_INVOLVEMENT_RESPONSE[1];
      const director: ClientInvolvement = {
        id: directorInvolvement.id,
        involvedClientGuid: directorInvolvement.involvedClientGuid,
        clientName: directorInvolvement.involvedClientName,
        involvementType: directorInvolvement.involvementType,
        from: directorInvolvement.startDate,
        to: directorInvolvement.endDate
      };

      const secretarieInvolvement = MOCK_CLIENT_INVOLVEMENT_RESPONSE[0];
      const secretary: ClientInvolvement = {
        id: secretarieInvolvement.id,
        involvedClientGuid: secretarieInvolvement.involvedClientGuid,
        clientName: secretarieInvolvement.involvedClientName,
        involvementType: secretarieInvolvement.involvementType,
        from: secretarieInvolvement.startDate,
        to: secretarieInvolvement.endDate
      };

      const expectedResponse: ProcessedClientInvolvement = {
        directors: [director],
        secretaries: [],
        partners: [],
        proprietors: []
      };

      service.getClientInvolvements('123').subscribe(res => {
        expect(res).toEqual(expectedResponse);
      });
    });
  });
});
