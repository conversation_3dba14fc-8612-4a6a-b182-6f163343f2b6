import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReportBuilderService } from './report-builder.service';
import { TestBed } from '@angular/core/testing';
import {
  ReportingStandard,
  ReportingStandardVersion,
  ReportingStandardVersionName,
  ReportingStandards,
  ReportingTemplate
} from '../models/report.model';
import { of, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { availableReportTemplates } from '../utils/available-report-template-helper';

describe('BuilderService', () => {
  let service: ReportBuilderService;
  let httpClient: HttpClientTestingModule;

  const reportingTemplates: ReportingTemplate[] = [
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e965',
      name: 'FRS102 1A - Full',
      description: '(Display) FRS102 1A - Full $$LTDS1AFUL001$$',
      isPublic: false,
      type: 'FRS102 1A',
      version: ReportingStandardVersionName.Full
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e966',
      name: 'FRS102 - Full HMRC',
      description: 'FRS102 - Full HMRC v25.4.0.1 $$LTD102FUL001$$',
      isPublic: false,
      type: 'FRS102',
      version: ReportingStandardVersionName.Full
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e963',
      name: 'FRS105 - Filleted',
      description: '(display) this description $$LTD105SFC001$$',
      isPublic: true,
      type: 'FRS105',
      version: ReportingStandardVersionName.Filleted
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e962',
      name: 'FRS105 - Full',
      description: 'this description (Display) $$LTD105FUL001$$',
      isPublic: true,
      type: 'FRS105',
      version: ReportingStandardVersionName.Full
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e964',
      name: 'FRS105 - Balances sheet',
      description: 'FRS105 - random Filleted report one that is not in available template configuration',
      isPublic: false,
      type: 'FRS105',
      version: ReportingStandardVersionName.Filleted
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e968',
      name: 'FRS102 1A - Full',
      description: '(Display) FRS102 1A - Full',
      isPublic: false,
      type: 'FRS102 1A',
      version: ReportingStandardVersionName.Full
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e969',
      name: 'FRS102 - Full HMRC',
      description: 'FRS102 - Full HMRC v25.4.0.1',
      isPublic: false,
      type: 'FRS102',
      version: ReportingStandardVersionName.Full
    },
    {
      id: '486021cf-d6bb-4d76-829f-4f8f3706e967',
      name: 'Unincorporated - Detailed Profit and Loss',
      description: 'Unincorporated - Filleted - Detailed Profit and Loss',
      isPublic: false,
      type: 'Unincorporated',
      version: ReportingStandardVersionName.Filleted
    },
    {
      id: '6846bc1dce3611951c712788',
      name: 'IFRS - Additional Note 1',
      description: 'IFRS - Additional Note 1',
      isPublic: false,
      type: 'IFRS',
      version: ReportingStandardVersionName.Full
    },
    {
      id: '6846bc1dce3611951c712789',
      name: 'IFRS - Full Co Hse',
      description: '(Display) IFRS - Full Co Hse $$LTDIASSFC001$$',
      isPublic: true,
      type: 'IFRS',
      version: ReportingStandardVersionName.Full
    }
  ];

  const RESPONSE_MAPPINGS = {
    getReportingStandards: of(reportingTemplates)
  };

  const createHttpClient = (type: string) => {
    return {
      get: () => RESPONSE_MAPPINGS[type] ?? throwError('error')
    };
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule]
    });
    service = TestBed.inject(ReportBuilderService);
  });

  describe('getReportingStandards', () => {
    it('should return the correct response', () => {
      httpClient = createHttpClient('getReportingStandards');
      service = new ReportBuilderService(httpClient as HttpClient);
      const expectedResponse: ReportingStandard[] = [
        availableReportTemplates.find(o => o.correlationId === 'LTDS1AFUL001'),
        availableReportTemplates.find(o => o.correlationId === 'LTD102FUL001'),
        availableReportTemplates.find(o => o.correlationId === 'LTD105SFC001'),
        availableReportTemplates.find(o => o.correlationId === 'LTD105FUL001'),
        {
          id: '486021cf-d6bb-4d76-829f-4f8f3706e964',
          name: 'FRS105 - Balances sheet',
          description: 'FRS105 - random Filleted report one that is not in available template configuration',
          display: false,
          reportingStandard: ReportingStandards.FRS105,
          reportType: ReportingStandardVersion.Filleted,
          compatibleCompanyTypes: null,
          compatibleCompanySubTypes: null,
          correlationId: null,
          equivalentCorrelationId: null
        },
        {
          id: '486021cf-d6bb-4d76-829f-4f8f3706e968',
          name: 'FRS102 1A - Full',
          description: '(Display) FRS102 1A - Full',
          display: true,
          reportingStandard: ReportingStandards.FRS102_1A,
          reportType: ReportingStandardVersion.Full,
          compatibleCompanyTypes: null,
          compatibleCompanySubTypes: null,
          correlationId: null,
          equivalentCorrelationId: null
        },
        {
          id: '486021cf-d6bb-4d76-829f-4f8f3706e969',
          name: 'FRS102 - Full HMRC',
          description: 'FRS102 - Full HMRC v25.4.0.1',
          display: false,
          reportingStandard: ReportingStandards.FRS102,
          reportType: ReportingStandardVersion.Full,
          compatibleCompanyTypes: null,
          compatibleCompanySubTypes: null,
          correlationId: null,
          equivalentCorrelationId: null
        },
        {
          id: '486021cf-d6bb-4d76-829f-4f8f3706e967',
          name: 'Unincorporated - Detailed Profit and Loss',
          description: 'Unincorporated - Filleted - Detailed Profit and Loss',
          display: false,
          reportingStandard: ReportingStandards.Unincorporated,
          reportType: ReportingStandardVersion.Filleted,
          compatibleCompanyTypes: null,
          compatibleCompanySubTypes: null,
          correlationId: null,
          equivalentCorrelationId: null
        },
        {
          id: '6846bc1dce3611951c712788',
          name: 'IFRS - Additional Note 1',
          description: 'IFRS - Additional Note 1',
          display: false,
          reportingStandard: ReportingStandards.IFRS,
          reportType: ReportingStandardVersion.Full,
          compatibleCompanyTypes: null,
          compatibleCompanySubTypes: null,
          correlationId: null,
          equivalentCorrelationId: null
        },
        availableReportTemplates.find(o => o.correlationId === 'LTDIASSFC001')
      ];
      service.getReportingStandards().subscribe(res => {
        expect(res).toEqual(expectedResponse);
      });
    });

    it('should correctly identify IFRS reporting standards from description', () => {
      const ifrsTemplates: ReportingTemplate[] = [
        {
          id: '6846bc1dce3611951c712788',
          name: 'IFRS - Additional Note 1',
          description: 'IFRS - Additional Note 1',
          isPublic: false,
          type: 'IFRS',
          version: ReportingStandardVersionName.Full
        },
        {
          id: '6846bc1dce3611951c712789',
          name: 'IFRS - Full Co Hse',
          description: '(Display) IFRS - Full Co Hse $$LTDIASSFC001$$',
          isPublic: true,
          type: 'IFRS',
          version: ReportingStandardVersionName.Full
        }
      ];

      const httpClientMock = {
        get: () => of(ifrsTemplates)
      };

      service = new ReportBuilderService(httpClientMock as unknown as HttpClient);

      service.getReportingStandards().subscribe(res => {
        const ifrsAdditionalNote = res.find(r => r.name === 'IFRS - Additional Note 1');
        const ifrsFullCoHse = res.find(r => r.name === 'IFRS - Full Co Hse');

        expect(ifrsAdditionalNote).toBeDefined();
        expect(ifrsAdditionalNote.reportingStandard).toBe(ReportingStandards.IFRS);
        expect(ifrsAdditionalNote.id).toBe('6846bc1dce3611951c712788');

        expect(ifrsFullCoHse).toBeDefined();
        expect(ifrsFullCoHse.reportingStandard).toBe(ReportingStandards.IFRS);
        expect(ifrsFullCoHse.id).toBe('6846bc1dce3611951c712789');
      });
    });
  });
});
