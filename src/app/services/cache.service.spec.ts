import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { AccountProductionPeriod } from 'src/app/models/period.model';
import { CacheService } from './cache.service';

describe('CacheServiceSpecs', () => {
  let cacheService: CacheService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule]
    });
    cacheService = TestBed.inject(CacheService);
  });
  describe('Given a cache service', () => {
    it('should return undefined if id is not found in cache', () => {
      const cachedData = cacheService.get<AccountProductionPeriod>('2');
      expect(cachedData).toBe(undefined);
    });

    it('should return data if id is found in cache', () => {
      cacheService.set<AccountProductionPeriod>('1', {
        clientUuid: '123',
        endDate: '01/01/2020',
        id: 2,
        periodId: '1234',
        status: { id: 1, name: 'status' }
      });
      const cachedData = cacheService.get<AccountProductionPeriod>('1');
      expect(cachedData).not.toBe(undefined);
    });
  });
});
