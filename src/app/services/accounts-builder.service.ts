import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, EMPTY, Observable, Subject } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import * as moment from 'moment';
import {
  GenerateReportPayload,
  ReportData,
  ReportStatus,
  TriggerGenerateReportResponse,
  ValidationIssueCategory,
  ReportingStandardsABTypes
} from '../models/report.model';

@Injectable({
  providedIn: 'root'
})
export class AccountsBuilderService {
  triggerGenerateReportSubject = new Subject();
  reportStatusSubject = new Subject<string>();
  reportData = new BehaviorSubject<ReportData>(null);

  generateReportPayload: GenerateReportPayload = null;

  constructor(private http: HttpClient) {
    this.triggerGenerateReportSubject.subscribe(() => {
      if (this.generateReportPayload) {
        this.reportStatusSubject.next(ReportStatus.IN_PROGRESS);
        this.triggerGenerateReport(this.generateReportPayload.clientId, this.generateReportPayload)
          .pipe(
            catchError(() => {
              this.reportStatusSubject.next(ReportStatus.FAILED);
              return EMPTY;
            })
          )
          .subscribe(() => this.reportStatusSubject.next(ReportStatus.SUCCESS));
      }
    });
  }

  triggerGenerateReport(clientUUID: string, generateReportPayload: GenerateReportPayload) {
    const generateReportDto = {
      clientId: generateReportPayload.clientId,
      periodId: generateReportPayload.periodId,
      previousPeriodId: generateReportPayload.previousPeriodId,
      reportingStandard: {
        id: generateReportPayload.reportingStandard.id,
        name: generateReportPayload.reportingStandard.name,
        type: ReportingStandardsABTypes[generateReportPayload.reportingStandard.reportingStandard],
        version: generateReportPayload.reportingStandard.reportType
      }
    };
    return this.http.post(
      `${environment.accounts_production_accounts_builder_api_uri}/clients/${clientUUID}/reports`,
      generateReportDto,
      {
        responseType: 'text',
        headers: {
          'x-api-key': environment.accounts_production_accounts_builder_api_key
        }
      }
    );
  }

  getReportData(clientUUID: string, periodUUID: string): Observable<ReportData> {
    return this.http
      .get<TriggerGenerateReportResponse>(
        `${environment.accounts_production_accounts_builder_api_uri}/clients/${clientUUID}/reports/${periodUUID}`,
        {
          headers: {
            'x-api-key': environment.accounts_production_accounts_builder_api_key
          }
        }
      )
      .pipe(
        map(response => {
          this.populateValidationIssueCount(response.data);
          this.reportData.next(response.data);
          return response.data;
        })
      );
  }

  getPreviousReportData(clientUUID: string, periodUUID: string): Observable<ReportData> {
    return this.http
      .get<TriggerGenerateReportResponse>(
        `${environment.accounts_production_accounts_builder_api_uri}/clients/${clientUUID}/reports/${periodUUID}`,
        {
          headers: {
            'x-api-key': environment.accounts_production_accounts_builder_api_key
          }
        }
      )
      .pipe(
        map(response => {
          this.populateValidationIssueCount(response.data);
          return response.data;
        })
      );
  }

  setGenerateReportPayload(newPayload: GenerateReportPayload): void {
    this.generateReportPayload = newPayload;
  }

  populateValidationIssueCount(reportData: ReportData) {
    reportData.validationData.mandatoryValidationIssuesLogCount = reportData.validationData.validationIssues.filter(
      v => v.errorCategory == ValidationIssueCategory.Mandatory
    ).length;
    reportData.validationData.advisoryValidationIssuesLogCount = reportData.validationData.validationIssues.filter(
      v => v.errorCategory == ValidationIssueCategory.Advisory
    ).length;
  }
}
