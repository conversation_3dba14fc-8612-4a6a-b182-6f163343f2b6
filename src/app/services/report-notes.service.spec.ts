import { BehaviorSubject, of, throwError } from 'rxjs';

import { ReportNotesService } from './report-notes.service';
import {
  NotesFormTypeEnum,
  ReportNotesData,
  ReportNotesResponse,
  ValidationIssueEnum
} from '../models/report-sections/report-notes.model';
import { fakeAsync, tick } from '@angular/core/testing';
import { ReportSection } from '../models/report-sections/report-sections.model';
import { MOCK_REPORT_DATA, MOCK_REPORT_STANDARDS } from '../models/report.mock';
import { BusinessTypes, ClientInvolvement, ProfitShare } from '../models/client.model';
import {
  ReportingStandardVersion,
  ValidationData,
  ValidationIssueCategory,
  ValidationIssueTarget,
  ValidationIssueType
} from '../models/report.model';

describe('ReportNotesService', () => {
  let service: ReportNotesService;
  const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
    'triggerGenerateReport',
    'getReportStatus',
    'getReportData'
  ]);
  const clientServiceMock = jasmine.createSpyObj('ClientService', ['getClientDetails', 'getClientInvolvements']);
  const httpClientMock = jasmine.createSpyObj('HttpClient', ['post', 'put', 'get']);

  const alertServiceMock = jasmine.createSpyObj('AlertService', ['showAlert']);

  const periodsServiceMock = jasmine.createSpyObj('PeriodsService', [
    'getSourceData',
    'getPeriodData',
    'getEntitySetup'
  ]);

  const periodDataMock = {
    clientUuid: '9a2881c0-19d7-11ec-9621-0242ac141115',
    id: 1224,
    periodId: '8a2817c0-19d7-11ec-9621-0242ac130002',
    previousPeriodId: '********-1111-1111-1111-********1111',
    endDate: '2022-12-31'
  };

  const MOCK_DIRECTORS: ClientInvolvement[] = [
    {
      id: 123,
      involvedClientGuid: '118be4e3-71cf-461a-81e4-8105171a2846',
      clientName: 'JOHN',
      involvementType: 'DIRECTOR',
      from: null,
      to: null
    },
    {
      id: 123,
      involvedClientGuid: 'f4c4c0b6-2ad9-4c5c-ab58-1f497ef78200',
      clientName: 'JOHN',
      involvementType: 'DIRECTOR',
      from: null,
      to: null
    }
  ];

  const MOCK_DIRECTOR: ClientInvolvement[] = [
    {
      id: 123,
      involvedClientGuid: '118be4e3-71cf-461a-81e4-8105171a2846',
      clientName: 'JOHN',
      involvementType: 'DIRECTOR',
      from: null,
      to: null
    }
  ];

  const MOCK_VALIDATION_DATA: ValidationData = {
    advisoryValidationIssuesLogCount: 0,
    mandatoryValidationIssuesLogCount: 0,
    validationIssues: [
      {
        errorCode: ValidationIssueEnum.IMPROVEMENTS_PROPERTY_ERROR,
        description: 'mock desc',
        errorCategory: ValidationIssueCategory.Mandatory,
        name: 'mock name',
        type: ValidationIssueType.Missing,
        target: ValidationIssueTarget.IssueLog
      }
    ]
  };

  const reportNotesMock: ReportNotesData = {
    previousPeriodId: '3',
    averageNumberOfEmployees: {
      currentPeriod: 3,
      previousPeriod: 5
    },
    offBalanceSheetArrangements: 'off-balance mock',
    advancesCreditAndGuaranteesGrantedToDirectors: 'advances mock',
    advancesCreditAndGuaranteesGrantedToDirectorsExtended: {
      guarantees: 'guarantees',
      items: [
        {
          index: 1,
          involvementClientGuid: 'abcd-abcd',
          directorName: 'John',
          balanceOutstandingAtStartOfYear: 232,
          amountsAdvanced: 22,
          amountsRepaid: 22,
          amountsWrittenOff: 22,
          amountsWaived: 22,
          balanceOutstandingAtEndOfYear: 22,
          advanceCreditConditions: 'advanceCreditConditions'
        }
      ]
    },
    guaranteesAndOtherFinancialCommitments: 'guarantees mock',
    membersLiabilityText: 'members liability mock',
    additionalNote1: {
      noteTitle: 'title 1 mock',
      noteText: 'paragraph 1 mock'
    },
    additionalNote2: {
      noteTitle: 'title 2 mock',
      noteText: 'paragraph 2 mock'
    },
    controllingPartyNote: 'ctrl party mock',
    relatedPartyTransactions: 'related party transaction mock',
    operatingProfitLoss: {
      isEnabled: true,
      items: [
        {
          description: 'operating profit',
          value: 123,
          index: 1
        }
      ]
    },
    intangibleAssetsRevaluation: 'intangible assets revaluation mock',
    tangibleFixedAssetsNotes: {
      valuationInCurrentReportingPeriod: {
        valuationDetails: 'valuationdetails',
        independentValuerInvolved: false,
        revaluationBasis: 'revaluationbasis',
        dateOfRevaluation: '2024-02-12'
      },
      historicalCostBreakdown: {
        revaluedAssetClass: 'historicDetails',
        revaluedClassPronoun: 'revaluedClassPronoun',
        currentReportingPeriodCost: 1,
        currentReportingPeriodAccumulatedDepreciation: 2
      },
      analysisOfCostOrValuation: {
        costLandAndBuildings: 1,
        costPlantAndMachineryEtc: 2,
        totalLandAndBuildings: 4,
        totalPlantAndMachineryEtc: 5,
        analysisOfCostOrValuationItems: [
          {
            index: 1,
            year: 2004,
            landAndBuildings: 1,
            plantAndMachineryEtc: 1
          },
          {
            index: 2,
            year: 2005,
            landAndBuildings: 2,
            plantAndMachineryEtc: 2
          }
        ]
      }
    }
  };

  const profitShare: ProfitShare = {
    id: 1,
    accountPeriodId: 1,
    totalProfitLossShare: 1,
    clientId: '1',
    tenantId: '1',
    partnerPeriods: [
      {
        id: 1,
        startDateUtc: 'string',
        endDateUtc: 'string',
        periodProfitLossShare: 1,
        isPercentageSelected: true,
        shareAllocations: [
          {
            id: 1,
            sharePercentage: 1,
            shareAmount: 1,
            involvementId: 1
          }
        ]
      }
    ]
  };

  const avgNumberMock = {
    averageNumberOfEmployees: reportNotesMock.averageNumberOfEmployees
  };

  const MOCK_SECTION_REF = {
    parent: null,
    children: null,
    isMandatory: false,
    hasData: false,
    formConfigKey: NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES,
    director: {
      index: 1,
      involvementClientGuid: '321-123-456',
      directorName: 'john'
    },
    label: 'mock section',
    errorCount: 0,
    warningCount: 0
  };

  const reportNotesResponseMock: ReportNotesResponse = {
    body: reportNotesMock,
    status: 204
  };

  beforeEach(() => {
    httpClientMock.post.and.returnValue(of([]));
    httpClientMock.put.and.returnValue(of([]));
    const reportDataMock = {
      previousPeriodId: reportNotesMock.previousPeriodId,
      clientId: 'clientId',
      periodId: 'periodId',
      validationData: {
        validationIssues: []
      },
      reportType: MOCK_REPORT_STANDARDS[0]
    };
    accountsBuilderServiceMock.reportData = new BehaviorSubject(reportDataMock);
    clientServiceMock.directors = new BehaviorSubject(MOCK_DIRECTORS);
    periodsServiceMock.periodData = new BehaviorSubject(periodDataMock);

    service = new ReportNotesService(
      alertServiceMock,
      accountsBuilderServiceMock,
      clientServiceMock,
      periodsServiceMock,
      httpClientMock
    );
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    accountsBuilderServiceMock.reportData.next(null);
    clientServiceMock.directors.next(null);
    expect(service.reportData).toBe(null);
  });

  it('should display an alert when the save call fails', fakeAsync(() => {
    service.isCreated = false;
    httpClientMock.post.and.returnValue(throwError({ status: 404 }));
    service.updateReportNotesDataValues(reportNotesMock.averageNumberOfEmployees, MOCK_SECTION_REF);
    tick(1000);
    expect(alertServiceMock.showAlert).toHaveBeenCalled();
  }));

  it('should display an alert when the update call fails', fakeAsync(() => {
    service.isCreated = true;
    httpClientMock.put.and.returnValue(throwError({ status: 404 }));
    service.updateReportNotesDataValues(reportNotesMock.averageNumberOfEmployees, MOCK_SECTION_REF);
    tick(1000);
    expect(alertServiceMock.showAlert).toHaveBeenCalled();
  }));

  it('should trigger the refresh status banner when the save call succeeds', fakeAsync(() => {
    const refreshStatusBannerSpy = spyOn(service, 'refreshStatusBanner');
    service.isCreated = false;
    service.updateReportNotesDataValues(reportNotesMock.averageNumberOfEmployees, MOCK_SECTION_REF);
    tick(1000);
    expect(service.isCreated).toBe(true);
    expect(refreshStatusBannerSpy).toHaveBeenCalledWith(true);
  }));

  describe('initReportNotesData', () => {
    it('should generate the initial data structure', () => {
      service.initReportNotesData();
      const avgNumberOfEmployees = service.getReportNotesData(MOCK_SECTION_REF.formConfigKey);
      const currentPeriod = avgNumberOfEmployees.currentPeriod;
      const previousPeriod = avgNumberOfEmployees.previousPeriod;

      expect(previousPeriod).toBe(null);
      expect(currentPeriod).toBe(null);
    });
  });

  describe('updateValuesFromPreviousPeriod', () => {
    it('should copy notes from previous period', () => {
      service.updateValuesFromPreviousPeriod(reportNotesResponseMock);

      expect(service.getReportNotesData('averageNumberOfEmployees').previousPeriod).toEqual(
        reportNotesMock.averageNumberOfEmployees.currentPeriod
      );
      expect(service.getReportNotesData('advancesCreditAndGuaranteesGrantedToDirectors')).toBeNull();
      expect(service.getReportNotesData('offBalanceSheetArrangements')).toEqual(
        reportNotesMock.offBalanceSheetArrangements
      );
      expect(service.getReportNotesData('additionalNote1').noteTitle).toEqual(
        reportNotesMock.additionalNote1.noteTitle
      );
      expect(service.getReportNotesData('additionalNote1').noteText).toEqual(reportNotesMock.additionalNote1.noteText);
      expect(service.getReportNotesData('operatingProfitLoss')).toEqual(reportNotesMock.operatingProfitLoss);
    });

    it('should set valuationInCurrentReportingPeriod.dateOfRevaluation default value as period end date', () => {
      service.updateValuesFromPreviousPeriod(reportNotesResponseMock);
      expect(
        service.getReportNotesData('tangibleFixedAssetsNotes').valuationInCurrentReportingPeriod.dateOfRevaluation
      ).toEqual(periodDataMock.endDate);
    });
  });

  describe('generateSubsectionsStructure', () => {
    it('should have all the sections generated in order for full frs102_1a', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[2];
      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customNode.children.length).toBe(11);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Average number of employees')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Operating profit/(loss)')
        ).toBeGreaterThanOrEqual(1);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Intangible assets - Revaluations')
        ).toBeGreaterThanOrEqual(2);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(3);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Related party transactions')
        ).toBeGreaterThanOrEqual(4);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Guarantees and other financial commitments'
          )
        ).toBeGreaterThanOrEqual(5);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Off-balance sheet arrangements')
        ).toBeGreaterThanOrEqual(6);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Advances, credits and guarantees granted to Directors'
          )
        ).toBeGreaterThanOrEqual(7);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 1')
        ).toBeGreaterThanOrEqual(8);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 2')
        ).toBeGreaterThanOrEqual(9);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Controlling party')
        ).toBeGreaterThanOrEqual(10);
      });
    });

    it('should have all the sections generated in order for full frs102_1a llp', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[2];
      service.businessType = BusinessTypes.LLP;
      // service.profitShare = profitShare;

      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customNode.children.length).toBe(11);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Average number of employees')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Operating profit/(loss)')
        ).toBeGreaterThanOrEqual(1);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Intangible assets')
        ).toBeGreaterThanOrEqual(2);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(3);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Loans and other debts due to members')
        ).toBeGreaterThanOrEqual(4);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Related party transactions')
        ).toBeGreaterThanOrEqual(5);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Guarantees and other financial commitments'
          )
        ).toBeGreaterThanOrEqual(6);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Off-balance sheet arrangements')
        ).toBeGreaterThanOrEqual(7);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 1')
        ).toBeGreaterThanOrEqual(8);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 2')
        ).toBeGreaterThanOrEqual(9);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Controlling party')
        ).toBeGreaterThanOrEqual(10);
      });
    });

    it('should have all the sections generated in order for filleted frs102_1a', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[3];
      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customNode.children.length).toBe(10);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Average number of employees')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Intangible assets - Revaluations')
        ).toBeGreaterThanOrEqual(1);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(2);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Related party transactions')
        ).toBeGreaterThanOrEqual(3);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Guarantees and other financial commitments'
          )
        ).toBeGreaterThanOrEqual(4);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Off-balance sheet arrangements')
        ).toBeGreaterThanOrEqual(5);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Advances, credits and guarantees granted to Directors'
          )
        ).toBeGreaterThanOrEqual(6);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 1')
        ).toBeGreaterThanOrEqual(7);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 2')
        ).toBeGreaterThanOrEqual(8);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Controlling party')
        ).toBeGreaterThanOrEqual(9);
      });
    });

    it('should have all the sections generated in order for filleted frs102_1a llp', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[3];
      service.businessType = BusinessTypes.LLP;
      service.profitShare = profitShare;

      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customNode.children.length).toBe(10);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Average number of employees')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Intangible assets')
        ).toBeGreaterThanOrEqual(1);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(2);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Loans and other debts due to members')
        ).toBeGreaterThanOrEqual(3);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Related party transactions')
        ).toBeGreaterThanOrEqual(4);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Guarantees and other financial commitments'
          )
        ).toBeGreaterThanOrEqual(5);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Off-balance sheet arrangements')
        ).toBeGreaterThanOrEqual(6);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 1')
        ).toBeGreaterThanOrEqual(7);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 2')
        ).toBeGreaterThanOrEqual(8);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Controlling party')
        ).toBeGreaterThanOrEqual(9);
      });
    });

    it('should have all the sections generated in order for full frs105', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[0];
      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customNode.children.length).toBe(11);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Off-balance sheet arrangements')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Average number of employees')
        ).toBeGreaterThanOrEqual(1);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Operating profit/(loss)')
        ).toBeGreaterThanOrEqual(2);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Intangible assets - Revaluations')
        ).toBeGreaterThanOrEqual(3);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(4);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Guarantees and other financial commitments'
          )
        ).toBeGreaterThanOrEqual(5);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Related party transactions')
        ).toBeGreaterThanOrEqual(6);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Advances, credits and guarantees granted to Directors'
          )
        ).toBeGreaterThanOrEqual(7);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 1')
        ).toBeGreaterThanOrEqual(8);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 2')
        ).toBeGreaterThanOrEqual(9);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Controlling party')
        ).toBeGreaterThanOrEqual(10);
      });
    });

    it('should have all the sections generated in order for full frs105 llp', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[0];
      service.businessType = BusinessTypes.LLP;

      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customNode.children.length).toBe(10);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Off-balance sheet arrangements')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Average number of employees')
        ).toBeGreaterThanOrEqual(1);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Operating profit/(loss)')
        ).toBeGreaterThanOrEqual(2);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Intangible assets')
        ).toBeGreaterThanOrEqual(3);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(4);

        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Guarantees and other financial commitments'
          )
        ).toBeGreaterThanOrEqual(5);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Related party transactions')
        ).toBeGreaterThanOrEqual(6);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 1')
        ).toBeGreaterThanOrEqual(7);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Additional note 2')
        ).toBeGreaterThanOrEqual(8);

        expect(
          service.customNode.children.findIndex(section => section.label === 'Controlling party')
        ).toBeGreaterThanOrEqual(9);
      });
    });

    it('should correctly rename the directors label to director when only one director is present', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[2];
      service.directors = MOCK_DIRECTOR;
      reportNotesResponseMock.status = 200;

      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(
          service.customNode.children.findIndex(
            section => section.label === 'Advances, credits and guarantees granted the Director'
          )
        ).toBeGreaterThanOrEqual(6);
      });
    });

    it('should not have an tangibleFixed assets section when there is specific validation issue warning', () => {
      service.reportData.reportingStandard = MOCK_REPORT_STANDARDS[2];
      service.reportData.validationData = MOCK_VALIDATION_DATA;
      service.reportData.validationData.validationIssues[0].errorCode =
        ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_NOTES_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;

      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(
          service.customNode.children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_FIXED_ASSETS_NOTES_WARN
          )
        ).toBe(-1);
      });
    });

    it('should not have an intangible assets section when there is specific validation issue warning', () => {
      service.reportData.validationData = MOCK_VALIDATION_DATA;
      service.reportData.validationData.validationIssues[0].errorCode =
        ValidationIssueEnum.INTANGIBLE_ASSETS_REVALUATION_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;

      reportNotesResponseMock.status = 200;
      spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(
          service.customNode.children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.INTANGIBLE_ASSETS_REVALUATION_WARN
          )
        ).toBe(-1);
      });
    });

    it('should update the node if it was created previously', () => {
      reportNotesResponseMock.status = 200;
      const getAvgSpy = spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.isCreated).toBe(true);
        expect(updateAvgFlagSpy).toHaveBeenCalled();
      });

      expect(getAvgSpy).toHaveBeenCalledWith('clientId', 'periodId');
    });

    it('should get default value if node was just created', () => {
      reportNotesResponseMock.status = 204;
      const getAvgSpy = spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const updateValuesFromPreviousPeriodSpy = spyOn<any>(service, 'updateValuesFromPreviousPeriod').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        const avgNumberOfEmployees = service.getReportNotesData(MOCK_SECTION_REF.formConfigKey);
        const previousPeriodValue = avgNumberOfEmployees.previousPeriod;
        expect(service.isCreated).toBe(false);
        expect(getAvgSpy).toHaveBeenCalledWith('clientId', '3');
        expect(previousPeriodValue).toEqual(3);
        expect(updateAvgFlagSpy).toHaveBeenCalled();
        expect(updateValuesFromPreviousPeriodSpy).toHaveBeenCalled();
      });

      expect(getAvgSpy).toHaveBeenCalledWith('clientId', 'periodId');
    });

    it('should NOT get default value if there is no previous period id', () => {
      reportNotesResponseMock.status = 204;
      const getAvgSpy = spyOn<any>(service, 'getReportNotes').and.returnValue(of(reportNotesResponseMock));
      const returnedValue = service.generateSubsectionsStructure();
      service.reportData.previousPeriodId = null;

      returnedValue.subscribe(() => {
        const avgNumberOfEmployees = service.getReportNotesData(MOCK_SECTION_REF.formConfigKey);
        const previousPeriodValue = avgNumberOfEmployees.previousPeriod;
        expect(service.isCreated).toBe(false);
        expect(previousPeriodValue).toEqual(null);
      });

      expect(getAvgSpy).toHaveBeenCalledWith('clientId', 'periodId');
    });
  });

  describe('updateReportNotesDataValues', () => {
    it('should update the section matching sectionReference.formConfigKey when formConfigKeyForNewValues param is null', () => {
      service.updateReportNotesDataValues(
        reportNotesMock.averageNumberOfEmployees,
        MOCK_SECTION_REF,
        false,
        null,
        null
      );
      const avgNumberOfEmployees = service.getReportNotesData(MOCK_SECTION_REF.formConfigKey);
      expect(avgNumberOfEmployees).toBe(reportNotesMock.averageNumberOfEmployees);
    });

    it('should update the section matching formConfigKeyForNewValues when the param is not null', () => {
      service.updateReportNotesDataValues(
        reportNotesMock.tangibleFixedAssetsNotes,
        MOCK_SECTION_REF,
        false,
        null,
        NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES
      );
      const tangibleFixedAssetsNotes = service.getReportNotesData(NotesFormTypeEnum.TANGIBLE_FIXED_ASSETS_NOTES);
      expect(tangibleFixedAssetsNotes).toBe(reportNotesMock.tangibleFixedAssetsNotes);
    });
  });

  describe('updateAvgNumberOfEmployeesValues', () => {
    it('should update the section hasData value and store the values', () => {
      service.updateReportNotesDataValues(reportNotesMock.averageNumberOfEmployees, MOCK_SECTION_REF);
      expect(MOCK_SECTION_REF.hasData).toBe(true);

      const avgNumberOfEmployees = service.getReportNotesData(MOCK_SECTION_REF.formConfigKey);
      const currentPeriod = avgNumberOfEmployees.currentPeriod;
      const previousPeriod = avgNumberOfEmployees.previousPeriod;

      expect(currentPeriod).toBe(reportNotesMock.averageNumberOfEmployees.currentPeriod);
      expect(previousPeriod).toBe(reportNotesMock.averageNumberOfEmployees.previousPeriod);
    });

    it('should update the stored values and set hasData to false if there is no data', () => {
      service.isCreated = true;
      service.updateReportNotesDataValues({ currentPeriod: null, previousPeriod: null }, MOCK_SECTION_REF);
      expect(MOCK_SECTION_REF.hasData).toBe(false);
    });

    it('should update the stored values and set hasData to true if there is currentPeriod data', () => {
      const currentAvgNumberMock = {
        averageNumberOfEmployees: { ...avgNumberMock.averageNumberOfEmployees }
      };
      currentAvgNumberMock.averageNumberOfEmployees.previousPeriod = null;
      service.isCreated = true;
      service.updateReportNotesDataValues(
        { ...reportNotesMock.averageNumberOfEmployees, previousPeriod: null },
        MOCK_SECTION_REF
      );
      expect(MOCK_SECTION_REF.hasData).toBe(true);
    });

    it('should update the stored values and set hasData to true if there is previousPeriod data', () => {
      const currentAvgNumberMock = {
        averageNumberOfEmployees: { ...avgNumberMock.averageNumberOfEmployees }
      };
      currentAvgNumberMock.averageNumberOfEmployees.currentPeriod = null;
      service.isCreated = true;
      service.updateReportNotesDataValues(
        { ...reportNotesMock.averageNumberOfEmployees, currentPeriod: null },
        MOCK_SECTION_REF
      );
      expect(MOCK_SECTION_REF.hasData).toBe(true);
    });

    it('should update the the value in report notes data if it not an object', () => {
      const MOCK_PARAGRAPH_REF = { ...MOCK_SECTION_REF, formConfigKey: NotesFormTypeEnum.ADVANCES };
      const MOCK_VALUE_CHANGE = 'new value';
      service.updateReportNotesDataValues(MOCK_VALUE_CHANGE, MOCK_PARAGRAPH_REF);
      expect(MOCK_PARAGRAPH_REF.hasData).toBe(true);
      expect(service.getReportNotesData(MOCK_PARAGRAPH_REF.formConfigKey)).toBe(MOCK_VALUE_CHANGE);
    });
  });

  describe('initSubsectionsValidity', () => {
    beforeEach(() => {
      const MOCK_PARENT_SECTION: ReportSection = {
        label: 'parent node',
        children: [MOCK_SECTION_REF],
        errorCount: 0,
        warningCount: 0,
        isMandatory: true,
        parent: null
      };
      MOCK_SECTION_REF.parent = MOCK_PARENT_SECTION;
      service.customNode = MOCK_PARENT_SECTION;
    });

    it('should increase the error count for the parent section when there is a validation problem', () => {
      service.updateReportNotesDataValues(
        { ...reportNotesMock.averageNumberOfEmployees, currentPeriod: null },
        MOCK_SECTION_REF
      );
      service.initSubsectionsValidity(MOCK_REPORT_DATA.validationData);
      expect(service.customNode.errorCount).toBe(1);
    });

    it('should increase the warning count for the parent section when there is a validation problem', () => {
      const MOCK_ADDITIONAL_NOTE = {
        ...MOCK_SECTION_REF,
        formConfigKey: NotesFormTypeEnum.ADDITIONAL_NOTE_1
      };
      MOCK_ADDITIONAL_NOTE.parent = service.customNode;
      service.customNode.children = [MOCK_ADDITIONAL_NOTE];
      service.updateReportNotesDataValues({ ...reportNotesMock.additionalNote1, noteText: null }, MOCK_ADDITIONAL_NOTE);
      service.initSubsectionsValidity(MOCK_REPORT_DATA.validationData);
      expect(service.customNode.warningCount).toBe(1);
    });

    it('should increase the error count for the parent section when there is a validation problem for advances', () => {
      const validationData: ValidationData = {
        advisoryValidationIssuesLogCount: 0,
        mandatoryValidationIssuesLogCount: 0,
        validationIssues: [
          {
            errorCode: ValidationIssueEnum.ADVANCES_CREDIT_AND_GUARANTEES_TO_DIRECTORS_ERROR,
            description: 'mock desc',
            errorCategory: ValidationIssueCategory.Mandatory,
            name: 'AdvancesCreditGuarantees',
            type: ValidationIssueType.Missing,
            target: ValidationIssueTarget.IssueLog
          }
        ]
      };
      const MOCK_ADVANCES_NOTE = {
        ...MOCK_SECTION_REF,
        formConfigKey: NotesFormTypeEnum.ADVANCES
      };
      MOCK_ADVANCES_NOTE.parent = service.customNode;
      service.customNode.children = [MOCK_ADVANCES_NOTE];
      service.initSubsectionsValidity(validationData);
      expect(service.customNode.errorCount).toBe(1);
    });
  });

  describe('hasSectionValidation', () => {
    it('should return true if that section is found in the validation array', () => {
      service.validationData.push(NotesFormTypeEnum.ADVANCES);
      const validationSearch = service.hasSectionValidation(NotesFormTypeEnum.ADVANCES);
      expect(validationSearch).toBe(true);
    });

    it('should return false if that section is NOT found in the validation array', () => {
      service.validationData = [];
      const validationSearch = service.hasSectionValidation(NotesFormTypeEnum.ADVANCES);
      expect(validationSearch).toBe(false);
    });
  });
});
