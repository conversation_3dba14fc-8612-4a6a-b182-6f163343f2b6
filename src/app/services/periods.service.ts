import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { AccountProductionPeriod } from '../models/period.model';
import { EntitySetupResponse } from '../models/entity-setup-response';

@Injectable({
  providedIn: 'root'
})
export class PeriodsService {
  periodData = new BehaviorSubject<AccountProductionPeriod>(null);

  constructor(private http: HttpClient) {}

  getSourceData(periodId: number): Observable<number> {
    return this.http.get<number>(
      `${environment.accountsproduction_trialbalance_api_url}/accountperiods/${periodId}/detailedaccountbalances/count`,
      {
        headers: {
          'x-api-key': environment.accountsproduction_trialbalance_api_key
        }
      }
    );
  }

  getPeriodData(
    clientId: string,
    periodId: number,
    includePreviousPeriod: boolean
  ): Observable<AccountProductionPeriod> {
    const periodDataRoute = `${environment.accounts_production_periods_api_uri}/clients/${clientId}/accountperiods/${periodId}?includePreviousPeriod=${includePreviousPeriod}`;

    return this.http
      .get<AccountProductionPeriod>(periodDataRoute, {
        headers: {
          'x-api-key': environment.accounts_production_periods_api_key
        }
      })
      .pipe(
        map(response => {
          this.periodData.next(response);
          return response;
        })
      );
  }

  getPeriodList(clientId: string): Observable<AccountProductionPeriod[]> {
    const periodListRoute = `${environment.accounts_production_periods_api_uri}/clients/${clientId}/accountperiods`;
    return this.http
      .get<AccountProductionPeriod[]>(periodListRoute, {
        headers: {
          'x-api-key': environment.accounts_production_periods_api_key
        }
      })
      .pipe(
        map(period =>
          period.sort((a: AccountProductionPeriod, b: AccountProductionPeriod) => {
            return +new Date(b.endDate) - +new Date(a.endDate);
          })
        )
      );
  }

  getEntitySetup(clientId: string, periodId: number): Observable<EntitySetupResponse> {
    const url = `${environment.accounts_production_periods_api_uri}/clients/${clientId}/accountperiods/${periodId}/entitysetup`;
    return this.http.get<EntitySetupResponse>(url, {
      headers: { 'x-api-key': environment.accounts_production_periods_api_key }
    });
  }
}
