import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { PeriodsService } from './periods.service';

describe('PeriodsService', () => {
  let service: PeriodsService;
  let httpClient: HttpClientTestingModule;
  const PERIOD = { periodId: '8a2817c0-19d7-11ec-9621-0242ac130002', previousPeriodId: '11111111-1111-1111-1111-111111111111' };
  const SOURCE_DATA_ENTRIES: number = 2;

  const RESPONSE_MAPPINGS = {
    getSourceData: of(SOURCE_DATA_ENTRIES),
    getPeriodData: of(PERIOD)
  };

  const createHttpClient = (type: string) => {
    return {
      get: () => RESPONSE_MAPPINGS[type] ?? throwError('error')
    };
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule]
    });
    service = TestBed.inject(PeriodsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getSourceData', () => {
    it('should return the correct response', () => {
      httpClient = createHttpClient('getSourceData');
      service = new PeriodsService(httpClient as HttpClient);

      service.getSourceData(123).subscribe(res => {
        expect(res).toEqual(SOURCE_DATA_ENTRIES);
      });
    });
  });

  describe('getPeriodData', () => {
    it('should return the current plus previousPeriodId', () => {
      httpClient = createHttpClient('getPeriodData');
      service = new PeriodsService(httpClient as HttpClient);
      const expectedPeriodId = PERIOD.periodId;
      const expectedPreviousPeriodId = PERIOD.previousPeriodId;

      service.getPeriodData('123', 456, true).subscribe(res => {
        expect(res.periodId).toEqual(expectedPeriodId);
        expect(res.previousPeriodId).toEqual(expectedPreviousPeriodId);
      });
    });
  });
});
