import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { empty, EMPTY, Observable, of, Subject } from 'rxjs';
import { catchError, debounceTime, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { DefaultErrorAlert } from '../models/alert.model';
import {
  accountingPoliciesSection,
  accountingPoliciesSubsections,
  balanceSheetSection,
  plantAndMachinerySubsections,
  landAndBuildingsSubsections,
  tangibleAssetsSubsections,
  intangibleAssetsSubsections
} from '../models/report-sections/accounting-policies.model';
import {
  AccountingPoliciesData,
  AccountingPoliciesResponse,
  ExemptionTypeEnum,
  NotesFormTypeEnum,
  StatusTypesEnum,
  ValidationIssueEnum
} from '../models/report-sections/report-notes.model';
import { ReportSection } from '../models/report-sections/report-sections.model';
import { ReportData, ReportingStandards, ValidationData } from '../models/report.model';
import { AccountsBuilderService } from './accounts-builder.service';
import { AlertService } from './alert.service';
import { BusinessTypes } from '../models/client.model';

@Injectable({
  providedIn: 'root'
})
export class AccountingPoliciesService {
  private readonly AP_PERIODS_API_ENDPOINT = `${environment.accounts_production_periods_api_uri}/clients`;
  private readonly AP_PERIODS_API_KEY = { 'x-api-key': environment.accounts_production_periods_api_key };

  private triggerChanges: Subject<AccountingPoliciesData> = new Subject<AccountingPoliciesData>();
  private triggerRefreshStatusBanner: Subject<boolean> = new Subject<boolean>();
  triggerRefreshStatusBanner$ = this.triggerRefreshStatusBanner.asObservable();
  accountingPoliciesSection: ReportSection = null;
  customizationData: AccountingPoliciesData;
  customizationSections: ReportSection[];
  reportData: ReportData;
  reportValidationCodes: string[];
  isCreated = false;
  businessType: string;
  reportingStandard: string;

  constructor(
    private alertService: AlertService,
    private accountsBuilderService: AccountsBuilderService,
    private http: HttpClient
  ) {
    this.initCustomizationData();
    this.accountsBuilderService.reportData.subscribe(newReportData => {
      this.reportData = newReportData;
      this.customizationData.previousPeriodId = this.reportData?.previousPeriodId ?? null;
      this.customizationData.clientId = this.reportData?.clientId ?? null;
    });

    this.triggerChanges.pipe(debounceTime(1000)).subscribe(reportNotesData => {
      const url = `${this.AP_PERIODS_API_ENDPOINT}/${this.reportData.clientId}/accountperiods/${this.reportData.periodId}/accountingpolicies`;
      const payload: AccountingPoliciesData = {
        ...reportNotesData
      };

      if (payload.exemptionsFinancialStatements.section === null) {
        payload.exemptionsFinancialStatements.section = ExemptionTypeEnum.NO_EXEMPTION;
      }

      if (!this.isCreated) {
        this.saveValues(payload, url);
      } else {
        this.updateValues(payload, url);
      }
    });
  }

  initCustomizationData(): void {
    const assetsBasisDefault = {
      categoryDescription: null,
      reducingBalanceBasis: null,
      straightLineBasis: null,
      alternativeBasis: null
    };

    this.customizationData = {
      clientId: null,
      previousPeriodId: null,
      exemptionsFinancialStatements: {
        section: null,
        parentName: null,
        parentAddress: null
      },
      researchAndDevelopment: null,
      foreignCurrencies: null,
      presentationCurrency: null,
      changesInAccountingPolicies: null,
      financialInstrumentsAccountingPolicy: null,
      governmentGrantsAccountingPolicy: null,
      goodwillMaterial: null,
      membersTransactionsWithTheLlpText: null,
      tangibleFixedAssets: {
        plantAndMachinery: {
          classNameCustomization: null,
          improvementsToProperty: {
            ...assetsBasisDefault
          },
          plantAndMachinery: {
            ...assetsBasisDefault
          },
          fixturesAndFittings: {
            ...assetsBasisDefault
          },
          motorVehicles: {
            ...assetsBasisDefault
          },
          computerEquipment: {
            ...assetsBasisDefault
          }
        },
        landAndBuildings: {
          classNameCustomization: null,
          freeholdProperty: {
            ...assetsBasisDefault
          },
          shortLeaseholdProperty: {
            ...assetsBasisDefault
          },
          longLeaseholdProperty: {
            ...assetsBasisDefault
          }
        }
      },
      intangibleAssets: {
        goodwill: {
          ...assetsBasisDefault
        },
        patentsAndLicenses: {
          ...assetsBasisDefault
        },
        computerSoftware: {
          ...assetsBasisDefault
        },
        developmentCosts: {
          ...assetsBasisDefault
        }
      }
    };
  }

  generateBalanceSheetStructure(): Observable<ReportSection> {
    const balanceSheet = { ...balanceSheetSection };
    const relatedValidationIssue = this.reportData?.validationData.validationIssues?.findIndex(
      validation => validation.errorCode === balanceSheet.validationIssueCode
    );
    if (relatedValidationIssue === -1) {
      return of(balanceSheet);
    }
    return of(null);
  }

  generateSubsectionsStructure(): Observable<ReportSection> {
    return this.getAccountingPolicies(this.reportData.clientId, this.reportData.periodId).pipe(
      map((response: AccountingPoliciesResponse) => {
        this.accountingPoliciesSection = { ...accountingPoliciesSection };
        this.accountingPoliciesSection.children = [];
        accountingPoliciesSubsections.forEach(subsection => {
          this.accountingPoliciesSection.children.push({
            ...subsection,
            parent: this.accountingPoliciesSection
          });
        });

        if (this.businessType !== BusinessTypes.LLP || this.reportingStandard !== ReportingStandards.FRS102_1A) {
          this.accountingPoliciesSection.children = this.accountingPoliciesSection.children.filter(
            section => section.formConfigKey !== NotesFormTypeEnum.MEMBERS_TRANSACTIONS
          );
        }
        this.customizationSections = [];
        this.customizationSections.push(
          ...this.accountingPoliciesSection.children.filter(section => section.formConfigKey)
        );
        this.updateTangibleAssetsSection(this.reportData.validationData);
        this.updateIntangibleAssetsSection(this.reportData.validationData);
        this.accountingPoliciesSection.children = this.accountingPoliciesSection.children.filter(
          c => c.formConfigKey || (!c.formConfigKey && c.children.length > 0)
        );
        if (response.status === StatusTypesEnum.CREATED) {
          this.isCreated = false;
          if (this.reportData.previousPeriodId) {
            this.getAccountingPolicies(this.reportData.clientId, this.reportData.previousPeriodId).subscribe(
              (previousResponse: AccountingPoliciesResponse) => {
                if (previousResponse.body) {
                  this.customizationData = this.storeDefaultValues(previousResponse.body, this.customizationData);
                  this.customizationData.changesInAccountingPolicies = null;
                }
                this.updateReportNotesDataFlags();
              }
            );
          }
        } else {
          this.isCreated = true;
          this.customizationData = this.storeDefaultValues(response.body, this.customizationData);
          this.updateReportNotesDataFlags();
        }

        return this.accountingPoliciesSection;
      })
    );
  }

  storeDefaultValues(requestBody, defaultValues) {
    Object.keys(defaultValues).forEach(dv => {
      if (typeof defaultValues[dv] === typeof {} && defaultValues[dv]) {
        if (requestBody[dv]) {
          defaultValues[dv] = this.storeDefaultValues(requestBody[dv], defaultValues[dv]);
        }
      } else {
        defaultValues[dv] = requestBody[dv] ?? defaultValues[dv];
      }
    });
    return defaultValues;
  }

  initSubsectionsValidity(validationData: ValidationData): void {
    this.reportValidationCodes = [];
    validationData.validationIssues.forEach(validation => this.reportValidationCodes.push(validation.errorCode));
    this.customizationSections.forEach(section => {
      if (section.formConfigKey === NotesFormTypeEnum.MEMBERS_TRANSACTIONS && !section.hasData) {
        this.updateErrorCounts(section, true);
      }
      if (!section.validationIssueCode) return;
      const isSectionMandatory =
        this.reportValidationCodes.findIndex(error => error === section.validationIssueCode) > -1;
      if (isSectionMandatory) {
        const sectionData = this.getNestedProperty(this.customizationData, section.formConfigKey);
        switch (section.formConfigKey) {
          case NotesFormTypeEnum.IMPROVEMENTS_PROPERTY:
          case NotesFormTypeEnum.PLANT_AND_MACHINERY:
          case NotesFormTypeEnum.FIXTURE_AND_FITTINGS:
          case NotesFormTypeEnum.MOTOR_VEHICLES:
          case NotesFormTypeEnum.COMPUTER_EQUIPMENT:
          case NotesFormTypeEnum.GOODWILL:
          case NotesFormTypeEnum.PATENTS_AND_LICENSES:
          case NotesFormTypeEnum.DEVELOPMENT_COSTS:
          case NotesFormTypeEnum.COMPUTER_SOFTWARE:
          case NotesFormTypeEnum.FREEHOLD_PROPERTY:
          case NotesFormTypeEnum.SHORT_LEASEHOLD_PROPERTY:
          case NotesFormTypeEnum.LONG_LEASEHOLD_PROPERTY:
          case NotesFormTypeEnum.MEMBERS_TRANSACTIONS:
            if (
              !sectionData.categoryDescription ||
              !(sectionData.reducingBalanceBasis || sectionData.straightLineBasis || sectionData.alternativeBasis)
            ) {
              this.updateErrorCounts(section, true);
            }
            break;
        }
      }
    });
  }

  updateTangibleAssetsSection(validationData: ValidationData): void {
    const tangibleAssetsIndex = this.accountingPoliciesSection.children.findIndex(
      section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
    );
    let newTangibleAssetsSubsections = [...tangibleAssetsSubsections];

    const isTangibleFixedAssetsVisible =
      validationData.validationIssues.findIndex(
        validationItem => validationItem.errorCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
      ) === -1;

    if (!isTangibleFixedAssetsVisible) {
      this.accountingPoliciesSection.children = this.accountingPoliciesSection.children.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.TANGIBLE_ASSETS_WARN
      );
      return;
    }

    const isPlantAndMachineryVisible =
      validationData.validationIssues.findIndex(
        validationItem => validationItem.errorCode === ValidationIssueEnum.PLANT_AND_MACHINERY_WARN
      ) === -1;

    const isLandAndBuildingsVisible =
      validationData.validationIssues.findIndex(
        validationItem => validationItem.errorCode === ValidationIssueEnum.LAND_AND_BUILDING_WARN
      ) === -1;

    if (!isPlantAndMachineryVisible && !isLandAndBuildingsVisible) {
      this.accountingPoliciesSection.children = this.accountingPoliciesSection.children.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.TANGIBLE_ASSETS_WARN
      );
      return;
    }

    newTangibleAssetsSubsections = this.updatePlantAndMachinerySection(
      newTangibleAssetsSubsections,
      isPlantAndMachineryVisible
    );
    newTangibleAssetsSubsections = this.updateLandAndBuildingsSection(
      newTangibleAssetsSubsections,
      isLandAndBuildingsVisible
    );

    this.accountingPoliciesSection.children[tangibleAssetsIndex].children = [];
    this.accountingPoliciesSection.children[tangibleAssetsIndex].children.push(...newTangibleAssetsSubsections);
    this.accountingPoliciesSection.children[tangibleAssetsIndex].children.forEach(
      section => (section.parent = this.accountingPoliciesSection.children[tangibleAssetsIndex])
    );
  }

  updateLandAndBuildingsSection(
    tangibleAssetsSubsections: ReportSection[],
    isLandAndBuildingsVisible: boolean
  ): ReportSection[] {
    if (!isLandAndBuildingsVisible) {
      tangibleAssetsSubsections = tangibleAssetsSubsections.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.LAND_AND_BUILDING_WARN
      );
    } else {
      const newLandAndBuildingsSubsections = [...landAndBuildingsSubsections];
      const landAndBuildingsSection = tangibleAssetsSubsections.find(
        section => section.validationIssueCode === ValidationIssueEnum.LAND_AND_BUILDING_WARN
      );
      landAndBuildingsSection.children = [];
      landAndBuildingsSection.children.push(...newLandAndBuildingsSubsections);
      landAndBuildingsSection.children.forEach(section => (section.parent = landAndBuildingsSection));
      this.customizationSections.push(...newLandAndBuildingsSubsections);
    }
    return tangibleAssetsSubsections;
  }

  updatePlantAndMachinerySection(
    tangibleAssetsSubsections: ReportSection[],
    isPlantAndMachineryVisible: boolean
  ): ReportSection[] {
    if (!isPlantAndMachineryVisible) {
      tangibleAssetsSubsections = tangibleAssetsSubsections.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.PLANT_AND_MACHINERY_WARN
      );
    } else {
      const plantAndMachinerySection = tangibleAssetsSubsections.find(
        section => section.validationIssueCode === ValidationIssueEnum.PLANT_AND_MACHINERY_WARN
      );
      const newPlantAndMachinerySubsections = [...plantAndMachinerySubsections];
      plantAndMachinerySection.children = [];
      plantAndMachinerySection.children.push(...newPlantAndMachinerySubsections);
      plantAndMachinerySection.children.forEach(section => (section.parent = plantAndMachinerySection));
      this.customizationSections.push(...newPlantAndMachinerySubsections);
    }
    return tangibleAssetsSubsections;
  }

  updateIntangibleAssetsSection(validationData: ValidationData): void {
    const intangibleAssetsIndex = this.accountingPoliciesSection.children.findIndex(
      section => section.validationIssueCode === ValidationIssueEnum.INTANGIBLE_ASSETS_WARN
    );
    let newIntangibleAssetsSubsections = [...intangibleAssetsSubsections];

    const isIntangibleAssetsVisible =
      validationData.validationIssues.findIndex(
        validationItem => validationItem.errorCode === ValidationIssueEnum.INTANGIBLE_ASSETS_WARN
      ) === -1;

    if (!isIntangibleAssetsVisible) {
      this.accountingPoliciesSection.children = this.accountingPoliciesSection.children.filter(
        section => section.validationIssueCode !== ValidationIssueEnum.INTANGIBLE_ASSETS_WARN
      );
      return;
    } else {
      this.customizationSections.push(...newIntangibleAssetsSubsections);
      this.accountingPoliciesSection.children[intangibleAssetsIndex].children = [];
      this.accountingPoliciesSection.children[intangibleAssetsIndex].children.push(...newIntangibleAssetsSubsections);
      this.accountingPoliciesSection.children[intangibleAssetsIndex].children.forEach(
        section => (section.parent = this.accountingPoliciesSection.children[intangibleAssetsIndex])
      );
    }
  }

  getCustomizationData(propertyPath: string) {
    let returnedProperty: any = this.customizationData;
    const propertyArray = propertyPath.split('/');
    propertyArray.forEach(propertyName => {
      returnedProperty = returnedProperty[propertyName];
    });
    return returnedProperty;
  }

  updateCustomizationDataValues(
    newValues: any,
    sectionRef: ReportSection,
    hasError: boolean = false,
    hasWarning: boolean = false
  ): void {
    sectionRef.hasData = this.checkValues(newValues);
    this.customizationData = this.setNestedProperty(this.customizationData, sectionRef.formConfigKey, newValues);
    this.triggerChanges.next(this.customizationData);
    if (sectionRef.skipCountUpdate) return;
    if ((hasError && !sectionRef.errorCount) || (!hasError && sectionRef.errorCount)) {
      this.updateErrorCounts(sectionRef, hasError);
    }
    if ((hasWarning && !sectionRef.warningCount) || (!hasWarning && sectionRef.warningCount)) {
      this.updateWarningCounts(sectionRef, hasWarning);
    }
  }

  updateErrorCounts(sectionRef: ReportSection, hasError: boolean): void {
    while (sectionRef !== null) {
      hasError ? sectionRef.errorCount++ : sectionRef.errorCount--;
      sectionRef = sectionRef.parent;
    }
  }

  updateWarningCounts(sectionRef: ReportSection, hasWarning: boolean): void {
    while (sectionRef !== null) {
      hasWarning ? sectionRef.warningCount++ : sectionRef.warningCount--;
      sectionRef = sectionRef.parent;
    }
  }

  private checkValues(keyValue: any): boolean {
    if (keyValue) {
      if (typeof keyValue !== typeof {}) {
        return keyValue !== null;
      }
      return Object.keys(keyValue).some(value => {
        return keyValue[value] !== null;
      });
    }
    return false;
  }

  refreshStatusBanner(trigger: boolean) {
    this.triggerRefreshStatusBanner.next(trigger);
  }

  private updateReportNotesDataFlags(): void {
    this.customizationSections.forEach(child => {
      let keyValue = this.customizationData;
      const propertyArray = child.formConfigKey.split('/');
      propertyArray.forEach(propertyName => {
        keyValue = keyValue[propertyName];
      });
      child.hasData = this.checkValues(keyValue);
    });
  }

  private getAccountingPolicies(clientId: string, periodId: string): Observable<AccountingPoliciesResponse> {
    return this.http.get<AccountingPoliciesData>(
      `${this.AP_PERIODS_API_ENDPOINT}/${clientId}/accountperiods/${periodId}/accountingpolicies`,
      {
        headers: this.AP_PERIODS_API_KEY,
        observe: 'response'
      }
    );
  }

  private saveValues(payload: AccountingPoliciesData, url: string): void {
    this.http
      .post(url, payload, { headers: this.AP_PERIODS_API_KEY })
      .pipe(
        catchError(() => {
          this.alertService.showAlert(DefaultErrorAlert);
          return EMPTY;
        })
      )
      .subscribe(() => {
        this.isCreated = true;
        this.refreshStatusBanner(true);
      });
  }

  private updateValues(payload: AccountingPoliciesData, url: string) {
    this.http
      .put(url, payload, { headers: this.AP_PERIODS_API_KEY })
      .pipe(
        catchError(() => {
          this.alertService.showAlert(DefaultErrorAlert);
          return EMPTY;
        })
      )
      .subscribe(() => this.refreshStatusBanner(true));
  }

  setNestedProperty(object, path, newValue) {
    const [head, ...rest] = path.split('/');

    return {
      ...object,
      [head]: rest.length ? this.setNestedProperty(object[head], rest.join('/'), newValue) : newValue
    };
  }

  getNestedProperty(object, path) {
    const pathArray = path.split('/');
    let returnedProperty = object;
    pathArray.forEach(prop => {
      returnedProperty = returnedProperty[prop];
    });
    return returnedProperty;
  }

  hasSectionValidation(sectionErrorCode): boolean {
    return this.reportValidationCodes.findIndex(error => error === sectionErrorCode) > -1;
  }
}
