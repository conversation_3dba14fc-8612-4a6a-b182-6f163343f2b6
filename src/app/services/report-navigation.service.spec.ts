import { TestBed } from '@angular/core/testing';
import { BehaviorSubject, Subscription } from 'rxjs';
import { Breadcrumb } from '../models/breadcrumb.model';
import { ReportCustomizationService } from './report-customization.service';

import { ReportNavigationService } from './report-navigation.service';

const MOCK_SECTIONS_ROOT = {
  parent: null,
  label: 'Root node',
  isMandatory: false,
  formConfigKey: null,
  children: null,
  errorCount: 0,
  warningCount: 0
};
const MOCK_SECTIONS_NODE = {
  parent: MOCK_SECTIONS_ROOT,
  label: 'Sections reference',
  isMandatory: false,
  formConfigKey: null,
  children: null,
  errorCount: 0,
  warningCount: 0
};
MOCK_SECTIONS_ROOT.children = [MOCK_SECTIONS_NODE];

const reportCustomizationServiceMock = jasmine.createSpyObj('ReportCustomizationService', ['']);

describe('ReportNavigationService', () => {
  let service: ReportNavigationService;

  beforeEach(() => {
    reportCustomizationServiceMock.reportSectionsTree = new BehaviorSubject(MOCK_SECTIONS_ROOT);
    TestBed.configureTestingModule({
      providers: [
        {
          provide: ReportCustomizationService,
          useValue: reportCustomizationServiceMock
        }
      ]
    });
    service = TestBed.inject(ReportNavigationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('updateNavigation', () => {
    it('should trigger the update of the navigation using the referenced node', () => {
      const currentNavigationNodeSpy = spyOn(service.currentNavigationNode, 'next');
      const breadcrumbsNavigationsLinksSpy = spyOn(service.breadcrumbsNavigationsLinks, 'next');
      service.updateNavigation(MOCK_SECTIONS_NODE);

      expect(currentNavigationNodeSpy).toHaveBeenCalledWith(MOCK_SECTIONS_NODE);
      expect(breadcrumbsNavigationsLinksSpy).toHaveBeenCalled();
    });
  });

  describe('generateBreadcrumbs', () => {
    it('should generate a breadcrumbs-type structure based on the current tree', () => {
      const EXPECTED_GENERATED_BREADCRUMBS: Breadcrumb[] = [
        {
          name: MOCK_SECTIONS_ROOT.label,
          reference: MOCK_SECTIONS_ROOT
        },
        {
          name: MOCK_SECTIONS_NODE.label,
          reference: MOCK_SECTIONS_NODE
        }
      ];
      service.updateNavigation(MOCK_SECTIONS_NODE);
      const generatedBreabcrumbs = service.generateBreadcrumbs();

      expect(generatedBreabcrumbs).toEqual(EXPECTED_GENERATED_BREADCRUMBS);
    });
  });

  describe('navigateToNode', () => {
    it('should generate a breadcrumbs-type structure based on the current tree', () => {
      const updateNavigationSpy = spyOn(service, 'updateNavigation');
      service.navigateToNode(MOCK_SECTIONS_NODE);

      expect(updateNavigationSpy).toHaveBeenCalledWith(MOCK_SECTIONS_NODE);
    });
  });

  describe('unsubscribe', () => {
    beforeEach(() => {
      service.$subscriptions.push(new Subscription());
      service.$subscriptions.push(new Subscription());
    });

    it('should remove all subscriptions', () => {
      service.ngOnDestroy();
      expect(service.$subscriptions.length).toBe(0);
    });
  });
});
