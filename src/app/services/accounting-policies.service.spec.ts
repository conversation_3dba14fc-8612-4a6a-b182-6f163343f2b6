import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { BehaviorSubject, of, throwError } from 'rxjs';
import {
  AccountingPoliciesData,
  AccountingPoliciesResponse,
  AssetsBasis,
  ExemptionTypeEnum,
  NotesFormTypeEnum,
  ValidationIssueEnum
} from '../models/report-sections/report-notes.model';
import { MOCK_REPORT_STANDARDS } from '../models/report.mock';
import {
  ReportingStandards,
  ReportingStandardVersionName,
  ValidationData,
  ValidationIssueCategory,
  ValidationIssueTarget,
  ValidationIssueType
} from '../models/report.model';

import { AccountingPoliciesService } from './accounting-policies.service';

const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', [
  'triggerGenerateReport',
  'getReportStatus',
  'getReportData'
]);
const httpClientMock = jasmine.createSpyObj('HttpClient', ['post', 'put', 'get']);

const alertServiceMock = jasmine.createSpyObj('AlertService', ['showAlert']);

const assetsBasisMock = {
  categoryDescription: 'mock category',
  reducingBalanceBasis: 4,
  straightLineBasis: 2,
  alternativeBasis: 'mock alt'
};
const accountingPoliciesDataMock: AccountingPoliciesData = {
  clientId: 'mock-client-id',
  previousPeriodId: 'mock-period-id',
  exemptionsFinancialStatements: {
    section: ExemptionTypeEnum.SECTION_400,
    parentName: 'mock name',
    parentAddress: 'mock address'
  },
  researchAndDevelopment: false,
  presentationCurrency: false,
  foreignCurrencies: false,
  changesInAccountingPolicies: 'changes paragraph',
  governmentGrantsAccountingPolicy: 'government grants paragraph',
  financialInstrumentsAccountingPolicy: 'financial instruments paragraph',
  goodwillMaterial: true,
  tangibleFixedAssets: {
    plantAndMachinery: {
      classNameCustomization: 'mock name',
      improvementsToProperty: { ...assetsBasisMock },
      plantAndMachinery: { ...assetsBasisMock },
      fixturesAndFittings: { ...assetsBasisMock },
      motorVehicles: { ...assetsBasisMock },
      computerEquipment: { ...assetsBasisMock }
    },
    landAndBuildings: {
      classNameCustomization: 'mock name',
      freeholdProperty: { ...assetsBasisMock },
      shortLeaseholdProperty: { ...assetsBasisMock },
      longLeaseholdProperty: { ...assetsBasisMock }
    }
  },
  intangibleAssets: {
    goodwill: { ...assetsBasisMock },
    patentsAndLicenses: { ...assetsBasisMock },
    computerSoftware: { ...assetsBasisMock },
    developmentCosts: { ...assetsBasisMock }
  },
  membersTransactionsWithTheLlpText: 'members transactions paragraph'
};
const accountingPoliciesResponseMock: AccountingPoliciesResponse = {
  body: accountingPoliciesDataMock,
  status: 204
};

const MOCK_SECTION_REF = {
  parent: null,
  children: null,
  isMandatory: false,
  hasData: false,
  formConfigKey: NotesFormTypeEnum.EXEMPTION_FINANCIAL,
  label: 'mock section',
  errorCount: 0,
  warningCount: 0
};

const MOCK_VALIDATION_DATA: ValidationData = {
  advisoryValidationIssuesLogCount: 0,
  mandatoryValidationIssuesLogCount: 0,
  validationIssues: [
    {
      errorCode: ValidationIssueEnum.IMPROVEMENTS_PROPERTY_ERROR,
      description: 'mock desc',
      errorCategory: ValidationIssueCategory.Mandatory,
      name: 'mock name',
      type: ValidationIssueType.Missing,
      target: ValidationIssueTarget.IssueLog
    }
  ]
};

describe('AccountingPoliciesService', () => {
  let service: AccountingPoliciesService;

  beforeEach(() => {
    httpClientMock.post.and.returnValue(of([]));
    httpClientMock.put.and.returnValue(of([]));
    const reportDataMock = {
      previousPeriodId: 'previousPeriodId',
      clientId: 'clientId',
      periodId: 'periodId',
      validationData: {
        validationIssues: []
      },
      reportType: MOCK_REPORT_STANDARDS[2]
    };
    TestBed.configureTestingModule({});
    accountsBuilderServiceMock.reportData = new BehaviorSubject(reportDataMock);
    service = new AccountingPoliciesService(alertServiceMock, accountsBuilderServiceMock, httpClientMock);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should display an alert when the save call fails', fakeAsync(() => {
    service.isCreated = false;
    httpClientMock.post.and.returnValue(throwError({ status: 404 }));
    service.updateCustomizationDataValues(accountingPoliciesDataMock.exemptionsFinancialStatements, MOCK_SECTION_REF);
    tick(1000);
    expect(alertServiceMock.showAlert).toHaveBeenCalled();
  }));

  it('should display an alert when the update call fails', fakeAsync(() => {
    service.isCreated = true;
    httpClientMock.put.and.returnValue(throwError({ status: 404 }));
    service.updateCustomizationDataValues(accountingPoliciesDataMock.exemptionsFinancialStatements, MOCK_SECTION_REF);
    tick(1000);
    expect(alertServiceMock.showAlert).toHaveBeenCalled();
  }));

  it('should trigger the refresh status banner when the save call succeeds', fakeAsync(() => {
    const refreshStatusBannerSpy = spyOn(service, 'refreshStatusBanner');
    service.isCreated = false;
    service.updateCustomizationDataValues(accountingPoliciesDataMock.exemptionsFinancialStatements, MOCK_SECTION_REF);
    tick(1000);
    expect(service.isCreated).toBe(true);
    expect(refreshStatusBannerSpy).toHaveBeenCalledWith(true);
  }));

  describe('generateSubsectionsStructure', () => {
    it('should have all the sections generated', () => {
      accountingPoliciesResponseMock.status = 200;
      spyOn<any>(service, 'getAccountingPolicies').and.returnValue(of(accountingPoliciesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.accountingPoliciesSection.children.length).toBe(9);

        expect(
          service.accountingPoliciesSection.children.findIndex(
            section => section.label === 'Exemption from preparation of consolidated financial statements'
          )
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(
            section => section.label === 'Changes in accounting policies'
          )
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Intangible assets')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Tangible fixed assets')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Financial instruments')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Government grants')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Research and development')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Foreign currencies')
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children.findIndex(section => section.label === 'Presentation currency')
        ).toBeGreaterThanOrEqual(0);
      });
    });

    it('should update the node if it was created previously', () => {
      accountingPoliciesResponseMock.status = 200;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.isCreated).toBe(true);
        expect(updateAvgFlagSpy).toHaveBeenCalled();
      });

      expect(getAvgSpy).toHaveBeenCalledWith('clientId', 'periodId');
    });

    it('should get default value if node was just created', () => {
      accountingPoliciesResponseMock.status = 204;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(service.customizationData.exemptionsFinancialStatements.section).toBe(
          accountingPoliciesDataMock.exemptionsFinancialStatements.section
        );
        expect(updateAvgFlagSpy).toHaveBeenCalled();
      });

      expect(getAvgSpy).toHaveBeenCalledWith('clientId', 'periodId');
    });

    it('should NOT get default value if there is no previous period id', () => {
      accountingPoliciesResponseMock.status = 204;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();
      service.reportData.previousPeriodId = null;
      returnedValue.subscribe(() => {
        expect(service.isCreated).toBe(false);
        expect(service.customizationData.exemptionsFinancialStatements.parentAddress).toEqual(null);
      });

      expect(getAvgSpy).toHaveBeenCalledWith('clientId', 'periodId');
    });

    it('should get default values for missing properties from response', () => {
      const incompleteAccountingPoliciesDataMock: Partial<AccountingPoliciesData> = {
        changesInAccountingPolicies: 'some text here',
        tangibleFixedAssets: {
          landAndBuildings: { ...accountingPoliciesDataMock.tangibleFixedAssets?.landAndBuildings },
          plantAndMachinery: null
        }
      };
      service.initCustomizationData();
      const returnedValue = service.storeDefaultValues(incompleteAccountingPoliciesDataMock, service.customizationData);

      expect(returnedValue.changesInAccountingPolicies).toBe('some text here');
      expect(returnedValue.tangibleFixedAssets.plantAndMachinery.computerEquipment.categoryDescription).toBeNull();
    });

    it('should have tanglible fixed assets subsections generated', () => {
      accountingPoliciesResponseMock.status = 200;
      spyOn<any>(service, 'getAccountingPolicies').and.returnValue(of(accountingPoliciesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        const tangibleAssetsIndex = service.accountingPoliciesSection.children.findIndex(
          section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
        );
        expect(
          service.accountingPoliciesSection.children[tangibleAssetsIndex].children.findIndex(
            section => section.label === 'Plant and machinery etc.'
          )
        ).toBeGreaterThanOrEqual(0);
        expect(
          service.accountingPoliciesSection.children[tangibleAssetsIndex].children.findIndex(
            section => section.label === 'Land and buildings'
          )
        ).toBeGreaterThanOrEqual(0);
      });
    });

    it('should have plant and machinery subsections generated', () => {
      accountingPoliciesResponseMock.status = 200;
      service.reportData.validationData.validationIssues = [];

      spyOn<any>(service, 'getAccountingPolicies').and.returnValue(of(accountingPoliciesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();
      returnedValue.subscribe(result => {
        const tangibleAssetsIndex = result.children.findIndex(
          section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
        );
        const plantAndMachineryIndex = service.accountingPoliciesSection.children[
          tangibleAssetsIndex
        ].children.findIndex(section => section.label === 'Plant and machinery etc.');
        const plantAndMachineryIndexChildren =
          service.accountingPoliciesSection.children[tangibleAssetsIndex].children[plantAndMachineryIndex].children;

        expect(
          plantAndMachineryIndexChildren.findIndex(section => section.label === 'Improvements to property')
        ).toBeGreaterThanOrEqual(0);

        expect(
          plantAndMachineryIndexChildren.findIndex(section => section.label === 'Plant and machinery')
        ).toBeGreaterThanOrEqual(0);

        expect(
          plantAndMachineryIndexChildren.findIndex(section => section.label === 'Fixtures & fittings')
        ).toBeGreaterThanOrEqual(0);

        expect(
          plantAndMachineryIndexChildren.findIndex(section => section.label === 'Motor vehicles')
        ).toBeGreaterThanOrEqual(0);

        expect(
          plantAndMachineryIndexChildren.findIndex(section => section.label === 'Computer equipment')
        ).toBeGreaterThanOrEqual(0);
      });
    });

    it('should have land and buildings subsections generated', () => {
      accountingPoliciesResponseMock.status = 200;
      service.reportData.validationData.validationIssues = [];

      spyOn<any>(service, 'getAccountingPolicies').and.returnValue(of(accountingPoliciesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();
      returnedValue.subscribe(result => {
        const tangibleAssetsIndex = result.children.findIndex(
          section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
        );
        const landAndBuildingsIndex = service.accountingPoliciesSection.children[
          tangibleAssetsIndex
        ].children.findIndex(section => section.label === 'Land and buildings');
        const landAndBuildingsIndexChildren =
          service.accountingPoliciesSection.children[tangibleAssetsIndex].children[landAndBuildingsIndex].children;

        expect(
          landAndBuildingsIndexChildren.findIndex(section => section.label === 'Freehold property')
        ).toBeGreaterThanOrEqual(0);

        expect(
          landAndBuildingsIndexChildren.findIndex(section => section.label === 'Short leasehold property')
        ).toBeGreaterThanOrEqual(0);

        expect(
          landAndBuildingsIndexChildren.findIndex(section => section.label === 'Long leasehold property')
        ).toBeGreaterThanOrEqual(0);
      });
    });

    it('should not have a tangible assets section when there is a validation issue warning', () => {
      service.reportData.validationData = JSON.parse(JSON.stringify(MOCK_VALIDATION_DATA));
      service.reportData.validationData.validationIssues[0].errorCode = ValidationIssueEnum.TANGIBLE_ASSETS_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;

      accountingPoliciesResponseMock.status = 200;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(
          service.accountingPoliciesSection.children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
          )
        ).toBe(-1);
      });
    });

    it('should not have a tangible assets section when both subsections have validation issue error', () => {
      service.reportData.validationData = JSON.parse(JSON.stringify(MOCK_VALIDATION_DATA));
      service.reportData.validationData.validationIssues[0].errorCode = ValidationIssueEnum.PLANT_AND_MACHINERY_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;
      let validationLandAndBuildings = { ...service.reportData.validationData.validationIssues[0] };
      validationLandAndBuildings.errorCode = ValidationIssueEnum.LAND_AND_BUILDING_WARN;
      service.reportData.validationData.validationIssues.push(validationLandAndBuildings);

      accountingPoliciesResponseMock.status = 200;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(
          service.accountingPoliciesSection.children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
          )
        ).toBe(-1);
      });
    });

    it('should not have a land and building section when there is a validation issue warning', () => {
      service.reportData.validationData = JSON.parse(JSON.stringify(MOCK_VALIDATION_DATA));
      service.reportData.validationData.validationIssues[0].errorCode = ValidationIssueEnum.LAND_AND_BUILDING_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;

      accountingPoliciesResponseMock.status = 200;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        const tangibleIndex = service.accountingPoliciesSection.children.findIndex(
          section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
        );

        expect(tangibleIndex).toBeGreaterThan(0);

        expect(
          service.accountingPoliciesSection.children[tangibleIndex].children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.LAND_AND_BUILDING_WARN
          )
        ).toBe(-1);
      });
    });

    it('should not have a plant and machinery section when there is a validation issue warning', () => {
      service.reportData.validationData = JSON.parse(JSON.stringify(MOCK_VALIDATION_DATA));
      service.reportData.validationData.validationIssues[0].errorCode = ValidationIssueEnum.PLANT_AND_MACHINERY_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;

      accountingPoliciesResponseMock.status = 200;
      const getAvgSpy = spyOn<any>(service, 'getAccountingPolicies').and.returnValue(
        of(accountingPoliciesResponseMock)
      );
      const updateAvgFlagSpy = spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();
      returnedValue.subscribe(() => {
        const tangibleIndex = service.accountingPoliciesSection.children.findIndex(
          section => section.validationIssueCode === ValidationIssueEnum.TANGIBLE_ASSETS_WARN
        );

        expect(tangibleIndex).toBeGreaterThan(0);

        expect(
          service.accountingPoliciesSection.children[tangibleIndex].children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.PLANT_AND_MACHINERY_WARN
          )
        ).toBe(-1);
      });
    });

    it('should have intanglible assets subsections generated', () => {
      accountingPoliciesResponseMock.status = 200;
      service.reportData.validationData.validationIssues = [];
      spyOn<any>(service, 'getAccountingPolicies').and.returnValue(of(accountingPoliciesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        const intangibleAssetsIndex = service.accountingPoliciesSection.children.findIndex(
          section => section.validationIssueCode === ValidationIssueEnum.INTANGIBLE_ASSETS_WARN
        );
        expect(
          service.accountingPoliciesSection.children[intangibleAssetsIndex].children.findIndex(
            section => section.label === 'Goodwill'
          )
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children[intangibleAssetsIndex].children.findIndex(
            section => section.label === 'Patents & licences'
          )
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children[intangibleAssetsIndex].children.findIndex(
            section => section.label === 'Development costs'
          )
        ).toBeGreaterThanOrEqual(0);

        expect(
          service.accountingPoliciesSection.children[intangibleAssetsIndex].children.findIndex(
            section => section.label === 'Computer software'
          )
        ).toBeGreaterThanOrEqual(0);
      });
    });

    it('should not have an intangible assets section when there is specific validation issue warning', () => {
      service.reportData.validationData = JSON.parse(JSON.stringify(MOCK_VALIDATION_DATA));
      service.reportData.validationData.validationIssues[0].errorCode = ValidationIssueEnum.INTANGIBLE_ASSETS_WARN;
      service.reportData.validationData.validationIssues[0].target = ValidationIssueTarget.SectionValidation;

      accountingPoliciesResponseMock.status = 200;
      spyOn<any>(service, 'getAccountingPolicies').and.returnValue(of(accountingPoliciesResponseMock));
      spyOn<any>(service, 'updateReportNotesDataFlags').and.callThrough();
      const returnedValue = service.generateSubsectionsStructure();

      returnedValue.subscribe(() => {
        expect(
          service.accountingPoliciesSection.children.findIndex(
            section => section.validationIssueCode === ValidationIssueEnum.INTANGIBLE_ASSETS_WARN
          )
        ).toBe(-1);
      });
    });
  });

  describe('updateExemptionsValues', () => {
    it('should update the section hasData value and store the values', () => {
      service.updateCustomizationDataValues(accountingPoliciesDataMock.exemptionsFinancialStatements, MOCK_SECTION_REF);
      expect(MOCK_SECTION_REF.hasData).toBe(true);

      const exemptions = service.getCustomizationData(MOCK_SECTION_REF.formConfigKey);

      expect(exemptions.section).toBe(accountingPoliciesDataMock.exemptionsFinancialStatements.section);
      expect(exemptions.parentAddress).toBe(accountingPoliciesDataMock.exemptionsFinancialStatements.parentAddress);
      expect(exemptions.parentName).toBe(accountingPoliciesDataMock.exemptionsFinancialStatements.parentName);
    });

    it('should update the stored values and set hasData to false if there is no data', () => {
      service.isCreated = true;
      service.updateCustomizationDataValues({ section: null, parentName: null, parentAddress: null }, MOCK_SECTION_REF);
      expect(MOCK_SECTION_REF.hasData).toBe(false);
    });

    it('should update the stored values and trigger a error count update', () => {
      const updateErrorCountsSpy = spyOn(service, 'updateErrorCounts');
      const sectionMock = { ...MOCK_SECTION_REF };
      service.updateCustomizationDataValues(
        { section: null, parentName: null, parentAddress: null },
        sectionMock,
        true,
        false
      );
      expect(updateErrorCountsSpy).toHaveBeenCalledWith(sectionMock, true);
    });

    it('should update the stored values and trigger a warning count update', () => {
      const updateWarningCountsSpy = spyOn(service, 'updateWarningCounts');
      const sectionMock = { ...MOCK_SECTION_REF };
      service.updateCustomizationDataValues(
        { section: null, parentName: null, parentAddress: null },
        sectionMock,
        false,
        true
      );
      expect(updateWarningCountsSpy).toHaveBeenCalledWith(sectionMock, true);
    });

    it('should update the stored values and trigger a warning count update', () => {
      const sectionMock = { ...MOCK_SECTION_REF, skipCountUpdate: true };
      const EXPECTED_ERROR_COUNT = sectionMock.errorCount;
      const EXPECTED_WARN_COUNT = sectionMock.warningCount;
      service.updateCustomizationDataValues(
        { section: null, parentName: null, parentAddress: null },
        sectionMock,
        true,
        true
      );
      expect(sectionMock.warningCount).toBe(EXPECTED_WARN_COUNT);
      expect(sectionMock.errorCount).toBe(EXPECTED_ERROR_COUNT);
    });
  });

  describe('updateErrorCounts', () => {
    it('should increase the error counter when called with a true flag', () => {
      const sectionMock = { ...MOCK_SECTION_REF };
      service.updateErrorCounts(sectionMock, true);
      expect(sectionMock.errorCount).toBe(MOCK_SECTION_REF.errorCount + 1);
    });

    it('should decrease the error counter when called with a false flag', () => {
      const sectionMock = { ...MOCK_SECTION_REF };
      service.updateErrorCounts(sectionMock, false);
      expect(sectionMock.errorCount).toBe(MOCK_SECTION_REF.errorCount - 1);
    });
  });

  describe('updateWarningCounts', () => {
    it('should increase the warning counter when called with a true flag', () => {
      const sectionMock = { ...MOCK_SECTION_REF };
      service.updateWarningCounts(sectionMock, true);
      expect(sectionMock.warningCount).toBe(MOCK_SECTION_REF.warningCount + 1);
    });

    it('should decrease the error counter when called with a false flag', () => {
      const sectionMock = { ...MOCK_SECTION_REF };
      service.updateWarningCounts(sectionMock, false);
      expect(sectionMock.warningCount).toBe(MOCK_SECTION_REF.warningCount - 1);
    });
  });

  describe('generateBalanceSheetStructure', () => {
    it('should return the balance sheet section as a observable', () => {
      const result = service.generateBalanceSheetStructure();
      result.subscribe(section => {
        expect(section).not.toBeNull;
      });
    });

    it('should return the balance sheet section as a observable', () => {
      service.reportData.validationData.validationIssues.push({
        errorCode: ValidationIssueEnum.BALANCE_SHEET_WARN,
        description: 'mock desc',
        errorCategory: ValidationIssueCategory.Advisory,
        name: 'mock name',
        type: ValidationIssueType.Invalid,
        target: ValidationIssueTarget.SectionValidation
      });
      const result = service.generateBalanceSheetStructure();
      result.subscribe(section => {
        expect(section).toBeNull;
      });
    });
  });

  describe('initSubsectionsValidity', () => {
    it('should not trigger any validations when the section does not have a validation code', () => {
      const MOCK_SECTION = {
        ...MOCK_SECTION_REF,
        validationIssueCode: null
      };

      service.customizationSections = [MOCK_SECTION];
      service.initSubsectionsValidity(MOCK_VALIDATION_DATA);
      expect(MOCK_SECTION.errorCount).toBe(0);
    });

    it('should trigger an error count if Members Transactions has no data', () => {
      const MEMBERS_TRANSACTIONS_MOCK = {
        parent: null,
        children: null,
        isMandatory: true,
        hasData: false,
        formConfigKey: NotesFormTypeEnum.MEMBERS_TRANSACTIONS,
        label: 'mock section',
        errorCount: 0,
        warningCount: 0
      };

      const MOCK_SECTION = {
        ...MEMBERS_TRANSACTIONS_MOCK,
        validationIssueCode: null
      };

      service.customizationSections = [MOCK_SECTION];
      service.initSubsectionsValidity(MOCK_VALIDATION_DATA);
      expect(MOCK_SECTION.errorCount).toBe(1);
    });

    it('should not trigger any validations when the section validation code is not found in the validation issues', () => {
      const MOCK_SECTION = {
        ...MOCK_SECTION_REF,
        validationIssueCode: ValidationIssueEnum.PLANT_AND_MACHINERY_ERROR
      };

      service.customizationSections = [MOCK_SECTION];
      service.initSubsectionsValidity(MOCK_VALIDATION_DATA);
      expect(MOCK_SECTION.errorCount).toBe(0);
    });

    it('should check for assets basis errors when the section error code is found in the issue logs', () => {
      const MOCK_SECTION = {
        ...MOCK_SECTION_REF,
        formConfigKey: NotesFormTypeEnum.IMPROVEMENTS_PROPERTY,
        validationIssueCode: ValidationIssueEnum.IMPROVEMENTS_PROPERTY_ERROR
      };

      const MOCK_VALIDATION: ValidationData = {
        mandatoryValidationIssuesLogCount: 0,
        advisoryValidationIssuesLogCount: 0,
        validationIssues: [
          {
            errorCode: ValidationIssueEnum.IMPROVEMENTS_PROPERTY_ERROR,
            description: 'mock desc',
            errorCategory: ValidationIssueCategory.Mandatory,
            name: 'mock name',
            type: ValidationIssueType.Missing,
            target: ValidationIssueTarget.IssueLog
          }
        ]
      };
      let MOCK_VALID_ASSETS_BASIS: AssetsBasis = {
        categoryDescription: 'mock category',
        reducingBalanceBasis: 5,
        straightLineBasis: null,
        alternativeBasis: null
      };
      const MOCK_INVALID_ASSETS_BASIS: AssetsBasis = {
        categoryDescription: null,
        reducingBalanceBasis: null,
        straightLineBasis: null,
        alternativeBasis: null
      };

      service.customizationSections = [MOCK_SECTION];
      service.customizationData.tangibleFixedAssets.plantAndMachinery.improvementsToProperty = MOCK_VALID_ASSETS_BASIS;
      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(0);

      MOCK_VALID_ASSETS_BASIS.reducingBalanceBasis = null;
      MOCK_VALID_ASSETS_BASIS.straightLineBasis = 5;
      service.customizationData.tangibleFixedAssets.plantAndMachinery.improvementsToProperty = MOCK_VALID_ASSETS_BASIS;
      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(0);

      MOCK_VALID_ASSETS_BASIS.straightLineBasis = null;
      MOCK_VALID_ASSETS_BASIS.alternativeBasis = 'mock data';
      service.customizationData.tangibleFixedAssets.plantAndMachinery.improvementsToProperty = MOCK_VALID_ASSETS_BASIS;
      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(0);

      service.customizationData.tangibleFixedAssets.plantAndMachinery.improvementsToProperty =
        MOCK_INVALID_ASSETS_BASIS;
      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(1);

      service.customizationData.tangibleFixedAssets.landAndBuildings.shortLeaseholdProperty = MOCK_INVALID_ASSETS_BASIS;
      MOCK_VALIDATION.validationIssues[0].errorCode = ValidationIssueEnum.SHORT_LEASEHOLD_PROPERTY_ERROR;

      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(1);

      service.customizationData.tangibleFixedAssets.landAndBuildings.freeholdProperty = MOCK_INVALID_ASSETS_BASIS;
      MOCK_VALIDATION.validationIssues[0].errorCode = ValidationIssueEnum.FREEHOLD_PROPERTY_ERROR;

      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(1);

      service.customizationData.tangibleFixedAssets.landAndBuildings.longLeaseholdProperty = MOCK_INVALID_ASSETS_BASIS;
      MOCK_VALIDATION.validationIssues[0].errorCode = ValidationIssueEnum.LONG_LEASEHOLD_PROPERTY_ERROR;

      service.initSubsectionsValidity(MOCK_VALIDATION);
      expect(MOCK_SECTION.errorCount).toBe(1);
    });
  });
});
