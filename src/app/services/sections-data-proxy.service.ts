import { Injectable } from '@angular/core';
import { DataSourceServiceEnum } from '../models/report-sections/report-notes.model';
import { ReportSection } from '../models/report-sections/report-sections.model';
import { AccountingPoliciesService } from './accounting-policies.service';
import { ReportNotesService } from './report-notes.service';

@Injectable({
  providedIn: 'root'
})
export class SectionsDataProxyService {
  constructor(
    private reportNotesService: ReportNotesService,
    private accountingPoliciesService: AccountingPoliciesService
  ) {}

  getFormData(dataSourceService: number, formConfigKey: string) {
    switch (dataSourceService) {
      case DataSourceServiceEnum.NOTES:
        return this.reportNotesService.getReportNotesData(formConfigKey);
      case DataSourceServiceEnum.ACCOUNTING_POLICIES:
        return this.accountingPoliciesService.getCustomizationData(formConfigKey);
    }
  }

  getFormValidation(dataSourceService: number, formConfigKey: string) {
    switch (dataSourceService) {
      case DataSourceServiceEnum.NOTES:
        return this.reportNotesService.hasSectionValidation(formConfigKey);
      case DataSourceServiceEnum.ACCOUNTING_POLICIES:
        return false;
    }
  }

  updateServiceData(newValues, sectionRef: ReportSection, hasError?: boolean, hasWarning?: boolean) {
    switch (sectionRef.dataSourceService) {
      case DataSourceServiceEnum.NOTES:
        if (sectionRef.formConfigKey === 'membersLiabilityText') {
          return this.reportNotesService.updateReportNotesDataValues(
            { noteTitle: 'MembersLiabilityTitle', noteText: newValues },
            sectionRef,
            hasError,
            hasWarning
          );
        } else return this.reportNotesService.updateReportNotesDataValues(newValues, sectionRef, hasError, hasWarning);
      case DataSourceServiceEnum.ACCOUNTING_POLICIES:
        return this.accountingPoliciesService.updateCustomizationDataValues(
          newValues,
          sectionRef,
          hasError,
          hasWarning
        );
    }
  }
}
