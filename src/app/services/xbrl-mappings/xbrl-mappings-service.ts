import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class XbrlMappingsService {
  private readonly XBRL_MAPPING_API_ENDPOINT = `${environment.xbrl_mapping_api_uri}`;
  private readonly XBRL_MAPPING_API_KEY = { 'x-api-key': environment.xbrl_mapping_api_key };

  constructor(private readonly http: HttpClient) {}

  public getClientPeriodTaxonomyId(clientId: string, periodId: string, taxonomyCode: string): Observable<number> {
    return this.http
      .get<number>(
        `${this.XBRL_MAPPING_API_ENDPOINT}/client/${clientId}/period/${periodId}/taxonomycode/${taxonomyCode}/override`,
        {
          headers: this.XBRL_MAPPING_API_KEY,
          observe: 'response'
        }
      )
      .pipe(map(response => response.body));
  }
}
