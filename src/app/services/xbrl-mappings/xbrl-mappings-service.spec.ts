import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { XbrlMappingsService } from './xbrl-mappings-service';
import { environment } from 'src/environments/environment';

describe('XbrlMappingsService', () => {
  let service: XbrlMappingsService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [XbrlMappingsService]
    });
    service = TestBed.inject(XbrlMappingsService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch client period taxonomy id', () => {
    const clientId = '123';
    const periodId = '456';
    const taxonomyCode = '789';
    const mockResponse = 42;

    service.getClientPeriodTaxonomyId(clientId, periodId, taxonomyCode).subscribe(response => {
      expect(response).toBe(mockResponse);
    });

    const req = httpMock.expectOne(
      `${environment.xbrl_mapping_api_uri}/client/${clientId}/period/${periodId}/taxonomycode/${taxonomyCode}/override`
    );
    expect(req.request.method).toBe('GET');
    expect(req.request.headers.get('x-api-key')).toBe(environment.xbrl_mapping_api_key);
    req.flush(mockResponse);
  });
});
