import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { Breadcrumb } from '../models/breadcrumb.model';
import { ReportSection } from '../models/report-sections/report-sections.model';
import { ReportCustomizationService } from './report-customization.service';

@Injectable({
  providedIn: 'root'
})
export class ReportNavigationService implements OnDestroy {
  private currentNode: ReportSection;
  currentNavigationNode: BehaviorSubject<ReportSection> = new BehaviorSubject<ReportSection>(null);
  breadcrumbsNavigationsLinks: BehaviorSubject<Breadcrumb[]> = new BehaviorSubject<Breadcrumb[]>(null);
  $subscriptions: Subscription[] = [];

  constructor(private reportCustomizationService: ReportCustomizationService) {
    this.$subscriptions.push(
      this.reportCustomizationService.reportSectionsTree.subscribe(reportTypeNode => {
        const sectionsNode = reportTypeNode.children[0];
        this.updateNavigation(sectionsNode.children?.length ? sectionsNode : reportTypeNode);
      })
    );
  }

  updateNavigation(newNavigationNode: ReportSection): void {
    this.currentNode = newNavigationNode;
    this.currentNavigationNode.next(newNavigationNode);
    this.breadcrumbsNavigationsLinks.next(this.generateBreadcrumbs());
  }

  generateBreadcrumbs(breadcrumbsLength = 2) {
    let breadcrumbNode: ReportSection = { ...this.currentNode };
    let generatedList: Breadcrumb[] = [];

    while (breadcrumbNode !== null && generatedList.length < breadcrumbsLength) {
      generatedList.unshift({
        name: breadcrumbNode.label,
        reference: { ...breadcrumbNode }
      });
      breadcrumbNode = breadcrumbNode.parent;
    }
    return generatedList;
  }

  navigateToNode(newNode: ReportSection): void {
    this.updateNavigation(newNode);
  }

  removeSubscriptions() {
    this.$subscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.$subscriptions = [];
  }

  ngOnDestroy(): void {
    this.removeSubscriptions();
  }
}
