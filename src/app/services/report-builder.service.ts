import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  ReportDto,
  ReportingStandard,
  ReportingStandardVersion,
  ReportingStandards,
  ReportingTemplate
} from '../models/report.model';
import { availableReportTemplates } from '../utils/available-report-template-helper';

@Injectable({
  providedIn: 'root'
})
export class ReportBuilderService {
  private regex: RegExp = RegExp('(\\$\\$){1}[a-zA-Z0-9]*(\\$\\$){1}');

  constructor(private http: HttpClient) {}

  getReportingStandards(): Observable<ReportingStandard[]> {
    return this.http
      .get<ReportingTemplate[]>(`${environment.accountsproduction_builder_api_url}/report/availablereporttemplates`, {
        headers: {
          'x-api-key': environment.accountsproduction_builder_api_key
        }
      })
      .pipe(
        map(response => {
          return response.map((item: ReportDto) => {
            const correlationIdMatches = this.regex.exec(item.description);
            const display = item.description.includes('(Display)');
            const version = item.description.includes('Filleted')
              ? ReportingStandardVersion.Filleted
              : ReportingStandardVersion.Full;
            const reportingStandard = this.getReportingStandardFromDescription(item.description);

            let correlationId = correlationIdMatches?.[0];
            let report: ReportingStandard;
            if (correlationId !== undefined) {
              correlationId = correlationId.replace('$$', '').replace('$$', ''); // replace the markers before and after the correlationId
              report = availableReportTemplates.find(o => o.correlationId === correlationId);
            }

            if (!correlationId || !report?.correlationId) {
              const standard = {
                id: item.id,
                compatibleCompanyTypes: null,
                compatibleCompanySubTypes: null,
                name: item.name,
                reportType: version,
                display: display,
                description: item.description,
                reportingStandard: reportingStandard,
                correlationId: null,
                equivalentCorrelationId: null
              };
              return standard;
            }

            report.id = item.id;
            report.name = item.name;
            report.description = item.description;
            return report;
          });
        })
      );
  }

  private getReportingStandardFromDescription(description: string): ReportingStandards {
    let reportingStandard = ReportingStandards.FRS102;
    if (description.includes('FRS105')) reportingStandard = ReportingStandards.FRS105;
    if (description.includes('FRS102 1A')) reportingStandard = ReportingStandards.FRS102_1A;
    if (description.includes('Unincorporated')) reportingStandard = ReportingStandards.Unincorporated;
    if (description.includes('Charity') || description.includes('Charities'))
      reportingStandard = ReportingStandards.Charity;
    if (description.includes('IFRS')) reportingStandard = ReportingStandards.IFRS;
    return reportingStandard;
  }
}
