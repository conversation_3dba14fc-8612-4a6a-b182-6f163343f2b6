import { TestBed } from '@angular/core/testing';
import { BehaviorSubject, never, of } from 'rxjs';
import { Bookmark } from '../models/bookmark.model';
import { NotesFormTypeEnum } from '../models/report-sections/report-notes.model';
import { ReportSection, customizableReportSection } from '../models/report-sections/report-sections.model';
import { MOCK_GET_REPORTING_STANDARDS_RESPONSE, MOCK_REPORT_DATA } from '../models/report.mock';
import { AccountingPoliciesService } from './accounting-policies.service';
import { AccountsBuilderService } from './accounts-builder.service';
import { DirectorsReportService } from './directors-report.service';

import { ReportCustomizationService } from './report-customization.service';
import { ReportNotesService } from './report-notes.service';
import { ReportSectionsService } from './report-sections.service';
import {
  ReportData,
  ReportingStandardVersionName,
  ReportingStandards,
  ReportingTemplate
} from '../models/report.model';
import { ReportBuilderService } from './report-builder.service';

const MOCK_SUBSECTION_EXTENTION: ReportSection = {
  parent: null,
  label: 'custom node',
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  children: [
    {
      parent: null,
      label: 'custom subsection 1',
      children: null,
      isMandatory: false,
      hasData: true,
      errorCount: 0,
      warningCount: 0,
      formConfigKey: NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES
    },
    {
      parent: null,
      label: 'custom subsection 2',
      children: [
        {
          parent: null,
          label: 'custom subsection 3',
          children: null,
          isMandatory: false,
          hasData: true,
          errorCount: 0,
          warningCount: 0
        },
        {
          parent: null,
          label: 'custom subsection 4',
          children: null,
          isMandatory: false,
          hasData: true,
          errorCount: 0,
          warningCount: 0
        }
      ],
      isMandatory: false,
      hasData: true,
      errorCount: 0,
      warningCount: 0
    }
  ]
};

const MOCK_DIRECTORS_GENERATION: ReportSection = {
  parent: null,
  label: 'directors mock',
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  children: [
    {
      parent: null,
      label: 'custom subsection',
      children: null,
      isMandatory: false,
      hasData: true,
      errorCount: 0,
      warningCount: 0,
      formConfigKey: NotesFormTypeEnum.AVG_NUMBER_OF_EMPLOYEES
    }
  ]
};

const MOCK_RECEIVED_BOOKMARKS: Bookmark[] = [
  {
    Text: 'Cover',
    PageIndex: 1,
    Indices: ''
  },
  {
    Text: 'First bookmark',
    PageIndex: 2,
    Indices: ''
  },
  {
    Text: 'Second bookmark',
    PageIndex: 3,
    Indices: ''
  },
  {
    Text: 'FRS105 bookmark',
    PageIndex: 4,
    Indices: ''
  }
];

const MOCK_REPORT_TEMPLATES: ReportingTemplate[] = [
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e963',
    name: 'FRS105 - Filleted',
    description: '(display) this description $$LTD105SFC001$$',
    isPublic: true,
    type: 'FRS105',
    version: ReportingStandardVersionName.Filleted
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e962',
    name: 'FRS105 - Full',
    description: 'this description (Display) $$LTD105FUL001$$',
    isPublic: true,
    type: 'FRS105',
    version: ReportingStandardVersionName.Full
  },
  {
    id: '486021cf-d6bb-4d76-829f-4f8f3706e964',
    name: 'FRS105 - Balances sheet',
    description: 'FRS105 - random Filleted report one that is not in available template configuration',
    isPublic: false,
    type: 'FRS105',
    version: ReportingStandardVersionName.Filleted
  }
];

const reportNotesServiceMock = jasmine.createSpyObj('ReportNotesService', [
  'generateSubsectionsStructure',
  'initSubsectionsValidity'
]);

const directorsReportServiceMock = jasmine.createSpyObj('DirectorsReportService', ['generateSubsectionsStructure']);

const reportBuilderServiceMock = jasmine.createSpyObj('ReportBuilderService', ['getReportingStandards']);

const accountingPoliciesServiceMock = jasmine.createSpyObj('AccountingPoliciesService', [
  'generateSubsectionsStructure',
  'generateBalanceSheetStructure',
  'initSubsectionsValidity'
]);

const reportSectionsServiceMock = jasmine.createSpyObj('ReportSectionsService', ['updateBookmarkList']);

const accountsBuilderServiceMock = jasmine.createSpyObj('AccountsBuilderService', ['getReportStatus']);

describe('ReportCustomizationService', () => {
  let service: ReportCustomizationService;
  beforeEach(() => {
    reportNotesServiceMock.generateSubsectionsStructure.and.returnValue(of(MOCK_SUBSECTION_EXTENTION));
    directorsReportServiceMock.generateSubsectionsStructure.and.returnValue(of(MOCK_DIRECTORS_GENERATION));
    reportBuilderServiceMock.getReportingStandards.and.returnValue(of(MOCK_REPORT_TEMPLATES));
    accountingPoliciesServiceMock.generateSubsectionsStructure.and.returnValue(of(MOCK_SUBSECTION_EXTENTION));
    accountingPoliciesServiceMock.generateBalanceSheetStructure.and.returnValue(of(null));
    accountsBuilderServiceMock.reportData = new BehaviorSubject(MOCK_REPORT_DATA);
    reportSectionsServiceMock.bookmarkList = new BehaviorSubject(null);
    reportSectionsServiceMock.filterSections = (bookmarks: ReportSection[], _: ReportData) => {
      return bookmarks;
    };

    TestBed.configureTestingModule({
      providers: [
        {
          provide: ReportNotesService,
          useValue: reportNotesServiceMock
        },
        {
          provide: DirectorsReportService,
          useValue: directorsReportServiceMock
        },
        {
          provide: AccountingPoliciesService,
          useValue: accountingPoliciesServiceMock
        },
        {
          provide: AccountsBuilderService,
          useValue: accountsBuilderServiceMock
        },
        {
          provide: ReportSectionsService,
          useValue: reportSectionsServiceMock
        },
        {
          provide: ReportBuilderService,
          useValue: reportBuilderServiceMock
        }
      ]
    });
    service = TestBed.inject(ReportCustomizationService);
  });

  describe('generateReportSectionsRoot', () => {
    it('should generate the default sections tree', () => {
      const expectedRoot = {
        parent: null,
        label: 'Generator',
        isMandatory: true,
        formConfigKey: null,
        children: [],
        errorCount: 0,
        warningCount: 0
      };
      const generatedRoot = service.generateReportSectionsRoot();
      generatedRoot.children = [];
      expect(generatedRoot).toEqual(expectedRoot);
    });
  });

  describe('initSectionsTree', () => {
    it('should extend the default root with the FRS105 sections', () => {
      MOCK_REPORT_DATA.reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE.find(
        o => o.correlationId === 'LTD105SFC001'
      );

      MOCK_REPORT_DATA.reportingStandard.reportingStandard = ReportingStandards.FRS105;

      service.initSectionsTree(
        [...MOCK_RECEIVED_BOOKMARKS, { Text: customizableReportSection.label, PageIndex: 5, Indices: '' }],
        MOCK_REPORT_DATA
      );
      expect(reportNotesServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      service.reportSectionsTree.subscribe(sectionsTree => {
        const expectedSectionsList = sectionsTree.children[0].children;
        MOCK_RECEIVED_BOOKMARKS.forEach((expectedBookmark, index) => {
          expect(expectedSectionsList[index].label).toEqual(expectedBookmark.Text);
        });
      });
    });

    it('should extend the default root with the FRS102 sections', () => {
      MOCK_REPORT_DATA.reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE.find(
        o => o.correlationId === 'LTD102FUL001'
      );

      MOCK_REPORT_DATA.reportingStandard.reportingStandard = ReportingStandards.FRS102;

      service.initSectionsTree(
        [...MOCK_RECEIVED_BOOKMARKS, { Text: customizableReportSection.label, PageIndex: 5, Indices: '' }],
        MOCK_REPORT_DATA
      );

      service.reportSectionsTree.subscribe(sectionsTree => {
        const expectedSectionsList = sectionsTree.children[0].children;
        MOCK_RECEIVED_BOOKMARKS.forEach((expectedBookmark, index) => {
          expect(expectedSectionsList[index].label).toEqual(expectedBookmark.Text);
        });
      });
    });

    it('should reflect the bookmarks returned from DevExpress bookmarks endpoint', () => {
      MOCK_REPORT_DATA.reportingStandard = MOCK_GET_REPORTING_STANDARDS_RESPONSE.find(
        o => o.correlationId === 'LTD105SFC001'
      );
      MOCK_REPORT_DATA.reportingStandard.reportingStandard = ReportingStandards.FRS105;

      const bookmarks = [
        {
          Text: 'cover 29873298374294',
          PageIndex: 1,
          Indices: ''
        },
        {
          Text: 'section 12312312313',
          PageIndex: 2,
          Indices: ''
        },
        {
          Text: 'page 983475938475',
          PageIndex: 3,
          Indices: ''
        },
        {
          Text: customizableReportSection.label,
          PageIndex: 4,
          Indices: ''
        }
      ];

      service.initSectionsTree(bookmarks, MOCK_REPORT_DATA);
      expect(reportNotesServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      service.reportSectionsTree.subscribe(sectionsTree => {
        const expectedSectionsList = sectionsTree.children[0].children;
        bookmarks.forEach((expectedBookmark, index) => {
          expect(expectedSectionsList[index].label).toEqual(expectedBookmark.Text);
        });
      });
    });

    it('should extend the default root with the FRS 102 1A sections', () => {
      const MOCK_FRS102_1A_DATA = { ...MOCK_REPORT_DATA };
      MOCK_FRS102_1A_DATA.reportingStandard = { ...MOCK_REPORT_DATA.reportingStandard };
      MOCK_FRS102_1A_DATA.reportingStandard.reportingStandard = ReportingStandards.FRS102_1A;
      service.initSectionsTree(MOCK_RECEIVED_BOOKMARKS, MOCK_FRS102_1A_DATA);
      expect(reportNotesServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      expect(directorsReportServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      expect(accountingPoliciesServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      expect(service.currentSectionNode.children.length).toBe(4);
    });

    it('should remove the FRS 102 1A duplications when there are matching labels', () => {
      let MOCK_MATCHING_LABEL = { ...MOCK_DIRECTORS_GENERATION };
      MOCK_MATCHING_LABEL.label = MOCK_RECEIVED_BOOKMARKS[0].Text;
      MOCK_MATCHING_LABEL.alias = [MOCK_RECEIVED_BOOKMARKS[0].Text];

      directorsReportServiceMock.generateSubsectionsStructure.and.returnValue(of(MOCK_MATCHING_LABEL));

      const MOCK_FRS102_1A_DATA = { ...MOCK_REPORT_DATA };
      MOCK_FRS102_1A_DATA.reportingStandard = { ...MOCK_REPORT_DATA.reportingStandard };
      MOCK_FRS102_1A_DATA.reportingStandard.reportingStandard = ReportingStandards.FRS102_1A;

      service.initSectionsTree(MOCK_RECEIVED_BOOKMARKS, MOCK_FRS102_1A_DATA);

      expect(reportNotesServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      expect(directorsReportServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      expect(accountingPoliciesServiceMock.generateSubsectionsStructure).toHaveBeenCalled();
      expect(service.currentSectionNode.children.length).toBe(4);
    });
  });

  it('should unsubscribe from all subscriptions', () => {
    const subscription1 = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    const subscription2 = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    service.$subscriptions = [subscription1, subscription2];

    service.removeSubscriptions();

    expect(subscription1.unsubscribe).toHaveBeenCalled();
    expect(subscription2.unsubscribe).toHaveBeenCalled();
    expect(service.$subscriptions.length).toBe(0);
  });

  it('should not throw an error if there are no subscriptions', () => {
    service.$subscriptions = [];

    expect(() => service.removeSubscriptions()).not.toThrow();
  });

  describe('ngOnDestroy', () => {
    it('should remove the existing subscriptions', () => {
      const removeSubscriptionsSpy = spyOn(service, 'removeSubscriptions');
      service.ngOnDestroy();
      expect(removeSubscriptionsSpy).toHaveBeenCalled();
    });
  });

  describe('hasMatchingLabels', () => {
    it('should return true when the labels are matching', () => {
      const firstLabel = 'Test Label ';
      const secondLabel = ' tEST label';
      expect(service.hasMatchingLabels(firstLabel, secondLabel)).toBe(true);
    });

    it('should return true when the labels are matching', () => {
      const firstLabel = 'Test Label ';
      const secondLabel = ' tEST label 2';
      expect(service.hasMatchingLabels(firstLabel, secondLabel)).toBe(false);
    });

    describe('replaceOrAddSection', () => {
      it('should replace an existing section if found in the sections mapping', () => {
        const existingSection: ReportSection = {
          parent: null,
          label: 'Existing Section',
          isMandatory: true,
          errorCount: 0,
          warningCount: 0,
          children: []
        };
        const sectionsMapping: ReportSection[] = [existingSection];
        const section: ReportSection = {
          parent: null,
          label: 'Existing Section',
          isMandatory: true,
          errorCount: 1,
          warningCount: 2,
          children: [
            {
              parent: null,
              label: 'Child Section',
              isMandatory: false,
              errorCount: 0,
              warningCount: 0,
              children: []
            }
          ]
        };

        const result = service.replaceOrAddSection(section, sectionsMapping);

        expect(result).toEqual(sectionsMapping);
        expect(result[0].children).toEqual(section.children);
        expect(result[0].warningCount).toEqual(section.warningCount);
        expect(result[0].errorCount).toEqual(section.errorCount);
        expect(result[0].children[0].parent).toEqual(result[0]);
      });

      it('should add a new section if not found in the sections mapping', () => {
        const existingSection: ReportSection = {
          parent: null,
          label: 'Existing Section',
          isMandatory: true,
          errorCount: 0,
          warningCount: 0,
          children: []
        };
        const sectionsMapping: ReportSection[] = [existingSection];
        const section: ReportSection = {
          parent: null,
          label: 'New Section',
          isMandatory: true,
          errorCount: 1,
          warningCount: 2,
          children: [
            {
              parent: null,
              label: 'Child Section',
              isMandatory: false,
              errorCount: 0,
              warningCount: 0,
              children: []
            }
          ]
        };

        const result = service.replaceOrAddSection(section, sectionsMapping);

        expect(result[result.length - 1].children[0].parent).toEqual(null);
      });

      it('should return the sections mapping if the section is null', () => {
        const sectionsMapping: ReportSection[] = [];

        const result = service.replaceOrAddSection(null, sectionsMapping);

        expect(result).toEqual(sectionsMapping);
      });
    });
  });
});
