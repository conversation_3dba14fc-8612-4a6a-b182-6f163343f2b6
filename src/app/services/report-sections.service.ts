import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { Bookmark } from '../models/bookmark.model';

@Injectable({
  providedIn: 'root'
})
export class ReportSectionsService {
  bookmarkList = new BehaviorSubject<Bookmark[]>(null);
  navigateToBookmark = new Subject<Bookmark>();

  updateBookmarkList(bookmarkList: Bookmark[]): void {
    this.bookmarkList.next(bookmarkList);
  }

  updateBookmarkNavigation(selectedBookmark: Bookmark): void {
    this.navigateToBookmark.next(selectedBookmark);
  }
}
