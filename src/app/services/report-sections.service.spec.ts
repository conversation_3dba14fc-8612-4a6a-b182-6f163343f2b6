import { TestBed } from '@angular/core/testing';
import { MOCK_BOOKMARK_DATA } from '../models/bookmark.mock';
import { Bookmark } from '../models/bookmark.model';

import { ReportSectionsService } from './report-sections.service';
import { ReportSection } from '../models/report-sections/report-sections.model';
import { EntitySetup } from '../models/report.model';

const ACCOUNTANTS_REPORT_SECTION: ReportSection = {
  label: 'Accountants’ report',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  alias: ['Accountants’ report']
};

const ACCOUNTANT_S_REPORT_SECTION: ReportSection = {
  label: 'Accountant’s report',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  alias: ['Accountant’s report']
};

const A_RANDOM_REPORT_SECTION: ReportSection = {
  label: 'A different section',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  alias: ['A different section']
};

const A_DIFFERENT_RANDOM_REPORT_SECTION: ReportSection = {
  label: 'A random other section',
  parent: null,
  children: null,
  isMandatory: true,
  errorCount: 0,
  warningCount: 0,
  alias: ['A random other section']
};

const ENTITYSETUP_ACCOUNTANTS: EntitySetup = {
  entitySize: 'test',
  terminology: 'test',
  reportingStandard: 'test',
  independentReviewType: 'Accountants',
  choiceOfStatement: 'test',
  dormantStatus: 'test',
  tradingStatus: 'test'
};

const ENTITYSETUP_NONE: EntitySetup = {
  entitySize: 'test',
  terminology: 'test',
  reportingStandard: 'test',
  independentReviewType: 'None',
  choiceOfStatement: 'test',
  dormantStatus: 'test',
  tradingStatus: 'test'
};

describe('ReportSectionsService', () => {
  let service: ReportSectionsService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ReportSectionsService);
  });

  it('should be created', () => {
    const service: ReportSectionsService = TestBed.get(ReportSectionsService);
    expect(service).toBeTruthy();
  });

  describe('bookmarkListUpdate', () => {
    it('should add the new bookmark list to the behaviour subject', () => {
      const bookmarkListSpy = spyOn(service.bookmarkList, 'next');
      service.updateBookmarkList(MOCK_BOOKMARK_DATA);
      expect(bookmarkListSpy).toHaveBeenCalledWith(MOCK_BOOKMARK_DATA);
    });
  });

  describe('updateBookmarkNavigation', () => {
    it('should update the bookmark navigation subject', () => {
      const bookmarkNavigateSpy = spyOn(service.navigateToBookmark, 'next');
      service.updateBookmarkNavigation(MOCK_BOOKMARK_DATA[0]);
      expect(bookmarkNavigateSpy).toHaveBeenCalledWith(MOCK_BOOKMARK_DATA[0]);
    });
  });
});
