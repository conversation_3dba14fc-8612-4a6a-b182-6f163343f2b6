import { enableProdMode, NgZone } from '@angular/core';

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { Router } from '@angular/router';

import { singleSpaAngular, getSingleSpaExtraProviders } from 'single-spa-angular';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { singleSpaPropsSubject } from './single-spa/single-spa-props';
import { LifeCycleFn } from 'single-spa';

if (environment.production) {
  enableProdMode();
}

// The DOM element ID to which the component is bound to and rendered inside of
const elementId = 'accounts-builder-import';

let hostElementNode: HTMLElement;
let domObserver: MutationObserver;

let waitCount = 0;
const waitInterval = 100;
const waitTimeout = 5000;

const waitForElement = (elementId, callback): Promise<any> => {
  const expectedElement = document.getElementById(elementId);
  if (!expectedElement) {
    if (waitCount * waitInterval > waitTimeout) {
      // debugging purposes
      console.log('timeout');
      return;
    }
    window.setTimeout(() => {
      waitCount++;
      waitForElement(elementId, callback);
    }, waitInterval);
    return;
  }
  callback();
};

const lifecycles = singleSpaAngular({
  bootstrapFunction: singleSpaProps => {
    singleSpaPropsSubject.next(singleSpaProps);
    return platformBrowserDynamic(getSingleSpaExtraProviders()).bootstrapModule(AppModule);
  },
  template: '<elements-accounts-builder-component-v01-pkg-root />',
  Router,
  NgZone,
  domElementGetter: () => {
    let element = document.getElementById(elementId);
    if (!element) {
      /**
       * On unmount, it searches for this element, and fails to find it, since it's removed by the AP tabs.
       * We create it again here so that it doesn't trigger an error.
       */
      element = document.createElement('div');
      element.id = elementId;
    }
    return element;
  }
});

const findElement = (nodes: NodeList): HTMLElement => {
  let result: HTMLElement;
  nodes?.forEach(node => {
    if (node instanceof HTMLElement) {
      const element = node.querySelector(`#${elementId}`);
      if (element instanceof HTMLElement) {
        result = element;
      }
    }
  });
  return result;
};

const checkAddedNodes = async (addedNodes, props) => {
  const added = findElement(addedNodes);
  if (added && added !== hostElementNode) {
    // If already mounted on another node then unmount first
    if (hostElementNode) {
      try {
        await (lifecycles.unmount as LifeCycleFn<any>)(props);
      } catch (err) {
        console.error(err);
      }
    }
    hostElementNode = added;
    await (lifecycles.mount as LifeCycleFn<any>)(props);
  }
};

// Used to mount the component inside of SingleSpa
const mount = async props => {
  /* Delay the component mounting until the DOM element is available 
     otherwise the component will throw an error as it won't have
     an element to mount into */
  waitCount = 0;
  waitForElement(elementId, async () => {
    const expectedElement = document.getElementById(elementId);
    if (expectedElement) {
      hostElementNode = expectedElement;
      await (lifecycles.mount as LifeCycleFn<any>)(props);
    }
    domObserver = new MutationObserver(async (mutations: MutationRecord[]) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          await checkAddedNodes(mutation.addedNodes, props);
        }
      }
    });
    domObserver.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
  });
};

export const bootstrap = lifecycles.bootstrap;
export { mount };
export const unmount = lifecycles.unmount;
