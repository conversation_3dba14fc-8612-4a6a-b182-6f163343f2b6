@import '../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

$scrollbar-color: rgba($black, 0.2);

@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: $scrollbar-color;
    border-radius: 20px;
  }

  /* Firefox styles */
  scrollbar-width: thin;
  scrollbar-color: $scrollbar-color transparent;
}

@mixin report-sidebar-form-border {
  margin-left: calc(#{$p-0} + #{$p-4});
  padding: 0 $p-4 0 20px;
  position: relative;
  padding-top: $p-3;
  padding-bottom: $p-3;
  &:before {
    content: '';
    position: absolute;
    height: 100%;
    border-left: 1px solid $grey-light-6;
    left: 0;
    top: 0;
  }
  .helper-text {
    position: absolute;
    top: calc(100% - 20px);
    margin: 0;
    &--error {
      color: $iris-red;
      font-size: 14px;
    }
    &--warning {
      color: $yellow-dark-1;
      font-size: 14px;
    }
  }
  &:first-child {
    padding-top: 0;
  }
  &:last-child {
    padding-bottom: 0;
    .helper-text {
      position: initial;
    }
    &::before {
      height: calc(50% + #{$p-3}/ 2);
    }
  }
}

@mixin report-sidebar-form-border--checkbox {
  margin-left: 24px;
  position: relative;
  &:before {
    content: '';
    position: absolute;
    height: 100%;
    border-left: 1px solid $grey-light-6;
    left: 12px;
    top: 0;
    z-index: 1;
  }
  &:not(:first-child) {
    &:before {
      height: calc(100% + 48px);
      top: -48px;
    }
  }
}
