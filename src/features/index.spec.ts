import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { features, setFeatures } from './index';

describe('features', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should set features value when callig setFeatures', () => {
    const featuresData = {
      getState: (featureKey, defaultValue = true) => {
        return of(defaultValue);
      }
    };
    setFeatures(featuresData);
    expect(features).toEqual(featuresData);
  });

  it('should return feature status when callig getState', (done: DoneFn) => {
    features.getState('test').subscribe(c => {
      expect(c).toEqual(true);
      done();
    });
  });

  it('should return feature status when callig getState with parameter', (done: DoneFn) => {
    features.getState('test', false).subscribe(c => {
      expect(c).toEqual(false);
      done();
    });
  });

  it('should set features value when callig setFeatures with null', () => {
    expect(features).toEqual(setFeatures(null));
  });

  it('should set features value when callig setFeatures with null and get status data', (done: DoneFn) => {
    setFeatures(null)
      .getState('test')
      .subscribe(c => {
        expect(c).toEqual(true);
        done();
      });
  });

  it('should set features value when callig setFeatures with null and get status data with parameter', (done: DoneFn) => {
    setFeatures(null)
      .getState('test', false)
      .subscribe(c => {
        expect(c).toEqual(false);
        done();
      });
  });
});
