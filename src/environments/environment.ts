export let environment = {
  production: false,
  isLocalEnv: true,
  ias_hub_api_uri: 'https://api.elements-development.iris.co.uk/ias/v1',
  ias_hub_api_key: 'w47qAGIH3d27Bu2S01prc4jTojkR4Oy38CIPvMVR',
  accounts_production_periods_api_uri:
    'https://api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1',
  accounts_production_periods_api_key: 'lJMr99pxfp4cjy06OEPfW2jeOSD00cwo4rXRco77',
  accounts_production_accounts_builder_api_uri:
    'https://api.elements-development.iris.co.uk/accountsproduction-accountsbuilder/v1',
  accounts_production_accounts_builder_api_key: 'J8jEJ9XTbU6sxHC1EDZ2G7X9yuEvHTm07yFpVASm',
  accountsproduction_builder_api_url:
    'https://api.elements-development.iris.co.uk/accountsproduction-builder/v1/reports',
  accountsproduction_builder_api_key: 'QxobMVjVtd7AqXi4kzN6J3g5drTmtPvYHNkxeNb0',
  accountsproduction_api_url: 'https://api.elements-development.iris.co.uk/accountsproduction/v1',
  accountsproduction_api_key: 'aicZeDcgat4zItluICcgta9Ov0UPztkr29KITSn4',
  accountsproduction_accountperiod_partnerprofitshare_api_url:
    'https://api.elements-development.iris.co.uk/accountsproduction-accountperiod-partnerprofitshare/v1',
  accountsproduction_accountperiod_partnerprofitshare_api_key: 'OLLTSlHejCSRemcHguTS5NJ21ABmU1maNx42OM14',
  accountsproduction_trialbalance_api_url:
    'https://api.elements-development.iris.co.uk/accountsproduction-trialbalance/v1',
  accountsproduction_trialbalance_api_key: 'TX4GwF5Avja5QDW4AQd1c5DHqu6BTVrR7RiEogGG',
  xbrl_mapping_api_uri: 'https://api.elements-development.iris.co.uk/xbrl-mapping/v1',
  xbrl_mapping_api_key: 'mw3HkMDeSJ7ILo6NKVQaB7mRnqkgNZr96bJbGjJT'
};

export const setEnvironment = (value: any) => (environment = value || environment);
