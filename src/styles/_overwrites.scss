@import '../../node_modules/@iris/platform-ui-kit/dist/collection/styles/_variables';

iris-tabs-mat.tabs .tabs-list {
  margin: 0;
  padding: 0;
  .carousel {
    margin: 0;
    button {
      margin-left: 0;
      margin-right: $p-3;
    }
  }
}

.report-type {
  iris-select,
  iris-radio {
    label {
      font-weight: 400;
      .required {
        font-size: $p-2;
      }
    }
  }
}

.section-notes-textarea,
.customization-section-form,
.balance-sheet-section-form,
.assets-basis-form,
.section-rename {
  .iris-textarea {
    label {
      font-weight: 400;
      color: $grey-light-6;
    }
    textarea {
      width: 100%;
    }
  }
  .iris-input {
    input {
      width: 100%;
    }
  }
}

.notes-titled-paragraph {
  .iris-input,
  .iris-textarea {
    label {
      font-weight: 400;
      color: $iris-grey-dark;
    }
    input,
    textarea {
      width: 100%;
    }
  }
}

.section-notes-textarea,
.notes-titled-paragraph,
.item-section,
.customization-section-form,
.balance-sheet-section-form {
  .iris-textarea textarea.medium {
    height: 74px;
  }
  .iris-radio .iris-radio__label {
    font-weight: normal;
    &::before {
      margin-right: 10px;
    }
  }
  .iris-radio input:not(:checked) + label {
    &::before {
      border-color: $grey-light-6;
    }
  }
}

.item-section {
  .iris-input,
  .iris-textarea {
    label {
      font-weight: 400 !important;
      color: $iris-grey-dark !important;
    }
    input,
    textarea {
      width: 100%;
    }
  }
}

.average-employees-container,
.assets-basis-form {
  iris-numeric-input {
    input {
      text-align: left;
      width: 100%;
    }
  }

  iris-input {
    input {
      text-align: left;
      width: 100%;
    }
  }

  iris-textarea {
    .iris-textarea {
      textarea {
        width: 375px;
        &.medium {
          height: 100px;
        }
      }
    }
  }
}

.current-value-input {
  iris-numeric-input {
    input {
      text-align: left;
      width: 100%;
    }
  }
}

.analysis-cost-input {
  iris-numeric-input {
    input {
      text-align: right !important;
      width: 100%;
    }
  }
}

.balance-section {
  .iris-input,
  .iris-textarea {
    label {
      font-weight: bold !important;
      color: $iris-grey-dark !important;
    }
    input,
    textarea {
      width: 100%;
    }
  }
}

.report-title-container {
  iris-icon {
    border-radius: 50%;
    border: 2px solid $black;
  }
}

.accounts-builder-container {
  iris-radio {
    .iris-radio__label {
      &:before,
      &:after {
        z-index: 2;
      }
      &:after {
        z-index: 3;
      }
    }
  }
  .issue-details .mat-expansion-panel-body {
    border-top: 0;
    padding-left: $p-3;
  }
}

.accounts-builder-container .sidebar-container {
  .iris-checkbox label {
    font-weight: normal;
    align-items: initial;
  }
  .iris-checkbox i {
    position: absolute;
    top: 0%;
    transform: none;
  }
  .iris-input label {
    color: $iris-grey-dark;
  }
  iris-textarea {
    label {
      color: $iris-grey-dark;
      .required {
        font-size: inherit;
        font-weight: normal;
      }
    }
  }
}

.accounts-builder-container .balance-sheet-section-form {
  .iris-radio__label {
    align-items: initial;
    &:before,
    &:after {
      flex-shrink: 0;
      z-index: 2;
    }
    &:after {
      top: 12px;
      z-index: 3;
    }
  }
}

.xbrl-editor-toggle-container {
  iris-togglebutton {
    input:disabled + .slider:before {
      height: 14px !important;
      width: 14px !important;
    }
  }
}
